/*
 * @Description: 资料管理模块相关api
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-08 11:44:11
 * @LastEditTime: 2023-05-10 14:53:53
 */
import request from "@/utils/request"

// 查询资料列表
export function listMeans(params) {
  return request({
    url: "/course/manage/list",
    method: "get",
    params
  })
}

// 查询资料详细
export function getMeans(paperId) {
  return request({
    url: "/course/manage/" + paperId,
    method: "get"
  })
}

// 各个文件类型数量统计
export function getManageStatisticInfo(params?) {
  return request({
    url: "/course/manage/statistic",
    method: "get",
    params
  })
}

// 获取热门搜索关键词
export function getPopularSearchWords(params?) {
  return request({
    url: "/course/manage/hotWords",
    method: "get",
    params
  })
}

// 点击查看/点击下载埋点接口
export function editRelateNumber(data?) {
  return request({
    url: "/course/manage/updateNumber",
    method: "post",
    data
  })
}
