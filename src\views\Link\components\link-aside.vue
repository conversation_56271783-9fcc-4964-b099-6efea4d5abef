<script setup lang="ts">
//
</script>

<template>
  <div class="base-link-aside">
    <div class="user-manage">
      <h4>联系我们</h4>
      <div class="links">
        <RouterLink to="/link?name=联系我们&id=1043000">关于我们</RouterLink>
        <RouterLink to="/link/entCulture?name=企业文化&id=1043000"
          >企业文化</RouterLink
        >
        <RouterLink to="/link/honor?name=创新荣誉&id=1043000"
          >创新荣誉</RouterLink
        >
        <RouterLink to="/link/leaveMsg?name=在线留言&id=1043000"
          >在线留言</RouterLink
        >
        <RouterLink to="/link/recruit?name=人才招聘&id=1043000"
          >人才招聘</RouterLink
        >
        <RouterLink to="/link/way?name=联系方式&id=1043000"
          >联系方式</RouterLink
        >
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.base-link-aside {
  width: 220px;
  margin-right: 20px;
  border-radius: 2px;
  .user-manage {
    background-color: #fff;
    h4 {
      font-size: 18px;
      font-weight: 400;
      padding: 20px 52px 5px;
      border-top: 1px solid #f6f6f6;
    }

    .links {
      padding: 0 52px 10px;
    }

    a {
      display: block;
      line-height: 1;
      padding: 15px 0;
      font-size: 14px;
      color: #666;
      position: relative;

      &.router-link-exact-active {
        color: #666;
        &:before {
          display: block;
        }
      }

      &:hover {
        color: #000;
      }
      &.active {
        color: @baseColor;

        &:before {
          display: block;
        }
      }

      &:before {
        content: "";
        display: none;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        position: absolute;
        top: 19px;
        left: -16px;
        background-color: @baseColor;
      }
    }
  }
}
</style>
