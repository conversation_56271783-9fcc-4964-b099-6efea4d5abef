/*
 * @Description: 课程学习进度相关API
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-13 17:05:30
 * @LastEditTime: 2023-08-02 10:18:26
 */
import request from "@/utils/request"

// 获取课程学习进度详细信息
export function getGradeInfo(gradeId) {
  return request({
    url: "/course/grade/" + gradeId,
    method: "get"
  })
}

// 更新课程的学习进度
export function editCourseGrade(data) {
  return request({
    url: "/course/grade/edit",
    method: "post",
    data: data
  })
}
