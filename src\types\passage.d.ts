/*
 * @Description:章节
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-16 09:55:03
 * @LastEditTime: 2024-04-08 10:24:16
 */
export type passageType = {
  vodUrl: string
  coursePassageId: number
  coursePassageName: string
  mediaProgress: number
  videoFileList: videoFileListType[]
}

export type videoFileListType = {
  fileName: string
  fileUrl: string
  vodUrl: string
  passageId: number
  coursePassageName?: string
  mediaProgress?: number
  mediaDuration?: number
}
