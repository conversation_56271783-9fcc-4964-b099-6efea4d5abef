<!--
 * @Description: 进行中
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-09-06 09:19:05
 * @LastEditTime: 2024-02-20 09:20:13
-->
<script setup lang="ts">
  import { Search } from "@element-plus/icons-vue"
  import { fetchTaskMyList } from "@/api/trainingTask"
  import type { taskInfoType } from "@/types/task"

  const { proxy } = getCurrentInstance()!
  const { training_task_status } = proxy!.useDict("training_task_status")

  let router = useRouter()

  let dataTotal = ref(0)
  let pageNum = ref(1)
  let pageSize = ref(6)
  let searchValue = ref("")
  let taskList = ref<taskInfoType[]>([])

  async function getTaskInfo() {
    let queryData = {
      pageSize: pageSize.value,
      pageNum: pageNum.value,
      taskName: searchValue.value,
      taskStatus: "2",
      ...extraQueryData.value
    }

    const { rows, total } = await fetchTaskMyList(queryData)
    dataTotal.value = total || 0
    taskList.value = rows
  }

  let extraQueryData = ref({})
  const arrowSortChange = value => {
    extraQueryData.value = { ...value }
    getTaskInfo()
  }

  const goPage = item => {
    router.push({
      path: "/study/project/detail",
      query: { ...item }
    })
  }

  getTaskInfo()
</script>

<template>
  <div class="project">
    <div class="project-select">
      <div class="project-sort">
        <BaseArrowSort
          :data-list="[
            { name: 'start_time', label: '开始时间' },
            { name: 'end_time', label: '结束时间' },
            { name: 'rate_learning', label: '完成进度' }
          ]"
          @sort="arrowSortChange"
        ></BaseArrowSort>
      </div>
      <div class="project-search"
        ><el-input
          v-model="searchValue"
          class="w-50 m-2 project-search-input"
          :prefix-icon="Search"
          @keyup.enter="getTaskInfo"
          clearable
        ></el-input
      ></div>
    </div>

    <div v-if="taskList?.length > 0" class="project-content">
      <div class="project-content-list" v-for="item in taskList" :key="item.taskId">
        <img :src="item.coverPhoto" alt="" />
        <div class="project-content-list-content">
          <div class="title">{{ item.taskName }}</div>
          <div class="info">
            <div>培训时间：{{ item.startTime }} - {{ item.endTime }}</div>
            <el-progress :percentage="Math.floor(item.rateLearning * 100) || 0" />
            <el-button @click="goPage(item)">查看详情</el-button>
          </div>
          <div class="status">
            <span>
              状态：
              <dict-tag
                v-if="item.taskStatus"
                class="dict-tag"
                :options="training_task_status"
                :value="item.taskStatus"
              />
              <span v-else>未开始</span>
            </span>
          </div>
        </div>
      </div>
    </div>
    <el-empty v-else description="暂无数据" :image-size="200"></el-empty>
    <BasePagination
      class="pagination"
      v-if="taskList?.length > 0"
      :total="dataTotal"
      v-model:pageNum="pageNum"
      v-model:pageSize="pageSize"
      @pagination="getTaskInfo"
    />
  </div>
</template>

<style scoped lang="less">
  .project {
    .project-select {
      background-color: white;
      height: 62px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #dcdfe6;
      .project-sort {
        display: flex;
        align-items: center;
        > div {
          margin-right: 30px;
          display: flex;
          align-items: center;
          padding: 3px 4px;
          cursor: pointer;
        }
      }

      .project-search {
        width: 300px;
        margin-right: 50px;
      }
    }

    .project-content {
      padding: 0 20px;
      background-color: #ffffff;
      .project-content-list {
        border-bottom: 1px solid #e5e5e4;
        display: flex;
        // margin-top: 20px;
        padding-top: 20px;
        padding-bottom: 20px;
        align-items: center;
        > img {
          height: 118px;
          width: 20%;
        }

        .project-content-list-content {
          width: 75%;
          margin-left: 20px;
          .title {
            text-overflow: -o-ellipsis-lastline;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            line-clamp: 1;
            -webkit-box-orient: vertical;
            word-wrap: break-word;
            margin-bottom: 20px;
            font-weight: bold;
          }

          .info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;

            .lastStudyTime {
              width: 210px;
            }
            .el-progress {
              width: 200px;
            }
          }

          .status {
            .dict-tag {
              display: inline-block;
            }
          }
        }
      }
    }
  }

  :deep(.el-divider--horizontal) {
    margin: 0;
  }

  :deep(.el-input__wrapper) {
    border-radius: 30px;
  }
</style>
