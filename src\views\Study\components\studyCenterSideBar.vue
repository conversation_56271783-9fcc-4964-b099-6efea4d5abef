<!--
 * @Description: 学习中心-侧边栏
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-15 13:51:13
 * @LastEditTime: 2024-11-29 11:06:00
-->
<template>
  <div class="flex flex-col bg-#eef5ff h-full py-20px border border-[#f5f5f5] relative">
    <div class="menu" v-for="(menu, title) in menuGroup" :key="title">
      <div class="px-30px leading-48px text-24px font-bold">{{ title }}</div>
      <router-link
        v-for="item in menu"
        :key="item.title"
        class="px-30px flex items-center text-16px leading-48px hover:font-bold"
        :class="[
          'hover:[&_.item-icon]:text-skyblueColor',
          'active:bg-skyblueColor active:[&_.item-icon]:text-white active:[&_.item-text]:text-white'
        ]"
        :to="item.path"
        active-class="bg-skyblueColor [&_.item-icon]:text-white [&_.item-text]:text-white"
      >
        <el-icon class="text-22px mr-8px text-[#cdcdcd] item-icon">
          <component :is="item.iconName" />
        </el-icon>
        <span class="text-[#666] item-text">{{ item.title }}</span>
      </router-link>

      <el-divider v-if="showDivider(title as any)" />
    </div>
  </div>
</template>

<script setup lang="ts">
  import useTenantStore from "@/store/modules/tenant"

  interface MenuItem {
    title: string
    path: string
    iconName: string
  }

  interface MenuGroup {
    [key: string]: MenuItem[]
  }

  const MENU_CONFIG: MenuGroup = {
    我的学习: [
      { title: "学习计划", path: "/study/todo", iconName: "Reading" },
      { title: "我的课程", path: "/study/myCourse", iconName: "SetUp" },
      { title: "我的考试", path: "/study/exam", iconName: "Notebook" }
    ],
    任务中心: [
      { title: "进行中", path: "/study/onGoingTask?status=ongoing", iconName: "VideoPlay" },
      { title: "未开始", path: "/study/noStartTask?status=nostart", iconName: "VideoPause" },
      { title: "全部任务", path: "/study/myTask?status=all", iconName: "Monitor" },
      { title: "我的问卷", path: "/study/questionnaire", iconName: "ChatLineSquare" }
    ],
    我的成长: [
      { title: "我的资料", path: "/study/resource", iconName: "Postcard" },
      { title: "我的收藏", path: "/study/favor", iconName: "Star" },
      { title: "我的轨迹", path: "/study/locus", iconName: "Location" },
      { title: "我的荣誉", path: "/study/honor", iconName: "Trophy" },
      { title: "学习总览表", path: "/study/needTask", iconName: "DataLine" }
    ],
    我的发布: [{ title: "公益宣传片", path: "/study/trailer", iconName: "Postcard" }],
    个人主页: [{ title: "个人信息", path: "/study/myInfo", iconName: "Postcard" }]
  } as const

  const { domainName } = storeToRefs(useTenantStore())
  const menuGroup = ref<MenuGroup>(MENU_CONFIG)

  // 计算属性判断是否显示分割线
  const showDivider = (title: string) => {
    return Object.keys(menuGroup.value).indexOf(title) !== Object.keys(menuGroup.value).length - 1
  }

  onMounted(() => {
    if (domainName.value === "taibao") {
      // 使用数组方法移除考试选项
      menuGroup.value["我的学习"] = menuGroup.value["我的学习"].filter((_, index) => index !== 1)
    }
  })
</script>
