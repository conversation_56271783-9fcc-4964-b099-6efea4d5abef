/*
 * @Description: 上传相关接口
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-02-22 15:20:10
 * @LastEditTime: 2024-02-22 15:22:25
 */
import request from "@/utils/request"

// 手动上传formData文件
export function fileUpload(data) {
  return request({
    url: "/file/upload",
    method: "post",
    data,
    headers: {
      "Content-Type": "multipart/form-data; boundary=------WebKitFormBoundaryKcDXkXo0exIaYuxv"
    }
  })
}
