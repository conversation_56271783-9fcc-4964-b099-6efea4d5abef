<!--
 * @Description: 新闻页面
 * @Author: <PERSON><PERSON>
 * @LastEditors: sunyunwu
 * @Date: 2023-04-17 08:49:10
 * @LastEditTime: 2023-09-12 14:45:38
-->
<template>
  <div class="container">
    <div class="header">
      <img src="https://img.traingo.cn/data/pic/news/scrollphoto/news-pic.jpg" alt="" />
    </div>
    <section style="width: 60%; margin: 0 auto; padding: 30px 0 60px 0; min-height: 500px">
      <ul class="news-type">
        <li
          v-for="(item, index) in catalogueTableData"
          :class="{ active: activeIndex === index }"
          @click="setActive(index)"
        >
          {{ item.catalogueName }}</li
        >
      </ul>
      <ul v-if="newsList.length > 0" class="news-list">
        <li v-for="item in newsList" class="news-item">
          <span></span><span @click="toNews(item.newsId)">{{ item.newsContent }}</span
          ><span>{{ item.createTime }}</span>
        </li>
      </ul>
      <el-empty v-else description="暂无数据" :image-size="200"></el-empty>
    </section>
  </div>
</template>
<script lang="ts" setup>
  import { ref, reactive } from "vue"
  import { catalogueType } from "@/constants"
  import { catalogueList } from "@/api/system/catalogue"
  import type { CatalogueList } from "@/types"
  import { list } from "@/api/develops/news"
  import router from "@/router"
  let activeIndex = ref<number>(0)

  const queryParam = reactive({
    catalogueId: 0,
    pageSize: 100
  })
  const catalogueTableData = ref<CatalogueList>([])
  let newsList = ref<any>([])
  const setActive = index => {
    activeIndex.value = index
    queryParam.catalogueId = catalogueTableData.value[index]["catalogueId"]
    fetchData()
  }
  const toNews = newsId => {
    router.push({
      path: "/news/detail",
      query: {
        id: newsId
      }
    })
  }
  const getCatalogueList = () => {
    catalogueList({ catalogueType: catalogueType.NEWS_CATALOGUE }).then(response => {
      catalogueTableData.value = response.rows
      setActive(0)
    })
  }
  const fetchData = async (value?) => {
    const { rows } = await list(queryParam)
    newsList.value = rows
  }
  getCatalogueList()
</script>
<style lang="less" scoped>
  .header > img {
    width: 100%;
  }
  .news-list {
    padding: 10px 0;
  }
  .news-type {
    display: flex;
    white-space: nowrap;
    justify-content: flex-start;
    overflow: hidden;

    font-weight: bold;
    margin-left: 20px;
    border-bottom: 2px solid #e3e3e3;
    > li {
      line-height: 40px;
      padding: 0 10px;
      cursor: pointer;
      margin-right: 10px;
      font-size: 14px;
    }
  }
  .active {
    font-size: 18px !important;
  }
  .news-item {
    padding: 0 10px 0 30px;
    font-size: 14px;
    margin: 6px 0;
    position: relative;
    > span:first-child {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 8px;
      background: #f58335;
      position: absolute;
      top: 8px;
      left: 34px;
    }
    > span:nth-child(2) {
      width: 80%;
      overflow: hidden;
      text-overflow: ellipsis;
      padding-left: 22px;
      display: inline-block;
      line-height: 24px;
      white-space: nowrap;
      cursor: pointer;
      font-size:14px;
    }
    > span:nth-child(3) {
      float: right;
      line-height: 24px;
      font-size: 13px;
      opacity: 0.6;
    }
  }
</style>
