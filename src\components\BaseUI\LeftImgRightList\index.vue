<script setup lang="ts">
import type { LeftImgRightText } from "@/types";
import type { PropType } from "vue";
import { ref, reactive, watch } from "vue";
import type { NewsListInfo } from "@/types";

defineProps({
  dataSource: {
    type: Object as PropType<LeftImgRightText>,
    default: () => ({}),
  },
});

// 对象结构确定，可以用 reactive 定义响应式对象
const query = reactive({
  orderState: 0,
  page: 1,
  pageSize: 5,
});

const newsListInfo = ref<NewsListInfo>();
const loadData = async () => {};

// tabs切换时更新 orderState 参数
const changeTabs = (index: number) => {
  query.orderState = index;
};

watch(
  query,
  () => {
    loadData();
  },
  // 立刻执行
  { immediate: true }
);
</script>
<template>
  <!--左侧广告显示-->
  <div class="ad-info">
    <div class="media">
      <div class="ad-image">
        <img
          src="http://demo.hyxdsoft.com/img/templateImg/web1001/core-1.png"
        />
      </div>
      <!-- 信息区 -->
      <div class="spec">
        <!-- 主要信息 -->
        <p class="g-name">
          <RouterLink to="/news/detail?name=详情&id=32323"
            >污水处理设备行业新闻（演示内容）</RouterLink
          >

          <RouterLink to="/news/detail?name=详情&id=32323">企业文化</RouterLink>
        </p>
        <p class="g-desc">
          液体比重天平厂家解析液体密度测量的外在因素：1.温度：当有机化合物液体的温度上升或下降时，由于热胀冷缩的关系...
        </p>
        <p class="g-content">
          <span>2013-9-9</span>
          <span>查看详情 </span>
        </p>
      </div>
    </div>
    <div class="spec">
      <div class="news-list">
        <div class="news-item" v-for="item in newsListInfo" :key="item.id">
          <div class="body">
            <div class="column products">
              <ul>
                <li>
                  <a class="image" href="javascript:;">
                    <img :src="item.img" alt="" />
                  </a>
                  <div class="info">
                    <p class="name ellipsis-2">
                      <RouterLink :to="'/news/detail?id=' + item.id">
                        {{ item.title }}</RouterLink
                      >
                    </p>
                    <p class="attr ellipsis-2">
                      {{ item.intro }}
                    </p>
                    <p class="attr-date ellipsis-2">{{ item.createTime }}</p>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.ad-info {
  min-height: 600px;
  background: #fff;
  display: flex;
  .media {
    width: 555px;
    height: 400px;
    padding: 30px 50px;
    .ad-image {
      width: 480px;
      height: 400px;
    }
  }
}

// 信息区
.spec {
  .g-name {
    padding-top: 20px;
    font-size: 22px;
  }
  .g-desc {
    color: #999;
    line-height: 25px;
    margin-top: 10px;
  }
  .g-content {
    margin-top: 10px;
    display: flex;
    justify-content: space-between;
    span {
      &::before {
        font-size: 14px;
      }
      &:first-child {
        color: @redColor;
        margin-right: 10px;
        font-size: 16px;
      }
      &:last-child {
        text-align: right;
        color: #999;
        font-size: 16px;
      }
    }
  }
}
.news-list {
  background-color: #fff;
  padding-top: 30px;
}

.news-item {
  margin-bottom: 20px;
  border: 3px solid #f5f5f5;
  .body {
    display: flex;
    align-items: stretch;
    .column {
      border-left: 1px solid #f5f5f5;
      text-align: center;
      padding: 20px;
      > p {
        padding-top: 10px;
      }
      &:first-child {
        border-left: none;
      }
      &.products {
        flex: 1;
        padding: 0;
        align-self: center;
        ul {
          li {
            border-bottom: 1px solid #f5f5f5;
            padding: 10px;
            display: flex;
            &:last-child {
              border-bottom: none;
            }
            .image {
              width: 160px;
              height: 90px;
              border: 1px solid #f5f5f5;
            }
            .info {
              width: 500px;
              text-align: left;
              padding: 0 10px;
              line-height: 20px;
              p {
                margin-bottom: 5px;
                &.name {
                  height: 50px;
                  line-height: 25px;
                  font-weight: bold;
                  font-size: 16px;
                }
                &.attr {
                  color: #999;
                  font-size: 12px;
                  span {
                    margin-right: 5px;
                  }
                }
                &.attr-date {
                  color: #999;
                  font-size: 12px;
                  text-align: right;
                  span {
                    margin-right: 5px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
