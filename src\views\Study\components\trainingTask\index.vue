<!--
 * @Description: 培训任务页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-11-29 17:15:04
 * @LastEditTime: 2024-12-06 17:23:38
-->
<script setup lang="ts">
  import { Search, ArrowRight } from "@element-plus/icons-vue"
  import { fetchTaskMyList } from "@/api/trainingTask"
  import type { taskInfoType } from "@/types/task"

  const { proxy } = getCurrentInstance()!
  const { training_task_status } = proxy!.useDict("training_task_status")
  const router = useRouter()
  const route = useRoute()

  // 状态映射
  const STATUS_MAP = {
    ongoing: "2",
    nostart: "1",
    all: undefined
  }

  const dataTotal = ref(0)
  const pageNum = ref(1)
  const pageSize = ref(8)
  const searchValue = ref("")
  const taskList = ref<taskInfoType[]>([])

  // 从路由参数获取当前状态
  const currentStatus = computed(() => {
    return (route.query.status as string) || "all"
  })

  async function getTaskInfo() {
    const queryData = {
      pageSize: pageSize.value,
      pageNum: pageNum.value,
      taskName: searchValue.value,
      taskStatus: STATUS_MAP[currentStatus.value],
      ...extraQueryData.value
    }

    const { rows, total } = await fetchTaskMyList(queryData)
    dataTotal.value = total || 0
    taskList.value = rows
  }

  const extraQueryData = ref({})
  const arrowSortChange = value => {
    pageNum.value = 1
    extraQueryData.value = { ...value }
    getTaskInfo()
  }

  const handleSearch = () => {
    pageNum.value = 1
    getTaskInfo()
  }

  const goPage = item => {
    router.push({
      path: "/study/project/detail",
      query: { ...item }
    })
  }

  watch(
    () => route.query.status,
    () => {
      pageNum.value = 1
      getTaskInfo()
    }
  )

  onMounted(() => {
    getTaskInfo()
  })
</script>

<template>
  <div class="bg-white">
    <div class="flex justify-between items-center p-4 border-b-2px border-gray-200 border-b-dashed">
      <BaseArrowSort
        :data-list="[
          { name: 'start_time', label: '开始时间' },
          { name: 'end_time', label: '结束时间' },
          { name: 'rate_learning', label: '完成进度' }
        ]"
        @sort="arrowSortChange"
      />
      <el-input
        v-model="searchValue"
        class="w-300px rounded-full"
        :prefix-icon="Search"
        @keyup.enter="handleSearch"
        @clear="handleSearch"
        clearable
      />
    </div>

    <div v-if="taskList?.length > 0" class="p-5">
      <div
        v-for="item in taskList"
        :key="item.taskId"
        class="flex items-center p-5 border-b border-gray-200"
      >
        <!-- 左侧图片容器 -->
        <div class="w-300px flex-shrink-0 relative">
          <div class="pb-[56.25%] relative overflow-hidden">
            <img
              :src="item.coverPhoto"
              alt=""
              class="absolute inset-0 w-full h-full object-cover object-center"
              loading="lazy"
            />
          </div>
        </div>

        <!-- 右侧内容容器 -->
        <div class="flex justify-between items-stretch ml-5 flex-1 h-[169px]">
          <div class="flex flex-col justify-between flex-1">
            <div class="font-bold text-20px">{{ item.taskName }}</div>
            <div class="text-16px text-gray-600">
              培训时间：{{ item.startTime }} - {{ item.endTime }}
            </div>
            <div class="text-16px font-bold flex items-center">
              状态：
              <el-tag
                size="large"
                class="text-14px ml-2"
                :type="
                  item.taskStatus === '2' ? 'warning' : item.taskStatus === '3' ? 'success' : ''
                "
              >
                <dict-tag
                  v-if="item.taskStatus"
                  :options="training_task_status"
                  :value="item.taskStatus"
                />
                <span v-else>未开始</span>
              </el-tag>
            </div>
          </div>

          <div class="flex items-center gap-8 flex-1">
            <div class="max-w-350px w-300px">
              <el-progress
                :percentage="Math.floor(item.rateLearning * 100) || 0"
                :stroke-width="20"
                class="scale-110"
                color="#ef91bc"
              />
            </div>

            <el-button
              :type="item.taskStatus === '3' ? 'default' : 'primary'"
              @click="goPage(item)"
              class="w-150px h-40px text-16px ml-auto"
            >
              {{ item.taskStatus === "3" ? "查看详情" : "去学习" }}
              <el-icon class="ml-2"><ArrowRight /></el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <el-empty v-else description="暂无数据" :image-size="200" />

    <BasePagination
      v-if="taskList.length > 0"
      class="mt-4"
      :total="dataTotal"
      v-model:pageNum="pageNum"
      v-model:pageSize="pageSize"
      @pagination="getTaskInfo"
    />
  </div>
</template>

<style lang="less" scoped>
  :deep(.el-progress-bar__inner) {
    background-color: #ef91bc !important;
  }
</style>
