/*
 * @Description: 
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-11-18 16:03:27
 * @LastEditTime: 2024-11-18 16:45:56
 */
// 课程完成状态
export enum CourseStatus {
  ALL = "",
  RUNNING = "1",
  COMPLETED = "2",
  UNCOMPLETED = "3"
}

// 使用 Record 类型并按照固定顺序定义映射
export const CourseStatusMap: Record<CourseStatus, string> = {
  [CourseStatus.ALL]: "全部",
  [CourseStatus.RUNNING]: "进行中",
  [CourseStatus.COMPLETED]: "已完成",
  [CourseStatus.UNCOMPLETED]: "未完成"
}

// 课程类型
export enum CourseType {
  ALL = "",
  REQUIRED = "0",
  OPTIONAL = "1"
}

// 使用 Record 类型并按照固定顺序定义映射
export const CourseTypeMap: Record<CourseType, string> = {
  [CourseType.ALL]: "全部",
  [CourseType.REQUIRED]: "必修",
  [CourseType.OPTIONAL]: "选修"
}

// 课程类型对应的样式
export const CourseTypeStyle: Record<string, string> = {
  [CourseType.REQUIRED]: "bg-red-500",
  [CourseType.OPTIONAL]: "bg-green-500"
}

// 添加固定顺序的数组来控制显示顺序
export const CourseStatusOrder = [
  CourseStatus.ALL,
  CourseStatus.COMPLETED,
  CourseStatus.UNCOMPLETED
]

export const CourseTypeOrder = [CourseType.ALL, CourseType.REQUIRED, CourseType.OPTIONAL]
