<!--
 * @Description: 我的收藏页面
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-31 09:23:04
 * @LastEditTime: 2024-11-29 11:06:15
-->
<script setup lang="ts">
  import { fetchCourseList } from "@/api/course"
  import { Search, ArrowRight } from "@element-plus/icons-vue"
  import { ref } from "vue"
  import { editFavoriteOrNot } from "@/api/course"
  import { ElMessage } from "element-plus"
  import type { classType } from "@/types"

  const searchValue = ref("")
  const favorList = ref<classType[]>([])
  const pageNum = ref(1)
  const pageSize = ref(8)
  const dataTotal = ref<number | undefined>(0)

  const fetchfavorData = async (value?) => {
    const queryData = {
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      favoriteOrNot: true,
      courseName: searchValue.value,
      ...value,
      fromType: "learning"
    }

    const { rows, total } = await fetchCourseList(queryData)
    favorList.value = rows
    dataTotal.value = total
  }

  const cancelfavor = async item => {
    const queryData = {
      courseId: item.courseId,
      favoriteOrNot: false
    }
    await editFavoriteOrNot(queryData)
    ElMessage({
      message: "取消收藏成功！",
      type: "success"
    })
    fetchfavorData()
  }

  const handleFilterChange = (value?) => {
    pageNum.value = 1
    fetchfavorData(value)
  }

  onMounted(() => {
    fetchfavorData()
  })
</script>

<template>
  <div class="favor">
    <div class="favor-search">
      <div class="favor-search-left">
        <BaseArrowSort
          :data-list="[{ name: 'course_name', label: '按课程名' }]"
          @sort="handleFilterChange"
        ></BaseArrowSort>
      </div>
      <el-input
        v-model="searchValue"
        class="favor-search-right"
        :prefix-icon="Search"
        @keyup.enter="handleFilterChange"
        @clear="handleFilterChange"
        clearable
        size="large"
      ></el-input>
    </div>
    <div v-if="favorList.length > 0" class="favor-content">
      <div class="favor-content-list" v-for="item in favorList">
        <div class="cover-wrapper">
          <img :src="item.courseImage" alt="" class="cover-image" />
        </div>
        <div class="favor-content-list-content">
          <router-link
            class="title text-24px font-bold"
            :to="'/course/detail?id=' + item.courseId"
            target="_blank"
          >
            {{ item.courseName }}
          </router-link>
          <div class="info">
            <el-button type="primary" size="large" @click="cancelfavor(item)">
              取消收藏
              <el-icon class="ml-2"><ArrowRight /></el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <el-empty v-else description="暂无数据" :image-size="200"></el-empty>
    <BasePagination
      class="pagination"
      v-if="favorList.length > 0"
      :total="dataTotal!"
      v-model:pageNum="pageNum"
      v-model:pageSize="pageSize"
      @pagination="fetchfavorData"
    />
  </div>
</template>

<style lang="less" scoped>
  .favor {
    .favor-search {
      background-color: white;
      padding: 16px 24px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #dcdfe6;

      .favor-search-right {
        width: 300px;
      }
    }

    .favor-content {
      padding: 20px;
      background-color: #ffffff;

      .favor-content-list {
        border-bottom: 1px solid #e5e5e4;
        display: flex;
        padding: 24px 0;
        align-items: center;

        .cover-wrapper {
          width: 320px;
          height: 180px;
          overflow: hidden;
          border-radius: 8px;

          .cover-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .favor-content-list-content {
          flex: 1;
          margin-left: 24px;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .title {
            color: #303133;
            text-decoration: none;
            &:hover {
              color: @warmOrange;
            }
          }

          .info {
            .el-button {
              min-width: 120px;
            }
          }
        }
      }
    }
  }

  :deep(.el-input__wrapper) {
    border-radius: 30px;
  }
</style>
