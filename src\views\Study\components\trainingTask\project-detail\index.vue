<!--
 * @Description: 培训任务详情页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-12-02 09:55:37
 * @LastEditTime: 2025-01-09 17:16:37
-->

<script setup lang="ts">
  import { getTaskById, getTrainingTrack } from "@/api/trainingTask"
  import {
    type taskInfoType,
    TaskType,
    TaskStatus,
    TaskStatusConfig,
    CompletionStatusConfig
  } from "@/types/task"

  const activeName = ref("first")
  const queryParams = ref({
    pageNum: 1,
    pageSize: 999
  })

  const route = useRoute()
  const router = useRouter()
  const taskList = ref<taskInfoType[]>([])

  const { taskId, coverPhoto, taskName, startTime, endTime, taskStatus, rateLearning } =
    route.query as any
  const taskMap = ref({})
  const taskInfo = ref<any>({})
  const getTaskInfo = async () => {
    const res = await getTrainingTrack(taskId)
    taskInfo.value.rateLearning = res.data.rateLearning || rateLearning
    taskInfo.value.taskStatus = res.data.completionStatus || taskStatus
  }

  const currentStudyItem = ref<any>({})
  const getTaskList = async () => {
    const { msg, rows } = await getTaskById(taskId, queryParams.value)
    currentStudyItem.value = msg ? JSON.parse(msg) : {}
    taskList.value = rows

    taskList.value.forEach(item => {
      if (item.courseId) {
        taskMap.value[TaskType.COURSE + item.courseId] = item
      } else if (item.examId) {
        taskMap.value[TaskType.EXAM + item.examId] = item
      } else if (item.questionnaireId) {
        taskMap.value[TaskType.QUESTIONNAIRE + item.questionnaireId] = item
      }

      if (item.preconditionLinkList?.length > 0) {
        item.mixedPrecondition =
          item.preconditionLinkList[0].preconditionType + item.preconditionLinkList[0].preconditions
      } else {
        item.mixedPrecondition = ""
      }
    })
  }

  // 计算任务行数据
  const taskRows: any = computed(() => {
    const rows: any = []
    const itemsPerRow = 4
    let currentRow: any = []

    taskList.value.forEach((item, index) => {
      currentRow.push(item)
      if (currentRow.length === itemsPerRow || index === taskList.value.length - 1) {
        rows.push([...currentRow])
        currentRow = []
      }
    })

    return rows
  })

  // 判断是否为终点行
  const isEndRow = (rowIndex: number, currentRow: any[]) => {
    if (rowIndex === taskRows.value.length - 1) return true
    if (currentRow.length < 4) return true
    return false
  }

  // 获取垂直连接线高度
  const getVerticalLineHeight = (rowIndex: number) => {
    const ROW_HEIGHT = 120
    const VERTICAL_SPACING = 80
    const totalHeight = VERTICAL_SPACING + ROW_HEIGHT
    return `${totalHeight}px`
  }

  // 获取行项目
  const getRowItems = (row: any[], rowIndex: number) => {
    return rowIndex % 2 === 0 ? row : [...row].reverse()
  }

  // 获取节点位置
  const getNodeGridPosition = (index: number, totalNodes: number) => {
    const totalColumns = 12
    const columnsPerNode = Math.floor(totalColumns / totalNodes)
    const startColumn = index * columnsPerNode + 1

    if (totalNodes === 1) return "6 / span 2"
    if (totalNodes === 2) return index === 0 ? "4 / span 2" : "8 / span 2"
    if (totalNodes === 3) return `${1 + index * 4} / span 2`
    return `${startColumn} / span ${columnsPerNode}`
  }

  // 获取网格列模板
  const getGridColumns = (nodeCount: number) => "repeat(12, 1fr)"

  // 获取箭头位置
  const getArrowPositions = (nodeCount: number) => {
    if (nodeCount <= 1) return []
    if (nodeCount === 2) return [33]
    if (nodeCount === 3) return [25, 58]

    const arr = Array.from({ length: nodeCount }, (_, i) => ((i + 1) * 100) / (nodeCount + 1))
    return arr
  }

  // 获取行容器样式
  const getRowContainerStyle = (rowIndex: number) => ({
    marginTop: rowIndex === 0 ? "0" : "80px",
    marginBottom: taskRows.value.length > 1 ? "80px" : "0"
  })

  // 获取任务状态样式
  const getTaskStatusStyle = (status: TaskStatus) => {
    return TaskStatusConfig[status]?.type || "info"
  }

  // 修改跳转逻辑
  const jumpTo = (item?: any) => {
    let realItem = item || currentStudyItem.value

    switch (realItem.fieldType) {
      case TaskType.COURSE:
        const routeData = router.resolve({
          path: "/course/detail",
          query: { id: realItem.fieldId, taskId }
        })
        window.open(routeData.href, "_blank")
        break
      case TaskType.EXAM:
        router.push({
          path: "/prepare",
          query: {
            baseId: realItem.fieldId,
            arrangeId: realItem.examArrangeId,
            taskId
          }
        })
        break
      case TaskType.QUESTIONNAIRE:
        router.push({
          path: "/study/questionnaire/doquestionnaire",
          query: {
            questionnaireId: realItem.questionnaireId,
            questionnaireIssuedId: realItem.questionnaireIssuedId
          }
        })
        break
    }
  }

  // 修改获取考试状态的工具函数
  const getExamStatus = (item: taskInfoType) => {
    if (!item.completionStatus) return { duration: "未完成", type: "info" }

    return {
      type: item.duration === "通过" ? "success" : "danger",
      score: item.result,
      duration: item.duration
    }
  }

  // 获取节点边框颜色
  const getNodeBorderColor = (item: taskInfoType) => {
    const colorMap = {
      [TaskType.COURSE]: "border-orange-500", // 课程-橙色
      [TaskType.EXAM]: "border-green-500", // 考试-绿色
      [TaskType.QUESTIONNAIRE]: "border-gray-500" // 问卷-灰色
    }
    return colorMap[item.fieldType] || "border-gray-500"
  }

  // 获取状态框样式
  const getStatusBoxClass = (item: taskInfoType) => {
    switch (item.fieldType) {
      case TaskType.COURSE:
        return "border border-orange-500 border-solid text-orange-500"
      case TaskType.EXAM:
        return "border border-green-500 border-solid text-green-500"
      // case TaskType.QUESTIONNAIRE:
      //   return "border border-gray-500 border-solid text-gray-500"
      default:
        return "border border-gray-500 border-solid text-gray-500"
    }
  }

  onMounted(() => {
    getTaskInfo()
    getTaskList()
  })
</script>

<template>
  <div class="container pt-20px pb-30px">
    <!-- 头部信息 -->
    <div class="flex bg-white p-20px">
      <img :src="coverPhoto" alt="" class="w-2/5" />
      <div class="flex flex-col ml-40px w-3/5">
        <div class="text-24px font-bold mb-60px">{{ taskName }}</div>
        <div class="flex mb-60px">
          <div class="text-[#9f9f9f]">起止时间：</div>
          <div class="text-orange-500">{{ startTime }} - {{ endTime }}</div>
        </div>
        <div class="flex bg-[#f3f3f3] p-30px mr-50px my-30px items-center">
          <div class="flex flex-col">
            <div class="mb-30px flex items-center">
              <span>状态：</span>
              <el-tag
                size="large"
                class="text-14px"
                :type="getTaskStatusStyle(taskInfo.taskStatus)"
              >
                {{ TaskStatusConfig[taskInfo.taskStatus]?.text || "未开始" }}
              </el-tag>
            </div>
            <div class="flex">
              进度：
              <el-progress
                :percentage="Math.floor(taskInfo.rateLearning * 100) || 0"
                :stroke-width="12"
              />
            </div>
          </div>
          <div class="ml-20px">
            <el-button
              v-if="taskInfo.completionStatus !== TaskStatus.COMPLETED"
              type="primary"
              class="min-w-120px text-white border-none transition-transform"
              @click="jumpTo(null)"
            >
              去学习
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 任务流程图容器 -->
    <div class="mt-30px bg-white p-[10px_20px_30px_20px]">
      <el-tabs v-model="activeName">
        <el-tab-pane label="学习安排" name="first">
          <div class="relative mx-auto pt-5 pb-20">
            <div class="relative">
              <template v-for="(row, rowIndex) in taskRows" :key="rowIndex">
                <div class="relative my-20" :style="getRowContainerStyle(rowIndex)">
                  <div
                    :class="[
                      'relative flex items-center h-[120px]',
                      rowIndex % 2 === 0 ? 'flex-row' : 'flex-row-reverse'
                    ]"
                  >
                    <!-- 开始节点 -->
                    <template v-if="rowIndex === 0">
                      <div class="absolute left-30px top-1/2 -translate-y-1/2">
                        <BaseSvgIcon
                          icon-class="start-node.svg"
                          class="text-orange-500"
                          size="50px"
                          color="#ff9800"
                        />
                      </div>
                    </template>

                    <!-- 任务节点列表 -->
                    <div class="flex-1 relative">
                      <!-- 水平虚线和箭头 -->
                      <div
                        class="absolute top-1/2 -translate-y-1/2 left-0 right-0 h-12 flex items-center"
                      >
                        <div class="w-full mx-20 relative">
                          <!-- 水平虚线 -->
                          <div class="absolute top-1/2 left-0 right-0 h-[2px] dashed-line"></div>

                          <!-- 箭头 -->
                          <template v-for="(_, index) in row.length" :key="index">
                            <div
                              class="arrow-horizontal"
                              :class="rowIndex % 2 === 0 ? 'arrow-right' : 'arrow-left'"
                              :style="{
                                [rowIndex % 2 === 0 ? 'left' : 'right']: `${
                                  getArrowPositions(row.length)[index]
                                }%`
                              }"
                            ></div>
                          </template>
                        </div>
                      </div>

                      <!-- 节点列表 -->
                      <div
                        class="grid gap-4 px-20 min-h-[160px]"
                        :style="{
                          'grid-template-columns': getGridColumns(row.length)
                        }"
                      >
                        <template
                          v-for="(item, colIndex) in getRowItems(row, rowIndex)"
                          :key="item.id"
                        >
                          <div
                            class="flex flex-col items-center justify-start h-full pt-6 transform translate-y-5.5"
                            :style="{
                              'grid-column': getNodeGridPosition(colIndex, row.length)
                            }"
                          >
                            <!-- 任务名称 -->
                            <div
                              class="text-14px font-bold mb-4 w-full text-center truncate"
                              :title="item.optName"
                            >
                              {{
                                item.courseName ||
                                item.examName ||
                                item.questionnaireName ||
                                item.manageName
                              }}
                            </div>

                            <!-- 节点圆圈 -->
                            <div
                              class="w-4 h-4 rounded-full border-2 bg-white relative border-solid"
                              :class="getNodeBorderColor(item)"
                            ></div>

                            <!-- 状态框 -->
                            <div
                              class="mt-4 px-4 py-2 rounded text-12px min-w-[120px]"
                              :class="getStatusBoxClass(item)"
                            >
                              <template v-if="item.courseId">
                                <div class="text-center">
                                  进度：{{ Math.floor(item.learningProcess * 100) }}%
                                </div>
                              </template>
                              <template v-else-if="item.fieldType === TaskType.EXAM">
                                <div class="flex gap-4">
                                  <div class="text-center">{{ getExamStatus(item).duration }}</div>
                                  <div v-if="item.completionStatus" class="text-center">
                                    得分：{{ getExamStatus(item).score }}
                                  </div>
                                </div>
                              </template>
                              <template v-else>
                                <div class="text-center">
                                  {{
                                    CompletionStatusConfig[item.completionStatus]?.text || "未开始"
                                  }}
                                </div>
                              </template>
                            </div>
                          </div>
                        </template>
                      </div>
                    </div>

                    <!-- 终点节点 -->
                    <template v-if="isEndRow(rowIndex, row)">
                      <div
                        :class="[
                          'absolute transform -translate-y-1/2 top-1/2',
                          rowIndex % 2 === 0 ? 'right-[30px]' : 'left-[30px]'
                        ]"
                      >
                        <BaseSvgIcon
                          icon-class="end-node.svg"
                          class="text-orange-500"
                          size="50px"
                          color="#ff9800"
                        />
                      </div>
                    </template>
                  </div>

                  <!-- 垂直连接线和箭头 -->
                  <template v-if="rowIndex < taskRows.length - 1">
                    <div
                      :class="[
                        'absolute w-[2px]',
                        rowIndex % 2 === 0 ? 'right-[80px]' : 'left-[80px]'
                      ]"
                      :style="{
                        height: getVerticalLineHeight(rowIndex),
                        top: '60px'
                      }"
                    >
                      <div class="h-full dashed-line-vertical"></div>
                      <div class="arrow-vertical"></div>
                    </div>
                  </template>
                </div>
              </template>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<style lang="less" scoped>
  .dashed-line {
    background-image: linear-gradient(90deg, #ff9800 50%, transparent 50%);
    background-size: 12px 2px;
    background-repeat: repeat-x;
    position: absolute;
    width: calc(100% - 2rem);
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
  }

  .dashed-line-vertical {
    background-image: linear-gradient(0deg, #ff9800 50%, transparent 50%);
    background-size: 2px 12px;
    background-repeat: repeat-y;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 100%;
  }

  /* 添加网格布局容器样式 */
  .grid-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    padding: 0 4rem;
  }

  /* 确保任务节点垂直对齐 */
  .task-node {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    position: relative;
    padding: 0 1rem;
    height: 100%;
  }

  /* 修改SVG图标颜色 */
  :deep(.svg-icon) {
    fill: #ff9800;
  }

  /* 确保垂直连接线的定位 */
  .vertical-connector {
    position: absolute;
    width: 2px;
    transform: translateX(-50%);
  }

  /* 优化箭头样式 */
  .arrow-down {
    position: absolute;
    left: 50%;
    z-index: 2;
    transform: translate(-50%, -50%) rotate(45deg);
  }

  /* 添加新的grid布局样式 */
  .grid {
    display: grid;
    width: 100%;
    position: relative;
    margin: 0 auto;
    max-width: 1200px;
  }

  /* 确保内容容器高度一致 */
  .content-container {
    min-height: 160px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  /* 修改箭头节点样式 */
  .arrow-horizontal {
    position: absolute;
    top: 50%;
    width: 10px;
    height: 10px;
    border-width: 2px 2px 0 0;
    border-style: solid;
    border-color: #ff9800;
    z-index: 2;
  }

  .arrow-right {
    transform: translateY(-50%) rotate(45deg);
  }

  .arrow-left {
    transform: translateY(-50%) rotate(-135deg);
  }

  .arrow-vertical {
    position: absolute;
    left: 50%;
    top: 50%;
    width: 10px;
    height: 10px;
    border-width: 0 2px 2px 0;
    border-style: solid;
    border-color: #ff9800;
    transform: translateX(-50%) rotate(45deg);
    z-index: 2;
  }

  :deep(.el-progress) {
    width: 350px;
  }
</style>
