<!--
 * @Description: 
 * @Author: <PERSON><PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-24 14:04:19
 * @LastEditTime: 2024-09-02 16:57:13
-->
<script setup lang="ts">
  import useTenantStore from "@/store/modules/tenant"

  const tenantStore = useTenantStore()
</script>

<template>
  <footer class="app_footer">
    <div class="footer_con">
      <div class="base_info">
        <div class="contact"
          >服务热线: 130-321-59810 <small>周一至周日 9:00-18:00</small>
          <small>（法定节假日除外）</small></div
        >
        <div class="copyright">
          上海柏科管理咨询股份有限公司 版权所有 严禁复制 网站备案号：
          <a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank">
            沪ICP备2021018439号-2
          </a></div
        >
      </div>
      <div class="other_info">
        <ul>
          <li>
            <i class="iconfont icon-chang<PERSON><PERSON>wenti"></i>
            <p>问题反馈</p>
          </li>
          <li>
            <img
              src="https://training-voc.obs.cn-north-4.myhuaweicloud.com:443/%E4%B8%8A%E6%B5%B7%E6%9F%8F%E7%A7%91%E5%85%AC%E4%BC%97%E5%8F%B7%E4%BA%8C%E7%BB%B4%E7%A0%81.jpg"
              alt=""
            />
            <p>公众号</p>
          </li>
          <li>
            <img
              src="https://training-voc.obs.cn-north-4.myhuaweicloud.com:443/%E4%B8%8A%E6%B5%B7%E6%9F%8F%E7%A7%91%E5%AE%A2%E6%9C%8D%E4%BA%8C%E7%BB%B4%E7%A0%81.jpg"
              alt=""
            />
            <p>客服微信</p>
          </li>
        </ul>
      </div>
    </div>
  </footer>
</template>

<style scoped lang="less">
  .app_footer {
    overflow: hidden;

    background-color: #0f0a1e;
    .footer_con {
      width: 1240px;
      margin: 20px auto;
      display: flex;
    }
    .contact {
      color: #fff;
      font-size: 14px;
      line-height: 28px;
    }
    .base_info {
      display: flex;
      flex-direction: column;
      justify-content: center;
      flex: 1;
    }
    .other_info {
      width: 360px;
      color: #fff;
      > ul {
        display: flex;
        > li {
          margin: 0 20px;
          flex: 1;
          text-align: center;
          > i {
            font-size: 57px;
          }
          > p {
            font-size: 12px;
            line-height: 28px;
          }
        }
      }
    }
    .copyright {
      color: #93999f;
      line-height: 1.7;
      font-size: 12px;
      > a {
        color: #93999f;
      }
    }
  }

</style>
