/*
 * @Description: 全局组件
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-14 09:43:19
 * @LastEditTime: 2024-12-02 14:58:09
 */
import type { App, Plugin } from "vue"
import { useIntersectionObserver } from "@vueuse/core"
import defaultImg from "@/assets/images/200.png"

// 使用 import.meta.glob 自动导入所有组件
const moduleComponents = import.meta.glob("./**/index.vue", { eager: true })
const moduleOtherComponents = import.meta.glob("./**/*.vue", { eager: true })

// 创建动态导出
const exportComponents: Record<string, any> = {}

// 处理所有 index.vue 组件
Object.entries(moduleComponents).forEach(([path, module]) => {
  const componentName = path
    .split("/")
    .slice(-2, -1)[0]
    .replace(/^\w/, c => "Base" + c.toUpperCase())

  exportComponents[componentName] = (module as any).default
})

// 处理特殊命名的组件
Object.entries(moduleOtherComponents).forEach(([path, module]) => {
  if (path.includes("index.vue")) return

  const fileName = path.split("/").pop()?.split(".")[0] || ""
  if (["pane", "Item"].includes(fileName)) {
    const componentName = "Base" + fileName.replace(/^\w/, c => c.toUpperCase())
    exportComponents[componentName] = (module as any).default
  }
})

// Vue3 插件写法
const BaseUI: Plugin = {
  install(app: App) {
    // 注册所有组件
    Object.entries(exportComponents).forEach(([name, component]) => {
      app.component(name, component)
    })

    // 注册 v-lazy 指令
    app.directive("lazy", {
      mounted(el: HTMLImageElement, { value }) {
        const { stop } = useIntersectionObserver(el, ([{ isIntersecting }]) => {
          if (isIntersecting) {
            stop()
            el.src = value
            el.onerror = function () {
              el.src = defaultImg
            }
          }
        })
      }
    })
  }
}

// 导出默认插件
export default BaseUI

// 导出所有组件
export const {
  BaseTypeFilter,
  BaseSlider,
  BaseSingleRichText,
  BaseVideoPlayer,
  BaseLeftImgRightText,
  BaseMarquee,
  BaseRightImgLeftText,
  BaseBackTop,
  BaseLeftImgRightList,
  BaseTopImgBottomList,
  BaseCarousel,
  BaseMore,
  BaseBread,
  BaseBreadItem,
  BaseImageView,
  BaseCity,
  BaseCount,
  BaseButton,
  BaseCheckBox,
  BaseDialog,
  BaseTabs,
  BaseTabPane,
  BaseInfiniteLoad,
  BasePagination,
  BaseFilePreview,
  BaseImageUpload,
  BaseArrowSort,
  BaseEcharts,
  BaseEsign,
  BaseVideoUpload,
  BaseFaceVerification,
  BaseSvgIcon
} = exportComponents

// 做统一出口
export * from "./Message/index"
