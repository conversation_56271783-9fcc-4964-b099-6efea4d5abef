<!--
 * @Description: 我的课程
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-21 14:22:38
 * @LastEditTime: 2024-11-29 17:09:29
-->

<script setup lang="ts">
  import { myCourseList } from "@/api/course"
  import { Search, ArrowRight } from "@element-plus/icons-vue"
  import { useRouter } from "vue-router"
  import {
    CourseStatus,
    CourseStatusMap,
    CourseType,
    CourseTypeMap,
    CourseTypeStyle,
    CourseStatusOrder,
    CourseTypeOrder
  } from "@/types/course"

  const router = useRouter()
  const pageNum = ref<number>(1)
  const pageSize = ref<number>(8)
  const dataTotal = ref<number>(0)
  const searchValue = ref("")
  const courseList = ref<any[]>([])

  // 状态筛选
  const learnStatus = ref<CourseStatus>(CourseStatus.ALL)
  const courseType = ref<CourseType>(CourseType.ALL)

  async function fetchData() {
    const queryData = {
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      courseName: searchValue.value,
      learnStatus: learnStatus.value,
      courseType: courseType.value,
      ...extraQueryData.value
    }
    const { rows, total } = await myCourseList(queryData)
    dataTotal.value = total || 0
    courseList.value = rows
  }

  const extraQueryData = ref({})
  const handleFilterChange = () => {
    pageNum.value = 1
    fetchData()
  }

  const arrowSortChange = value => {
    pageNum.value = 1
    extraQueryData.value = { ...value }
    fetchData()
  }

  const handleDetail = ({ courseId, taskId = undefined }) => {
    const routeData = router.resolve({
      path: "/course/detail",
      query: {
        id: courseId,
        // @ts-ignore
        ...(taskId && { taskId })
      }
    })
    window.open(routeData.href, "_blank")
  }

  // 添加日期格式化函数
  const formatDate = (dateStr: any) => {
    if (!dateStr) return ""
    return dateStr.split(" ")[0] // 只保留年月日
  }

  onMounted(() => {
    fetchData()
  })
</script>

<template>
  <div class="bg-white">
    <div class="p-4 border-b border-gray-200">
      <div class="mb-4 flex items-center">
        <div class="text-gray-600 text-16px mr-5 text-skyblueColor font-bold">完成状态：</div>
        <el-radio-group v-model="learnStatus" @change="handleFilterChange">
          <el-radio v-for="status in CourseStatusOrder" :key="status" :label="status">
            {{ CourseStatusMap[status] }}
          </el-radio>
        </el-radio-group>
      </div>
      <div class="flex items-center">
        <div class="text-gray-600 text-16px mr-5 text-skyblueColor font-bold">课程类型：</div>
        <el-radio-group v-model="courseType" @change="handleFilterChange">
          <el-radio v-for="type in CourseTypeOrder" :key="type" :label="type">
            {{ CourseTypeMap[type] }}
          </el-radio>
        </el-radio-group>
      </div>
    </div>

    <div class="flex justify-between items-center p-4 border-b-2px border-gray-200 border-b-dashed">
      <BaseArrowSort
        :data-list="[
          { name: 'a.last_study_time', label: '最后学习' },
          { name: 'a.learning_process', label: '完成进度' },
          { name: 'b.course_name', label: '名称' }
        ]"
        @sort="arrowSortChange"
      />
      <el-input
        v-model="searchValue"
        class="w-300px rounded-full"
        :prefix-icon="Search"
        @keyup.enter="handleFilterChange"
        @clear="handleFilterChange"
        clearable
      />
    </div>

    <div v-if="courseList.length > 0" class="p-5">
      <div
        v-for="item in courseList"
        :key="item.courseId"
        class="flex items-center p-5 border-b border-gray-200"
      >
        <!-- 左侧图片容器 -->
        <div class="w-300px flex-shrink-0 relative">
          <div class="pb-[56.25%] relative overflow-hidden">
            <img
              :src="item.courseImage"
              alt=""
              class="absolute inset-0 w-full h-full object-cover object-center"
              loading="lazy"
            />
          </div>
        </div>

        <!-- 右侧内容容器 -->
        <div class="flex justify-between items-stretch ml-5 flex-1 h-[169px]">
          <div class="flex flex-col justify-between flex-1">
            <div class="flex items-center">
              <span
                v-if="item.courseType && item.courseType !== CourseType.ALL"
                :class="[
                  CourseTypeStyle[item.courseType],
                  'px-2 py-1 text-xs text-white rounded mr-2'
                ]"
              >
                {{ CourseTypeMap[item.courseType] }}
              </span>
              <router-link
                :to="'/course/detail?id=' + item.courseId"
                target="_blank"
                class="font-bold hover:text-sky-500 text-20px"
              >
                {{ item.courseName }}
              </router-link>
            </div>

            <div class="text-16px text-gray-600 my-3">
              <template v-if="item.taskId">
                <div class="mb-2">任务名称：{{ item.taskName }}</div>
                <div>
                  开始结束时间：{{ formatDate(item.startTime) }} ~
                  {{ formatDate(item.endTime) }}
                </div>
              </template>
              <template v-else>
                <div>
                  上下架时间：{{ formatDate(item.startTime) }} ~
                  {{ formatDate(item.endTime) }}
                </div>
              </template>
            </div>

            <div class="text-16px font-bold flex items-center">
              状态：
              <el-tag
                class="text-14px"
                size="large"
                :type="
                  item.learnStatus === CourseStatus.COMPLETED
                    ? 'success'
                    : item.learnStatus === CourseStatus.RUNNING
                    ? 'warning'
                    : 'danger'
                "
              >
                {{ item.learnStatusDesc }}
              </el-tag>
            </div>
          </div>

          <div class="flex items-center gap-8 flex-1">
            <div class="max-w-350px w-300px">
              <el-progress
                :percentage="Math.floor(item.learningProcess * 100) || 0"
                :stroke-width="20"
                class="scale-110"
                color="#ef91bc"
              />
            </div>

            <el-button
              :type="item.learnStatus === CourseStatus.COMPLETED ? 'default' : 'primary'"
              @click="handleDetail(item)"
              class="w-150px h-40px text-16px ml-auto"
            >
              {{ item.learnStatus === CourseStatus.COMPLETED ? "查看详情" : "去学习" }}
              <el-icon class="ml-2"><ArrowRight /></el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <el-empty v-else description="暂无数据" :image-size="200" />

    <BasePagination
      v-if="courseList.length > 0"
      class="mt-4"
      :total="dataTotal"
      v-model:pageNum="pageNum"
      v-model:pageSize="pageSize"
      @pagination="fetchData"
    />
  </div>
</template>

<style lang="less" scoped>
  :deep(.el-progress-bar__inner) {
    background-color: #ef91bc !important;
  }
</style>
