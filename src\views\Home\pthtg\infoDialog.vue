<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    :close-on-click-modal="false"
    center
    width="600px"
    destroy-on-close
  >
    <div>
      <img class="h-80px absolute right-40px" src="@/assets/images/examine.png" alt="" />
      <div class="flex items-center px-10px">
        <img class="w-50px h-50px mr-20px" src="@/assets/images/avatar.png" alt="" />
        <div class="text-16px">
          <div>{{ dataForm.userName }}</div>
          <div>{{ dataForm.idNumber }}</div>
        </div>
      </div>
      <div class="mt-20px text-16px px-10px"
        ><span class="color-#448ffa">审核状态:</span> 审核中</div
      >
      <el-divider></el-divider>
      <div class="flex items-center justify-between mt-60px px-10px">
        <div>证件类型</div>
        <div>{{ dataForm.idType }}</div>
      </div>
      <el-divider border-style="dashed"></el-divider>
      <div class="flex items-center justify-between px-10px">
        <div>姓名</div>
        <div>{{ dataForm.userName }}</div>
      </div>
      <el-divider border-style="dashed"></el-divider>
      <div class="flex items-center justify-between px-10px mb-20px">
        <div>证件号码</div>
        <div>{{ dataForm.idNumber }}</div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
  import { ref, getCurrentInstance } from "vue"
  import { addRegistration } from "@/api/pthtg/index"
  import { Delete, Plus } from "@element-plus/icons-vue"
  import { ElMessage } from "element-plus"
  import { sendCode } from "@/api/login"

  const emit = defineEmits(["refreshDataList"])
  const { proxy } = getCurrentInstance()
  const visible = ref(false)
  const dataFormRef = ref()
  const submitLoading = ref(false)
  const dataForm = ref({})
  // 打开弹窗
  const open = data => {
    visible.value = true
    dataForm.value = data
  }

  defineExpose({
    open
  })
</script>
