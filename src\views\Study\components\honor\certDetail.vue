<template>
  <div class="profile-container" v-loading="loading">
    <div class="content">
      <div class="mr-auto">
        <el-button type="warning" icon="ArrowLeft" @click="handleBack"> 返 回 </el-button>
        <el-button type="primary" icon="Download" class="mr-auto" @click="handleExport">
          导 出
        </el-button>
      </div>
      <!-- 表格部分 -->
      <div class="course">
        <div class="course-header">
          <div class="left">
            <div>1</div>
            <div>课程</div>
          </div>
          <el-pagination
            class="pagination"
            v-model:current-page="coursePageNum"
            v-model:page-size="coursePageSize"
            layout="total, prev, pager, next,jumper"
            :total="courseTotal"
            @current-change="fetchCourseList"
          />
        </div>
        <el-table :data="courseData" :border="false">
          <el-table-column prop="courseName" label="课程名称" width="180" />
          <el-table-column prop="learningProcess" label="完成进度" width="180" />
          <el-table-column prop="learnStatus" label="学习状态" width="180" />
          <el-table-column prop="deltaDuration" label="学习时长" width="180">
            <template #default="scope">
              {{ formatSeconds(scope.row.deltaDuration) }}
            </template>
          </el-table-column>
          <el-table-column prop="startTime" label="开始学习时间" width="180" />
          <el-table-column prop="actualCompletionTime" label="实际完成时间" width="180" />
          <el-table-column prop="associatedExamGrade" label="关联的考试得分" />
        </el-table>
      </div>
      <div class="course">
        <div class="course-header">
          <div class="left">
            <div>2</div>
            <div>考试</div>
          </div>
          <el-pagination
            class="pagination"
            v-model:current-page="examPageNum"
            v-model:page-size="examPageSize"
            layout="total, prev, pager, next,jumper"
            :total="examTotal"
            @current-change="fetchExamList"
          />
        </div>
        <el-table :data="examData" style="width: 100%" :border="false">
          <el-table-column prop="examName" label="考试名称" width="180" />
          <el-table-column prop="lowestScore" label="及格分" width="180" />
          <el-table-column prop="userPaperScore" label="用户得分" width="180" />
          <el-table-column prop="isPass" label="是否通过" width="180">
            <template #default="scope">
              <dict-tag :options="not_or_is" :value="scope.row.isPass" />
            </template>
          </el-table-column>
          <el-table-column prop="examTime" label="考试用时" width="180" />
          <el-table-column prop="submitTime" label="参加考试时间" width="180" />
          <el-table-column prop="examStatus" label="考试状态">
            <template #default="scope">
              <dict-tag :options="exam_participate_status" :value="scope.row.examStatus" />
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- <div class="course">
        <div class="course-header">
          <div class="left">
            <div>3</div>
            <div>问卷</div>
          </div>
          <el-pagination
            class="pagination"
            v-model:current-page="questionnairePageNum"
            v-model:page-size="questionnairePageSize"
            layout="total, prev, pager, next, jumper"
            :total="questionnaireTotal"
            @current-change="fetchQuestionnaireList"
          />
        </div>
        <el-table :data="questionnaireData" style="width: 100%" :border="false">
          <el-table-column prop="surveyTitle" label="调查标题" width="300" />
          <el-table-column prop="startTime" label="开始时间" width="360" />
          <el-table-column prop="endTime" label="结束时间" width="360" />
          <el-table-column prop="participateStatus" label="参与状态">
            <template #default="scope">
              <dict-tag :options="exam_participate_status" :value="scope.row.participateStatus" />
            </template>
          </el-table-column>
        </el-table>
      </div> -->
    </div>
  </div>
</template>

<script setup lang="ts">
  import dayjs from "dayjs"
  import {
    getTrainingCourseList,
    getTrainingExamList,
    getTrainingQuestionnaireList
  } from "@/api/trainingTask"
  import { formatSeconds } from "@/utils/common"
  import useUserStore from "@/store/modules/user"

  const route = useRoute()
  const router = useRouter()
  const { proxy } = getCurrentInstance()!
  const userStore = useUserStore()
  const { userInfo } = storeToRefs(userStore)

  // 从路由获取参数
  const { id: certificateId, courseId, examId, taskId } = route.query

  const { exam_participate_status, not_or_is } = proxy?.useDict(
    "exam_participate_status",
    "not_or_is"
  )!

  const loading = ref(false)

  // 课程分页数据
  const coursePageNum = ref(1)
  const coursePageSize = ref(5)
  const courseTotal: any = ref(0)
  const courseData: any = ref([])

  // 考试分页数据
  const examPageNum = ref(1)
  const examPageSize = ref(5)
  const examTotal: any = ref(0)
  const examData: any = ref([])
  // 获取课程表格数据
  const fetchCourseList = async () => {
    const queryData = {
      pageNum: coursePageNum.value,
      pageSize: coursePageSize.value,
      courseId: courseId,
      examId: examId,
      taskId: taskId,
      userId: userInfo.value.userId
    }
    const courseRes = await getTrainingCourseList(queryData)
    if (courseRes.code === 200) {
      courseData.value = courseRes.rows
      courseTotal.value = courseRes.total
    }
  }

  // 获取考试表格数据
  const fetchExamList = async () => {
    const queryData = {
      pageNum: examPageNum.value,
      pageSize: examPageSize.value,
      courseId: courseId,
      examId: examId,
      taskId: taskId,
      userId: userInfo.value.userId
    }
    const examRes = await getTrainingExamList(queryData)
    if (examRes.code === 200) {
      examData.value = examRes.rows
      examTotal.value = examRes.total
    }
  }

  // 导出功能修改
  const handleExport = () => {
    proxy?.download(
      "reportforms/report/export",
      {
        certificateId,
        courseId,
        examId,
        taskId
      },
      `证书详情_${dayjs().format("YYYY-MM-DD")}.xlsx`
    )
  }

  const handleBack = () => {
    router.back()
  }

  onMounted(() => {
    fetchCourseList()
    fetchExamList()
  })
</script>

<style scoped lang="less">
  .profile-container {
    display: flex;
    align-items: center;
    flex-direction: column;

    .title {
      font-size: 40px;
      margin-bottom: 20px;
      color: #666666;
    }

    .content {
      width: 85%;
      padding: 20px;
      display: flex;
      flex-direction: column;
      align-items: center;

      .content-datePicker {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;

        :deep(.el-input__wrapper) {
          flex-grow: 0;
        }

        :deep(.el-date-editor) {
          margin-right: 30px;
        }
      }

      .indexpart {
        margin-top: 50px;
        height: 300px;
        width: 90%;
      }

      .course {
        margin-top: 30px;
        width: 100%;
        .course-header {
          position: relative;
          display: flex;
          height: 60px;
          background-color: #f4f3f3;
          align-items: center;
          justify-content: space-between;

          .left {
            background-color: #5ca1fc;
            height: 100%;
            width: 200px;
            align-items: center;
            color: white;
            display: flex;
            :first-child {
              width: 30px;
              height: 30px;
              border-radius: 50%;
              background-color: white;
              text-align: center;
              line-height: 30px;
              color: #81b8fc;
              margin-left: 30px;
              margin-right: 30px;
            }
          }

          .left::after {
            content: "";
            position: absolute;
            left: 140px;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 0 60px 60px 0;
            border-color: transparent #f4f3f3 transparent transparent;
          }

          .pagination {
            margin-right: 30px;
          }
        }
      }
    }
  }
</style>
