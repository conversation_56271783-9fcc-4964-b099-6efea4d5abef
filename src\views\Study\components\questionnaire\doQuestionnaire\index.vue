<!--
 * @Description: 调查问卷填写页面
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-22 16:49:39
 * @LastEditTime: 2024-11-29 14:45:36
-->
<script lang="ts" setup>
  import { getQuestionById, submitQuestionnaire, getQuestionList } from "@/api/questionnaire"
  import { addAnswer } from "@/api/questionnaire"
  import { ElMessage } from "element-plus"
  import type { questionnaireType } from "@/types"

  let surveyList = ref<questionnaireType[]>([])
  let questionnaireData = ref()
  const router = useRouter()
  let route = useRoute()

  const fetchQuestionData = async () => {
    let queryData = {
      questionnaireId: route.query.questionnaireId
    }

    const { data } = await getQuestionList(queryData)
    surveyList.value = data

    surveyList.value.forEach(item => {
      if (item.questionType === "S") {
        item.userAnswer = ""
      } else if (item.questionType === "M") {
        item.userAnswer = []
      }
    })
  }

  const fetchQuestionnaireData = async () => {
    const { data } = await getQuestionById(route.query.questionnaireId)
    questionnaireData.value = data
  }

  //每选一题发送一次
  const fetchQuestionAnswer = async (item, index, code?) => {
    if (item.questionType === "S") {
      surveyList.value[index].userAnswer = ""
      surveyList.value[index].userAnswer = code
    }

    let queryData = {
      questionId: item.traningQuestionId,
      questionnaireIssuedId: route.query.questionnaireIssuedId,
      userAnswer: Array.isArray(surveyList.value[index].userAnswer)
        ? surveyList.value[index].userAnswer?.sort().join()
        : surveyList.value[index].userAnswer
    }
    await addAnswer(queryData)
  }

  //提交问卷
  const submitQusetionnaire = async () => {
    let queryData = {
      questionnaireIssuedId: route.query.questionnaireIssuedId
    }
    const { code } = await submitQuestionnaire(queryData)
    if (code === 200) {
      ElMessage({
        message: "感谢您的填写",
        type: "success"
      })
      router.go(-1)
    } else {
      ElMessage({
        message: "请稍后再试",
        type: "error"
      })
    }
  }

  onMounted(() => {
    fetchQuestionnaireData()
    fetchQuestionData()
  })
</script>

<template>
  <div class="survey">
    <div class="container">
      <div class="survey-title">
        <div>{{ questionnaireData?.surveyTitle }}</div>
        <div>
          {{ questionnaireData?.surveyDescription }}
        </div>
      </div>
      <div class="survey-content">
        <div
          class="survey-content-list"
          v-for="(item, index) in surveyList"
          :key="item.traningQuestionId"
        >
          <div class="survey-content-list-title"> {{ index + 1 }} 、 {{ item.questionName }} </div>
          <template v-if="item.questionType === 'Q'">
            <el-input
              type="textarea"
              v-model="item.userAnswer"
              @blur="fetchQuestionAnswer(item, index)"
              :rows="4"
            ></el-input>
          </template>

          <template v-else v-for="items in 11" :key="items">
            <div class="survey-content-list-radio" v-if="item[`item${items}`]">
              <el-radio-group
                v-if="item.questionType === 'S'"
                v-model="item.userAnswer"
                @change="fetchQuestionAnswer(item, index, String.fromCharCode(64 + items))"
              >
                <el-radio :label="String.fromCharCode(64 + items)" size="large">{{
                  item[`item${items}`]
                }}</el-radio>
              </el-radio-group>
              <el-checkbox-group
                v-else-if="item.questionType === 'M'"
                v-model="item.userAnswer"
                @change="fetchQuestionAnswer(item, index, String.fromCharCode(64 + items))"
              >
                <el-checkbox :label="String.fromCharCode(64 + items)">{{
                  item[`item${items}`]
                }}</el-checkbox>
              </el-checkbox-group>
            </div>
          </template>
        </div>
        <el-button class="subminButton" @click="submitQusetionnaire">提交</el-button>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
  .survey {
    background: url(https://training.ehsconsulting.com.cn/resources/skins/basicV2/images/need-bg.jpg)
      repeat top center fixed;
    min-height: 100vh;
  }

  .container {
    padding: 40px 0;
    .survey-title {
      background-color: rgba(255, 255, 255, 0.7);
      display: flex;
      flex-direction: column;
      padding: 50px 80px;
      border-bottom: 1px solid #eeeeee;

      :first-child {
        text-align: center;
        font-size: 40px;
        margin-bottom: 40px;
      }

      :last-child {
        color: #666666;
        font-size: 18px;
      }
    }
    .survey-content {
      background-color: rgba(255, 255, 255, 0.7);
      display: flex;
      flex-direction: column;
      padding: 30px 80px;

      .survey-content-list {
        margin-bottom: 30px;

        .survey-content-list-title {
          margin-bottom: 20px;
          font-size: 16px;
        }

        .survey-content-list-radio {
          margin-left: 30px;
          font-size: 14px;
        }
      }

      .subminButton {
        width: 150px;
        height: 40px;
        margin: 40px auto;
        background-color: @warmOrange;
        color: white;
      }
    }
  }
</style>
