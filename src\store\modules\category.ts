/*
 * @Description: category store
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-14 09:43:19
 * @LastEditTime: 2025-06-06 14:32:48
 */
import { defineStore } from "pinia"

//定义category仓库
const useCategoryStore = defineStore("category", {
  state: () => ({
    categoryList: [
      {
        id: 1,
        name: "首页",
        url: "/home"
      },
      {
        id: 12,
        name: "学习",
        url: "/study"
      },
      {
        id: 72,
        name: "课程",
        url: "/course"
      },
      {
        id: 73,
        name: "资料",
        url: "/means"
      },
      {
        id: 74,
        name: "通知",
        url: "/news"
      },
      {
        id: 75,
        name: "游戏",
        url: "/game"
      }
    ]
  })
})

export default useCategoryStore
