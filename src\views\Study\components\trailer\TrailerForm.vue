<template>
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="800px"
    append-to-body
    destroy-on-close
    top="5vh"
  >
    <el-form
      class="px-10 pt-10px"
      ref="formRef"
      :model="formData"
      :rules="isDetail ? {} : rules"
      label-width="100px"
      validate-on-rule-change="false"
    >
      <el-form-item label="视频名称" prop="title">
        <el-input v-if="!isDetail" v-model="formData.title" placeholder="请输入视频名称" />
        <span v-else>{{ formData.title }}</span>
      </el-form-item>
      <el-form-item label="封面" prop="cover">
        <BaseImageUpload v-if="!isDetail" v-model="formData.cover" :limit="1" :fileSize="5" />
        <div v-else class="cover-preview">
          <img
            :src="formData.cover || DEFAULT_COVER"
            alt="封面图片"
            class="w-200px h-120px object-cover rounded"
          />
        </div>
      </el-form-item>
      <el-form-item label="视频文件" prop="url">
        <BaseVideoUpload
          v-if="!isDetail"
          v-model:videoFileList="formData.url"
          :fileSize="2048"
          :limit="1"
          :fileType="FILE_COURSE_TYPE_MAP[COURSE_TYPE.VIDEO_COURSE]"
          :canDownload="false"
          @onVideoUploadSuccess="handleVideoUploadSuccess"
        />
        <div v-else class="video-preview">
          <div class="video-cover" @click="handlePlayVideo" v-if="!isPlaying">
            <img :src="formData.cover || DEFAULT_COVER" alt="视频封面" />
            <div class="play-icon">
              <el-icon><VideoPlay /></el-icon>
            </div>
          </div>
          <video
            v-show="isPlaying"
            :src="formData.url"
            controls
            controlsList="nodownload"
            @ended="handleVideoEnd"
            ref="videoRef"
          ></video>
        </div>
      </el-form-item>
      <el-form-item label="视频类型" prop="videoType">
        <el-select v-if="!isDetail" v-model="formData.videoType" placeholder="请选择视频类型">
          <el-option
            v-for="dict in public_trailer_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
        <dict-tag v-else :options="public_trailer_type" :value="formData.videoType" />
      </el-form-item>

      <!-- 详情时显示的额外字段 -->
      <template v-if="isDetail">
        <el-form-item label="上传时间">
          <span>{{ formData.createTime }}</span>
        </el-form-item>
        <el-form-item label="审核结果">
          <dict-tag :options="review_result" :value="formData.approveResult" />
        </el-form-item>
        <el-form-item label="审核时间" v-if="formData.approveTime">
          <span>{{ formData.approveTime }}</span>
        </el-form-item>
        <el-form-item label="审核意见" v-if="formData.approveRemark">
          <span>{{ formData.approveRemark }}</span>
        </el-form-item>
      </template>
    </el-form>
    <template #footer>
      <div class="dialog-footer" v-if="!isDetail">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="closeDialog">取 消</el-button>
      </div>
      <div class="dialog-footer" v-else>
        <el-button @click="closeDialog">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { COURSE_TYPE, FILE_COURSE_TYPE_MAP } from "@/utils/constant"
  import { getCurrentInstance } from "vue"
  import { addVideo, updateVideo } from "@/api/trailer"
  import type { FormInstance } from "element-plus"
  import { cloneDeep } from "lodash-es"
  import { VideoPlay } from "@element-plus/icons-vue"
  import { DEFAULT_COVER } from "@/utils/constant"
  const { proxy } = getCurrentInstance()!
  const { public_trailer_type, review_result } = proxy!.useDict(
    "public_trailer_type",
    "review_result"
  )
  const emit = defineEmits(["success", "update:modelValue"])
  const dialogVisible = ref(false)
  const dialogTitle = ref("")
  const isDetail = ref(false)
  const isPlaying = ref(false)
  const videoRef = ref()

  const formRef = ref<FormInstance>()
  const formData: any = ref({
    title: "",
    url: [],
    videoType: "",
    cover: "",
    createTime: "",
    approveStatus: "",
    approveTime: "",
    approveRemark: ""
  })

  const rules = {
    title: [{ required: true, message: "请输入视频名称", trigger: "blur" }],
    url: [{ required: true, message: "请上传视频文件", trigger: "change" }],
    videoType: [{ required: true, message: "请选择视频类型", trigger: "change" }]
  }

  const handleVideoUploadSuccess = videoInfo => {
    // 处理视频上传成功后的回调
    console.log("视频信息:", videoInfo)
  }

  const handlePlayVideo = () => {
    isPlaying.value = true
    nextTick(() => {
      if (videoRef.value) {
        videoRef.value.play()
      }
    })
  }

  const handleVideoEnd = () => {
    isPlaying.value = false
  }

  const submitForm = async () => {
    if (!formRef.value) return
    await formRef.value.validate(async valid => {
      if (valid) {
        try {
          const submitData = cloneDeep(formData.value)
          if (submitData.url.length) {
            submitData.url = submitData.url.map(item => item.fileUrl).join(",")
          }
          if (submitData.id) {
            await updateVideo(submitData)
          } else {
            await addVideo(submitData)
          }
          proxy?.$message.success("操作成功")
          closeDialog()
          emit("success")
        } catch (error) {
          console.error(error)
        }
      }
    })
  }

  const closeDialog = () => {
    dialogVisible.value = false
    isPlaying.value = false
    resetForm()
    if (formRef.value) {
      formRef.value.clearValidate()
    }
  }

  const resetForm = () => {
    formData.value = {
      title: "",
      url: [],
      videoType: "",
      cover: "",
      createTime: "",
      approveStatus: "",
      approveTime: "",
      approveRemark: ""
    }
  }

  const defaultCover = new URL(`@/assets/images/bg.jpg`, import.meta.url).href

  const openDialog = (type: string, row?: any) => {
    resetForm()
    dialogVisible.value = true
    isDetail.value = type === "detail"
    dialogTitle.value = type === "add" ? "新增视频" : type === "edit" ? "编辑视频" : "视频详情"

    nextTick(() => {
      if (formRef.value) {
        formRef.value.clearValidate()
      }

      if (row) {
        formData.value = cloneDeep(row)
        if (isDetail.value && typeof row.url === "string") {
          formData.value.url = row.url
        }
      }
    })
  }

  defineExpose({
    openDialog
  })
</script>

<style scoped lang="less">
  .dialog-footer {
    text-align: center;
  }

  .video-preview {
    width: 640px;
    height: 360px;
    margin: 0 auto;
    background: #000;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;

    .video-cover {
      position: relative;
      cursor: pointer;
      width: 100%;
      height: 100%;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
        display: block;
      }

      .play-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;

        .el-icon {
          font-size: 30px;
          color: white;
        }
      }

      &:hover .play-icon {
        background: rgba(0, 0, 0, 0.7);
      }
    }

    video {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
</style>
