/*
 * @Description:课程
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-06 11:20:29
 * @LastEditTime: 2025-03-04 13:09:50
 */
// 所有属性都为可选
export type classType = Partial<{
  courseImage: string
  courseId: number
  courseType: string
  catalogueName: string
  courseName: string
  lecturer: string
  credits: number
  creditHours: number
  completionConditions: string
  courseIntroduction: string
  curriculum: string
  courseObjectives: string
  favoriteOrNot: boolean
  favoriteCounts: number
  learnStatusDesc: string
  learnStatus: string
  learningProcess: number
  lastStudyTime: string
  free: boolean
  courseGrade: number
  selfCourseGrade: null | number
  needSnapshot: string
  multiplyPlayback: string
  progressBarDrag: string
  hotValue: number
  courseQuestionLinkList: Array
  courseStatus: string
  offShelfTime: string
  taskId: number
  taskName: string
  startTime: string
  endTime: string
  hasTask: boolean
}>
