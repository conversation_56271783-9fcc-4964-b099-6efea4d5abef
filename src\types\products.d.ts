/*
 * @Description: products.d.ts
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-14 09:43:19
 * @LastEditTime: 2023-08-24 08:58:03
 */
export interface NewsItem {
  id: string
  createTime: string
  name: string
  image: string
  summary: string
  desc: string
  content: string
}

export type NewsItemList = NewsItem[]

export interface NewsListInfo {
  counts: number
  pageSize: number
  pages: number
  page: number
  items: NewsItemList
}
