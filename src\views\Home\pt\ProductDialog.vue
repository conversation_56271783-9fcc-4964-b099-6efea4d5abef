<script setup lang="ts">
  import { ref, watch, onMounted } from "vue"
  import { getProductList, getEnterprise } from "@/api/system/enterprise"
  import { ElMessage } from "element-plus"

  // 定义产品类型接口
  interface Product {
    id: number
    enterpriseId: number
    productName: string
    productIntro: string
    discountInfo: string
    createBy: string | null
    createTime: string | null
    updateBy: string | null
    updateTime: string | null
    remark: string | null
  }

  // 定义企业信息接口
  interface EnterpriseInfo {
    id: number
    enterpriseName: string
    contactName: string
    position: string
    phone: string
    address: string
    enterpriseIntro: string
    createBy: string | null
    createTime: string | null
    updateBy: string | null
    updateTime: string | null
    remark: string | null
  }

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    },
    enterpriseId: {
      type: [String, Number],
      default: ""
    },
    enterpriseName: {
      type: String,
      default: ""
    }
  })

  const emit = defineEmits(["update:visible"])

  const dialogVisible = ref(props.visible)
  const loading = ref(false)
  const productList = ref<Product[]>([])
  const enterpriseInfo = ref<EnterpriseInfo>({} as EnterpriseInfo)

  watch(
    () => props.visible,
    val => {
      dialogVisible.value = val
      if (val && props.enterpriseId) {
        fetchEnterpriseInfo()
        fetchProducts()
      }
    }
  )

  watch(
    () => dialogVisible.value,
    val => {
      emit("update:visible", val)
    }
  )

  // 获取企业详细信息
  const fetchEnterpriseInfo = async () => {
    if (!props.enterpriseId) return

    try {
      const res = await getEnterprise(props.enterpriseId)
      if (res.code === 200) {
        enterpriseInfo.value = res.data
      } else {
        ElMessage.error(res.msg || "获取企业信息失败")
      }
    } catch (error) {
      console.error("获取企业信息失败:", error)
      ElMessage.error("获取企业信息失败")
    }
  }

  const fetchProducts = async () => {
    if (!props.enterpriseId) return

    loading.value = true
    try {
      const res = await getProductList(props.enterpriseId)
      if (res.code === 200) {
        productList.value = res.rows || []
      } else {
        ElMessage.error(res.msg || "获取产品列表失败")
      }
    } catch (error) {
      console.error("获取产品列表失败:", error)
      ElMessage.error("获取产品列表失败")
    } finally {
      loading.value = false
    }
  }
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`${enterpriseName}产品列表`"
    width="1500px"
    :destroy-on-close="true"
  >
    <!-- 企业信息展示区域 -->
    <div class="enterprise-info mb-20px p-20px bg-gray-50 rounded-8px">
      <div class="grid grid-cols-2 gap-16px mb-16px">
        <div class="flex items-center">
          <span class="text-gray-500 w-100px">企业名称：</span>
          <span class="text-gray-900 font-medium">{{ enterpriseInfo.enterpriseName }}</span>
        </div>
        <div class="flex items-center">
          <span class="text-gray-500 w-100px">联系人：</span>
          <span class="text-gray-900">{{ enterpriseInfo.contactName }}</span>
        </div>
        <div class="flex items-center">
          <span class="text-gray-500 w-100px">职务：</span>
          <span class="text-gray-900">{{ enterpriseInfo.position || "暂无" }}</span>
        </div>
        <div class="flex items-center">
          <span class="text-gray-500 w-100px">电话：</span>
          <span class="text-gray-900">{{ enterpriseInfo.phone }}</span>
        </div>
        <div class="flex items-center col-span-2">
          <span class="text-gray-500 w-100px">地址：</span>
          <span class="text-gray-900">{{ enterpriseInfo.address }}</span>
        </div>
      </div>
      <div class="flex items-start">
        <span class="text-gray-500 w-100px">企业简介：</span>
        <div class="text-gray-900 flex-1">{{ enterpriseInfo.enterpriseIntro }}</div>
      </div>
    </div>

    <el-divider>产品列表</el-divider>

    <el-skeleton :rows="5" animated v-if="loading" />

    <div v-else-if="productList.length > 0">
      <el-table :data="productList" style="width: 100%" border stripe>
        <el-table-column
          prop="productName"
          label="产品/服务名称"
          min-width="180"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <div class="font-bold text-gray-800">{{ row.productName }}</div>
          </template>
        </el-table-column>

        <el-table-column prop="productIntro" label="产品/服务简介及价格" min-width="400">
          <template #default="{ row }">
            <div class="text-gray-600">{{ row.productIntro || "暂无描述" }}</div>
          </template>
        </el-table-column>

        <el-table-column prop="discountInfo" label="对促进会会员优惠/免费说明" min-width="210">
          <template #default="{ row }">
            <div
              v-if="row.discountInfo"
              class="bg-blue-50 text-blue-500 py-4px px-8px rounded text-13px inline-block break-words whitespace-normal"
            >
              {{ row.discountInfo }}
            </div>
            <div v-else class="text-gray-400">暂无优惠</div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div v-else class="flex justify-center items-center py-30px">
      <el-empty description="暂无产品数据" />
    </div>
  </el-dialog>
</template>

<style scoped>
  .enterprise-info {
    border: 1px solid #e5e7eb;
  }
</style>
