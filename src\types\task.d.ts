import { TaskType, TaskStatus } from './task'

// 任务详情信息类型
export type taskInfoType = {
  coverPhoto: string
  endTime: string
  startTime: string
  rateLearning: number
  taskId: number
  taskName: string
  taskStatus: TaskStatus
  courseId: number
  completionStatus: TaskStatus
  examId: number
  questionnaireId: number
  preconditionLinkList: Array<any>
  mixedPrecondition: string
  examName: string
  courseName: string
  manageName: string
  questionnaireName: string
  learningProcess: number
  fieldType: TaskType
  fieldId: any
}
