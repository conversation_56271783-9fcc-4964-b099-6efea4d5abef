// 主题
@baseColor: #fff;
// 辅助
@helpColor: #e26237;
// 成功
@sucColor: #1dc779;
// 警告
@warnColor: #ffb302;
// 红色
@redColor: #cf4444;
// 蓝色
@blueColor: #00a0e9;
// 蔚蓝
@cerulean: #3ea2fd;
// 深蓝
@deepColor: #1f77b3;
// 暖橙
@warmOrange: #f58335;
//天空蓝
@skyblueColor: #3a86f2;

:root {
  --deepColor: #1a1a1a;
  --skyblueColor: #0088f4;
  --warmOrange: #f58335;
  
  // 添加 element-plus 主题色变量
  --el-color-primary: var(--skyblueColor) !important;
  --el-color-primary-light-3: color-mix(in srgb, var(--skyblueColor) 60%, white) !important;
  --el-color-primary-light-5: color-mix(in srgb, var(--skyblueColor) 40%, white) !important;
  --el-color-primary-light-7: color-mix(in srgb, var(--skyblueColor) 20%, white) !important;
  --el-color-primary-light-8: color-mix(in srgb, var(--skyblueColor) 10%, white) !important;
  --el-color-primary-light-9: color-mix(in srgb, var(--skyblueColor) 5%, white) !important;
  --el-color-primary-dark-2: color-mix(in srgb, var(--skyblueColor) 80%, black) !important;
}
