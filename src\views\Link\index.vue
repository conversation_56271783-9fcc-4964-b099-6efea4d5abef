<!--
 * @Description: 
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-14 09:43:19
 * @LastEditTime: 2023-08-24 09:04:20
-->
<script setup lang="ts">
  import { useRoute } from "vue-router"
  import useStore from "@/store"
  import LinkAside from "./components/link-aside.vue"
  // 获取当前路由对象
  const route = useRoute()
  // 获取动态路由参数的name
  const name = route.query.name
  //获取轮播图图片
  const { ad } = useStore()
  ad.getOneImage()
</script>

<template>
  <div class="container">
    <!-- 面包屑 -->
    <BaseBread>
      <BaseBreadItem to="/">首页</BaseBreadItem>
      <BaseBreadItem>{{ name }}</BaseBreadItem>
    </BaseBread>

    <div class="link-list">
      <!-- 左：侧边栏组件 -->
      <LinkAside />
      <!-- 右：内容 -->
      <div class="article">
        <!-- 三级路由的出口 -->
        <RouterView />
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
  .link-list {
    display: flex;
    // margin-top: 20px;
    .article {
      width: 1000px;
    }
  }
</style>
