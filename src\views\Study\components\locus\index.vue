<!--
 * @Description: 我的轨迹页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-09 09:19:25
 * @LastEditTime: 2025-01-02 14:33:55
-->
<script setup lang="ts">
  import { ref, getCurrentInstance } from "vue"
  import { studyLogList } from "@/api/course/study-log"
  import { Monitor, Iphone, Document } from "@element-plus/icons-vue"
  import { addDateRange } from "@/utils/common"

  const trackLearnedTypeMap = {
    0: {
      label: "课程",
      icon: Monitor,
      color: "#a0cfff"
    },
    1: {
      label: "考试",
      icon: Iphone,
      color: "#95d475"
    },
    2: {
      label: "资料",
      icon: Document,
      color: "#f59e0b"
    }
  }

  const { proxy } = getCurrentInstance()!
  const locusList = ref<any>([])
  const { traning_learned_type } = proxy?.useDict("traning_learned_type")!

  const dateRange = ref("")
  const activeType = ref("")

  async function fetchData(value?) {
    const queryData = {
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      ...value
    }
    const { rows, total } = await studyLogList(
      addDateRange(queryData, dateRange.value, "queryTime")
    )
    locusList.value = rows
    dataTotal.value = total
  }

  const pageNum = ref(1)
  const pageSize = ref(8)
  const dataTotal = ref<number | undefined>(0)

  //秒转时分秒
  function second(value: any) {
    let theTime: any = parseInt(value) // 秒
    let middle: any = 0 // 分
    let hour: any = 0 // 小时

    if (theTime >= 60) {
      middle = theTime / 60
      theTime = theTime % 60
      if (middle >= 60) {
        hour = middle / 60
        middle = middle % 60
      }
    }
    var result = "" + parseInt(theTime) + "秒"
    if (middle > 0) {
      result = "" + parseInt(middle) + "分" + result
    }
    if (hour > 0) {
      result = "" + parseInt(hour) + "小时" + result
    }
    return result
  }

  const handleFilterChange = (value?) => {
    pageNum.value = 1
    fetchData({ learnedType: value })
  }

  const handleDateChange = () => {
    pageNum.value = 1
    fetchData()
  }

  onMounted(() => {
    fetchData()
  })
</script>

<template>
  <div class="bg-white">
    <div class="border-b border-gray-200">
      <div class="p-4">
        <span class="text-gray-600 text-16px mr-5 text-skyblueColor font-bold">轨迹类型：</span>
        <el-radio-group v-model="activeType" @change="handleFilterChange">
          <el-radio label="">全部</el-radio>
          <el-radio v-for="item in traning_learned_type" :key="item.value" :label="item.value">
            {{ item.label }}
          </el-radio>
        </el-radio-group>
      </div>
      <div class="flex items-center p-4 border-b-2px border-gray-200 border-b-dashed">
        <span class="text-gray-600 text-16px mr-4 font-500">轨迹时间：</span>
        <div class="!w-240px">
          <el-date-picker
            v-model="dateRange"
            value-format="YYYY-MM-DD"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handleDateChange"
          >
          </el-date-picker>
        </div>
      </div>
    </div>

    <template v-if="locusList.length">
      <div class="p-10 bg-white">
        <el-timeline>
          <el-timeline-item
            v-for="item in locusList"
            :key="item.studyLogId"
            :icon="trackLearnedTypeMap[item.learnedType].icon"
            size="large"
            :color="trackLearnedTypeMap[item.learnedType].color"
            :timestamp="item.createTime"
            type="primary"
            class="pb-10"
          >
            <span v-if="item.courseId"
              >学习了课程《{{ item.courseName }}》，持续时间{{
                second(item.lastDeltaDuration)
              }}，学习进度到了{{ item.learningProcess * 100 }}%</span
            >
            <span v-else-if="item.examId"
              >参加了考试《{{ item.examName }}》，持续时间{{
                second(item.lastDeltaDuration)
              }}，考试分数{{ item.learningProcess }}分</span
            >
            <span v-else-if="item.manageId"
              >浏览了资料《{{ item.manageName }}》，持续时间{{
                second(item.lastDeltaDuration)
              }}，浏览进度到了{{ item.learningProcess * 100 }}%</span
            >
          </el-timeline-item>
        </el-timeline>
      </div>
      <BasePagination
        class="pagination"
        :total="dataTotal!"
        v-model:pageNum="pageNum"
        v-model:pageSize="pageSize"
        @pagination="fetchData"
      />
    </template>
    <el-empty v-else description="暂无数据" :image-size="200"></el-empty>
  </div>
</template>

<style lang="less" scoped>
  :deep(.el-timeline-item__node--large) {
    height: 30px;
    width: 30px;
    top: -5px;
    left: -10px;
  }
</style>
