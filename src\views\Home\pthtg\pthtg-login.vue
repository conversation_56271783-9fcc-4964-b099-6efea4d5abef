<!--
 * @Description: 上海市企业首席合同官培训认证平台
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2025-02-25 11:01:57
 * @LastEditTime: 2025-03-12 14:11:15
-->

<script lang="ts" setup>
  import { ref, onMounted, computed } from "vue"
  import RegisterDialog from "./registerDialog.vue"
  import InfoDialog from "./infoDialog.vue"
  import { getStatusByIdNumber } from "@/api/pthtg"
  import { loginRequest, getPublicKey } from "@/api/login"
  import { encrypt } from "@/utils/jsencrypt"
  import { setToken } from "@/utils/auth"
  import { cloneDeep } from "lodash-es"
  import { ElMessage } from "element-plus"

  const loginForm = ref({
    username: "",
    password: ""
  })
  const RegisterDialogRef = ref()
  const InfoDialogRef = ref()

  // 密码校验规则
  const validatePassword = (rule, value, callback) => {
    if (!value) {
      return callback(new Error("密码不能为空"))
    }
    if (value.length < 8 || value.length > 18) {
      return callback(new Error("密码长度必须在8到18位之间"))
    }
    const hasLetter = /[a-zA-Z]/.test(value)
    const hasNumber = /\d/.test(value)
    const hasSymbol = /[~!@#$&*()_+\-=.':/;,?]/.test(value)
    if (!hasLetter || !hasNumber || !hasSymbol) {
      return callback(new Error("密码必须包含字母、数字和特殊符号~!@#$&*()_+-=.':/;,?"))
    }
    callback()
  }
  // 表单验证规则
  const loginFormRules = ref({
    username: [{ required: true, message: "手机号不能为空", trigger: "blur" }],
    password: [{ validator: validatePassword, required: true, trigger: "blur" }]
  })

  const router = useRouter()
  const login = async () => {
    const res: any = await getStatusByIdNumber(loginForm.value.username)
    if (!res.data) {
      return ElMessage({ message: "该手机号未注册", type: "warning" })
    }
    if (res.data.status === "1") {
      const submitData = cloneDeep(loginForm.value)
      const response: any = await getPublicKey()
      submitData.password = encrypt(submitData.password, response.publicKey)
      const res1 = await loginRequest(submitData)
      setToken(res1.data.access_token)
      router.push("/home")
    } else {
      InfoDialogRef.value.open(res.data)
    }
  }

  const openDialog = () => {
    RegisterDialogRef.value.open()
  }
</script>
<template>
  <div
    class="flex flex-col items-center justify-center min-h-100vh"
    style="background: linear-gradient(to bottom, #73aae0, #ffffff)"
  >
    <div class="text-50px mt--100px font-bold color-#203fa0">上海市企业首席合同官培训认证平台</div>
    <div class="ml--320px mt-50px text-16px font-bold">
      <div class="color-#ec3933 mb-20px">友情提示：</div>
      <div class="ml-50px color-#26579d">1.网站是上海市企业首席合同官培训,本站免费提供报名入口</div>
    </div>
    <div
      class="w-500px bg-#ffffff rounded-lg mt-40px"
      style="box-shadow: 5px 5px 15px rgba(0, 0, 0, 0.4)"
    >
      <div
        class="flex items-center justify-between p-20px"
        style="border-bottom: 1px solid #d7d7d7"
      >
        <div class="text-20px">考生登录</div>
        <div @click="openDialog" class="text-14px color-#2a68a6 cursor-pointer"
          >没有账号？立即注册</div
        >
      </div>
      <el-form
        class="px-60px py-20px mt-20px"
        :model="loginForm"
        :rules="loginFormRules"
        :hide-required-asterisk="true"
      >
        <el-form-item label="手机号码" prop="username">
          <el-input placeholder="请输入手机号" v-model="loginForm.username"></el-input>
        </el-form-item>
        <el-form-item label="登录密码" prop="password">
          <el-input
            show-password
            type="password"
            placeholder="请输入登录密码"
            v-model="loginForm.password"
          ></el-input>
        </el-form-item>
      </el-form>
      <div style="border-top: 1px solid #d7d7d7" class="p-20px flex justify-center">
        <el-button type="primary" @click="login">登录</el-button>
      </div>
    </div>
  </div>

  <RegisterDialog ref="RegisterDialogRef" />
  <InfoDialog ref="InfoDialogRef" />
</template>
