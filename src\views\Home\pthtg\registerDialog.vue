<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    :close-on-click-modal="false"
    center
    width="600px"
    destroy-on-close
  >
    <el-form
      label-width="80px"
      ref="dataFormRef"
      :model="dataForm"
      :rules="dataFormRules"
      @keyup.enter="submitHandle()"
    >
      <!-- <el-form-item label="证件类型" prop="idType">
        <el-input placeholder="请输入证件类型" disabled v-model="dataForm.idType" />
      </el-form-item> -->
      <el-form-item label="姓名" prop="userName">
        <el-input placeholder="请输入姓名" v-model="dataForm.userName" />
      </el-form-item>
      <el-form-item label="证件号码" prop="idNumber">
        <el-input placeholder="请输入证件号码" v-model="dataForm.idNumber" />
      </el-form-item>
      <el-form-item label="手机号码" prop="phone">
        <el-input placeholder="请输入手机号码" v-model="dataForm.phone">
          <template #prepend>+86</template>
        </el-input>
      </el-form-item>
      <el-form-item label="验证码" prop="code">
        <el-input v-model="dataForm.code" placeholder="短信验证码" clearable>
          <template #append>
            <el-button
              type="primary"
              :disabled="countdown > 0"
              @click="sendSmsCode"
              class="send-code-btn"
            >
              {{ countdown > 0 ? `${countdown}s后重试` : "发送验证码" }}
            </el-button>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="单位名称" prop="workUnit">
        <el-input placeholder="请输入单位名称" v-model="dataForm.workUnit" />
      </el-form-item>
      <el-form-item label="文化程度" prop="eduLevel">
        <el-select v-model="dataForm.eduLevel" placeholder="请输入文化程度">
          <el-option
            v-for="dict in educational_background"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="电子邮箱" prop="email">
        <el-input placeholder="请输入电子邮箱" v-model="dataForm.email" />
      </el-form-item>
      <el-form-item label="登录密码" prop="password">
        <el-input
          type="password"
          show-password
          placeholder="请输入登录密码"
          v-model="dataForm.password"
        />
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input
          type="password"
          show-password
          placeholder="请输入确认密码"
          v-model="dataForm.confirmPassword"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitHandle()" :loading="submitLoading">注册</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import { ref, getCurrentInstance } from "vue"
  import { addRegistration } from "@/api/pthtg/index"
  import { Delete, Plus } from "@element-plus/icons-vue"
  import { ElMessage } from "element-plus"
  import { sendCode } from "@/api/login"

  const emit = defineEmits(["refreshDataList"])
  const { proxy } = getCurrentInstance()
  const { educational_background } = proxy?.useDict("educational_background")
  const visible = ref(false)
  const dataFormRef = ref()
  const submitLoading = ref(false)
  const codeData = ref()
  const dataForm = ref({
    idType: "身份证",
    userName: undefined,
    idNumber: undefined,
    phone: undefined,
    code: undefined,
    workUnit: undefined,
    eduLevel: undefined,
    email: undefined,
    password: undefined,
    confirmPassword: undefined
  })
  const dialogTitle = ref("注册")
  const countdown = ref(0)
  const timer = ref(null)

  // 密码校验规则
  const validatePassword = (rule, value, callback) => {
    if (!value) {
      return callback(new Error("密码不能为空"))
    }
    if (value.length < 8 || value.length > 18) {
      return callback(new Error("密码长度必须在8到18位之间"))
    }
    const hasLetter = /[a-zA-Z]/.test(value)
    const hasNumber = /\d/.test(value)
    const hasSymbol = /[~!@#$&*()_+\-=.':/;,?]/.test(value)
    if (!hasLetter || !hasNumber || !hasSymbol) {
      return callback(new Error("密码必须包含字母、数字和特殊符号~!@#$&*()_+-=.':/;,?"))
    }
    callback()
  }

  // 确认密码校验规则
  const validateConfirmPassword = (rule, value, callback) => {
    if (!value) {
      return callback(new Error("请再次输入密码"))
    }
    if (value !== dataForm.value.password) {
      return callback(new Error("两次输入的密码不一致"))
    }
    callback()
  }

  // 确认密码校验规则
  const validateCode = (rule, value, callback) => {
    if (!value) {
      return callback(new Error("请输入验证码"))
    }
    if (value !== codeData.value) {
      return callback(new Error("验证码不一致"))
    }
    callback()
  }

  const validateEmail = (rule, value, callback) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!value) {
      callback(new Error("请输入邮箱地址"))
    } else if (!emailRegex.test(value)) {
      callback(new Error("请输入有效的邮箱地址"))
    } else {
      callback()
    }
  }

  // 表单验证规则
  const dataFormRules = ref({
    userName: [{ required: true, message: "姓名不能为空", trigger: "blur" }],
    idNumber: [{ required: true, message: "身份证不能为空", trigger: "blur" }],
    code: [{ required: true, validator: validateCode, trigger: "blur" }],
    phone: [{ required: true, message: "手机号不能为空", trigger: "blur" }],
    email: [{ required: true, validator: validateEmail, trigger: "blur" }],
    password: [{ validator: validatePassword, required: true, trigger: "blur" }],
    confirmPassword: [{ validator: validateConfirmPassword, required: true, trigger: "blur" }]
  })
  // 打开弹窗
  const open = () => {
    // 重置表单数据
    dataForm.value = {
      idType: "身份证",
      userName: undefined,
      idNumber: undefined,
      phone: undefined,
      code: undefined,
      workUnit: undefined,
      eduLevel: undefined,
      email: undefined,
      password: undefined,
      confirmPassword: undefined
    }
    visible.value = true
  }

  // 发送验证码
  const sendSmsCode = async () => {
    if (!dataForm.value.phone) {
      ElMessage.error("请输入手机号")
      return
    }
    // 验证手机号格式
    if (!/^1[3-9]\d{9}$/.test(dataForm.value.phone)) {
      ElMessage.error("请输入正确的手机号")
      return
    }

    try {
      let res = await sendCode({ mobile: dataForm.value.phone, scene: 10 })
      codeData.value = res.data
      ElMessage.success("验证码发送成功")
      countdown.value = 60
      timer.value = setInterval(() => {
        if (countdown.value > 0) {
          countdown.value--
        } else {
          if (timer.value) {
            clearInterval(timer.value)
            timer.value = null
          }
        }
      }, 1000)
    } catch (error) {
      console.error("发送验证码失败:", error)
    }
  }

  // 表单提交
  const submitHandle = () => {
    dataFormRef.value.validate(async valid => {
      if (!valid) {
        return false
      }
      submitLoading.value = true
      try {
        const res = await addRegistration(dataForm.value)
        if (res.code === 200) {
          ElMessage.success("您的注册信息已经提交审核！")
          visible.value = false
          emit("refreshDataList")
        } else {
          ElMessage.error(res.msg || "注册失败")
        }
      } catch (error) {
        console.error("注册失败:", error)
        ElMessage.error("注册失败")
      } finally {
        submitLoading.value = false
      }
    })
  }

  defineExpose({
    open
  })
</script>

<style scoped>
  .product-item {
    background-color: #f8f9fa;
  }
</style>
