/*
 * @Description: 新闻相关类型
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-05 14:24:03
 * @LastEditTime: 2023-09-15 16:32:54
 */
export type newsType = {
  dataType: string
  newsTitle: string
  newsContent: string
  createTime: string
  newsId: number
  publishStartTime: string
}

export type ArrowSortType = {
  name: string
  label: string
  isSort?: boolean
}

export type typeFilterType = {
  title: string
  optionsList: optionsListType
  labelField: string
  comparisonField: string
  backParamField: string
  isShow?: boolean
  rotate?: boolean
}

export type optionsListType = {
  label: string
  value: string
  length: number
}
