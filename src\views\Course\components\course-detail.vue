<script lang="ts" setup>
  import { getTaskInfoById } from "@/api/trainingTask"
  import { Base64 } from "js-base64"
  import { getToken } from "@/utils/auth"
  import {
    specialTenantList,
    COURSE_TYPE,
    FILE_COURSE_TYPE_MAP,
    NO_NEED_HOOK_DOMAIN_LIST,
    noNeedVideoPreventOnHookTenantList
  } from "@/utils/constant"
  import {
    getCourse,
    getCourseStats,
    updateCourseProgress,
    courseBuriedPoint,
    clearCourseProgress
  } from "@/api/course"
  import { listPassage } from "@/api/passage"
  import type { classType, passageType } from "@/types"
  import { getFileName } from "@/utils/common"
  import detailIntro from "./detail-intro.vue"
  import detailBody from "./detail-body.vue"
  import useTenantStore from "@/store/modules/tenant"
  import AnswerDialog from "./answerDialog.vue"
  import { ElMessage } from "element-plus"

  const FREQUENCY_TIME = 10 * 1000 // 每10秒调用一次获取进度接口
  const HOOK_TIME = 15 * 60 * 1000 // 15分钟

  const { domainName } = storeToRefs(useTenantStore())
  const route = useRoute()
  const router = useRouter()
  const courseInfo = ref<classType>()
  const passageList = ref<passageType[]>([])
  const canvasRef = ref<HTMLCanvasElement | null>(null)
  const cameraRef = ref<HTMLVideoElement | null>(null)
  const videoRef = ref()
  const isShowOnHookDialog = ref(false)
  const gradeVal = ref() // 评分值
  const videoHeight = ref()
  const videoWidth = ref()
  const learnedStatus = ref(false)
  const vue3VideoPlayerRef = ref()
  const currentPlayItem = ref<any>() // 当前播放的章节视频信息

  // 添加文档预览组件的状态管理
  const docLoading = ref(true)
  const docError = ref(false)

  // 添加加载状态
  const loading = ref(true)

  // 获取课程
  async function fetchCourseData() {
    loading.value = true
    try {
      if (!route.query.id) return
      const { data } = await getCourse(route.query.id)
      courseInfo.value = data
      gradeVal.value = courseInfo.value?.courseGrade
    } finally {
      loading.value = false
    }
  }

  // 获取章节
  async function fetchPassageData() {
    if (!route.query.id) return
    let queryData = {
      courseId: route.query.id,
      pageSize: 999
    }
    const { rows } = await listPassage(queryData)
    passageList.value = rows
    currentPlayItem.value = passageList.value[0].videoFileList[0]
  }

  // 切换章节
  const passageIndex = ref(0)
  const videoIndex = ref(0)
  const setActive = async (item, index, index2) => {
    videoLoading.value = true
    videoUrlValidated.value = false
    currentPlayItem.value = item
    passageIndex.value = index
    videoIndex.value = index2
    clearInterval(hookTimer.value)
    clearInterval(timer.value)

    // 等待 DOM 更新完成
    await nextTick()
    await getCourseProgress()

    // 预加载验证视频URL
    if (!isWordCourse.value && currentPlayItem.value?.vodUrl) {
      await ensureVideoUrlValid()
    } else {
      videoLoading.value = false
    }

    // 切换文件后重新启动定时器
    if (isWordCourse.value) {
      startDocumentTimer()
    }
  }

  // 课程热度埋点
  const addCourseHotPoint = async () => {
    if (!route.query.id) return
    await courseBuriedPoint(route.query.id)
  }

  const lastPlayTime = ref()
  const timer = ref()
  const lastProgress = ref(0)

  // 判断是否需要记录学习进度和开启定时器等功能
  // const shouldEnableFullFeatures = computed(() => {
  //   return !!route.query.taskId
  // })

  // 播放事件
  const startPlay = async (e: any) => {
    try {
      if (route.query.taskId && !canPlayVideo.value) {
        e.target.pause()
        ElMessage.warning("请先完成人脸识别")
        return
      }

      videoRef.value = e.target
      // 重置视频加载重试计数器
      videoLoadRetryCount.value = 0
      
      if (
        currentPlayItem.value?.mediaDuration! > 300 &&
        !noNeedVideoPreventOnHookTenantList.includes(domainName.value)
      ) {
        preventOnHook()
      }
      timer.value = setInterval(async () => {
        courseProgressUpdate()
      }, FREQUENCY_TIME)
    } catch (error) {
      console.error("视频播放开始事件处理错误:", error)
      ElMessage.error("视频播放失败，请刷新页面重试")
    }
  }

  // 视频暂停事件
  const pauseTimer = async (e: any) => {
    try {
      videoRef.value = e.target
      clearInterval(hookTimer.value)
      clearInterval(timer.value)
      await courseProgressUpdate()
    } catch (error) {
      console.error("视频暂停事件处理错误:", error)
    }
  }

  const statusList = ref()

  // 获取视频播放进度
  const getCourseProgress = async () => {
    if (!route.query.id) return
    const params = route.query.taskId ? { taskId: route.query.taskId } : undefined
    const res = await getCourseStats(route.query.id, params)
    if (res.code === 200) {
      if (!res.data || res.data.length === 0) return
      statusList.value = res.data
      const statusItem = statusList.value.find(
        item => item.passageId === currentPlayItem.value?.passageId
      )
      currentPlayItem.value = Object.assign(currentPlayItem.value!, statusItem)
      learnedStatus.value = res.data.every(item => {
        return item.learnedStatus === "2"
      })

      // 只有在视频课程时才需要处理播放进度
      if (!isWordCourse.value && currentPlayItem.value?.vodUrl) {
        res.data.forEach(item => {
          if (item.passageId === currentPlayItem.value?.passageId) {
            lastPlayTime.value = item.mediaProgress
            // 只要原先播放的进度和视频总时长相差小于2秒，则清空原有进度(从头播放)
            if (Math.abs(item.mediaDuration - item.mediaProgress) < 2) {
              currentPlayItem.value!.mediaProgress = 0
              return
            }
            // 如果有视频进且视频播放器已初始化，直接跳转到进度位置
            if (videoRef.value) {
              videoRef.value.currentTime = lastPlayTime.value
            }
          }
        })
        // 只有在视频课程时才需要自动播放
        vue3VideoPlayerRef.value?.togglePlay()
      }
    }
  }

  const hookTimer = ref()
  // 防挂机
  const preventOnHook = () => {
    hookTimer.value = setInterval(() => {
      // 检查视频是否处于全屏状态
      if (videoRef.value?.clientHeight !== videoHeight.value) {
        document.exitFullscreen()
      }
      isShowOnHookDialog.value = true
      videoRef.value!.pause()
      clearInterval(hookTimer.value)
    }, HOOK_TIME)
  }

  // 点击放挂机弹窗的在
  const isOK = () => {
    isShowOnHookDialog.value = false
    videoRef.value!.play()
  }

  const loadStart = (e: any) => {
    try {
      videoRef.value = e.target
      videoHeight.value = videoRef.value?.clientHeight
      videoWidth.value = videoRef.value?.clientWidth
    } catch (error) {
      console.error("视频加载开始事件处理错误:", error)
    }
  }

  const endVideo = async () => {
    // 1.更新视频播放进度
    await courseProgressUpdate()
    // 2.如果这个章节下还有下一个视频，则播放下一个视频
    if (videoIndex.value < passageList.value[passageIndex.value].videoFileList.length - 1) {
      videoIndex.value++
      currentPlayItem.value = passageList.value[passageIndex.value].videoFileList[videoIndex.value]
      await getCourseProgress()
    }
    // 3.如果该章节下没有下一个视频
    else {
      // 3.1 如果这个节下还有下一个章，则继续播放下一个章节下的第一个视频
      if (passageIndex.value < passageList.value.length - 1) {
        passageIndex.value++
        videoIndex.value = 0
        currentPlayItem.value =
          passageList.value[passageIndex.value].videoFileList[videoIndex.value]
        await getCourseProgress()
      }
      // 3.2 如果没有下个章节，则只更新各个章节的学习状态
      else {
        const params = route.query.taskId ? { taskId: route.query.taskId } : undefined
        const res = await getCourseStats(route.query.id, params)
        statusList.value = res.data
        learnedStatus.value = res.data.every(item => {
          return item.learnedStatus === "2"
        })
      }
    }
  }

  // 章节进度
  const schedule = id => {
    if (!id) return 0
    const statItem = statusList.value?.find(item => item.passageId === id)
    if (!statItem) return
    return statItem.learnedDuration >= statItem.mediaDuration || statItem.learnedStatus === "2"
      ? 100
      : Math.floor((statItem.learnedDuration / statItem.mediaDuration) * 100)
  }

  let isSpeed = computed(() => courseInfo.value?.progressBarDrag === "1")

  // 更新视频播放进度
  const courseProgressUpdate = async () => {
    // 如果不需要记录进度，直接返回
    if (!needRecordProgress.value) return

    const currentTime = videoRef.value!.currentTime
    if (currentTime !== lastProgress.value) {
      const deltaDuration =
        courseInfo.value?.multiplyPlayback === "1"
          ? (FREQUENCY_TIME / 1000) * videoRef.value!.playbackRate
          : FREQUENCY_TIME / 1000
      lastProgress.value = currentTime
      // 调用后端接口记录进度
      const requestData: any = {
        courseId: route.query.id,
        passageId: currentPlayItem.value?.passageId,
        learnFileType: "0",
        deltaDuration, // 增量时间，传增的看视频时长
        mediaProgress: currentTime!, // 视频播放节点，传已看到的视频节点
        mediaDuration: videoRef.value!.duration // 视频总时长
      }

      // 有taskId时才添加taskId字段
      if (route.query.taskId) {
        requestData.taskId = route.query.taskId
      }
      await updateCourseProgress(requestData)
    }
  }

  const handleClose = () => {
    router.push("/course")
  }

  // 视频时间变化
  const onTimeUpdate = e => {
    // 该视频状态已完成 || 该视频下没有问答题 || 视频答题弹窗已经打开时 不执行下方代码

    if (
      currentPlayItem.value.learnedStatus === "2" ||
      !currentPlayItem.value.courseQuestionLinkList ||
      currentPlayItem.value.courseQuestionLinkList.length === 0 ||
      answerDialogRef.value.visible
    )
      return
    // 该视频下有问答题时
    const currentTime = isWordCourse.value ? e.target.currentTime : videoRef.value?.currentTime
    if (!currentTime) return
    for (let i = 0; i < currentPlayItem.value.courseQuestionLinkList.length; i++) {
      const qsItem = currentPlayItem.value.courseQuestionLinkList[i]
      // 如果已经答过该题，跳过
      if (qsItem.isAlreadyAnswer) continue
      if (Math.floor(currentTime) === qsItem.embeddingPoint) {
        currentPlayItem.value.courseQuestionLinkList[i].isAlreadyAnswer = true
        openAnswerDialog(qsItem)
        break
      }
    }
  }

  // 视频答题弹窗逻辑
  const answerDialogRef = ref()
  // 答题
  const openAnswerDialog = qsItem => {
    // 如果是文档课程，暂停计时
    if (isWordCourse.value) {
      isPaused.value = true
      pausedTime.value = Date.now()
    } else {
      // 检查视频是否处于全屏状态
      if (videoRef.value?.clientHeight !== videoHeight.value) {
        document.exitFullscreen()
      }
      videoRef.value!.pause()
    }
    answerDialogRef.value.openDialog(qsItem)
  }

  const answerQuestionDone = async (flag, needReset = false) => {
    if (needReset) {
      // 清空学习进度
      const requestData = {
        taskId: route.query.taskId,
        courseId: route.query.id,
        passageId: currentPlayItem.value?.passageId
      }
      await clearCourseProgress(requestData)

      // 如果是视频课程，将视频进度调整到0
      if (!isWordCourse.value && videoRef.value) {
        videoRef.value.currentTime = 0
      }
    }

    if (isWordCourse.value) {
      // 继续计时，更新开始时间以抵消暂停的时间
      isPaused.value = false
      const pauseDuration = Date.now() - pausedTime.value
      startTime.value += pauseDuration
      pausedTime.value = 0
    } else {
      videoRef.value!.play()
    }
  }

  // 添加计算属性判断是否为文档课程
  const isWordCourse = computed(() => {
    return courseInfo.value?.courseType?.includes(COURSE_TYPE.WORD_COURSE)
  })

  const kkFileURL = import.meta.env.VITE_APP_KK_URL
  const currentWordUrl = computed(() => {
    if (!currentPlayItem.value?.fileUrl) return ""

    const fileUrl = currentPlayItem.value.fileUrl
    // 确保 URL 是经过编码的
    const encodedUrl = encodeURIComponent(Base64.encode(fileUrl))
    return `${kkFileURL}/onlinePreview?url=${encodedUrl}`
  })

  const startTime = ref(Date.now())
  const updateTimer = ref()
  const isPaused = ref(false) // 添加暂停状态
  const pausedTime = ref(0) // 记录暂停时的时间

  const startDocumentTimer = () => {
    // 清除可能存在的定时器
    clearInterval(timer.value)
    clearInterval(updateTimer.value)

    // 添加每秒调用 onTimeUpdate 的定时器
    updateTimer.value = setInterval(() => {
      if (!isPaused.value) {
        // 计算当前学习时长作为 currentTime 传入，需要减去暂停的时间
        const pauseDuration = pausedTime.value ? Date.now() - pausedTime.value : 0
        const currentTime = Math.floor((Date.now() - startTime.value - pauseDuration) / 1000)
        onTimeUpdate({ target: { currentTime } })
      }
    }, 1000)

    timer.value = setInterval(async () => {
      // 如果不需要记录进度或处于暂停状态，直接返回
      if (!needRecordProgress.value || isPaused.value) return

      // 文档类型的学习进度更新
      const requestData: any = {
        courseId: route.query.id,
        passageId: currentPlayItem.value?.passageId,
        learnFileType: "0", // 区分文档类型
        deltaDuration: FREQUENCY_TIME / 1000, // 增量时间为固定值
        mediaDuration: currentPlayItem.value?.passageDuration
      }

      // 有taskId时才添加taskId字段
      if (route.query.taskId) {
        requestData.taskId = route.query.taskId
      }
      await updateCourseProgress(requestData)
    }, FREQUENCY_TIME)

    // 记录开始时间
    startTime.value = Date.now()
    isPaused.value = false
    pausedTime.value = 0
  }

  // 修改 iframe 加载事件处理
  const dynamicIframe = ref()

  // 添加 iframe 加载完成和错误处理函数
  const handleIframeLoad = () => {
    docLoading.value = false
    // 如果是文档课程，启动文档学习进度定时器
    if (isWordCourse.value) {
      startDocumentTimer()
    }
  }

  const handleIframeError = () => {
    docLoading.value = false
    docError.value = true
  }

  const taskInfo: any = ref()
  // 人脸识别控制
  const getTaskInfo = async () => {
    const { code, data } = await getTaskInfoById(route.query.taskId)
    if (code === 200) {
      taskInfo.value = data
      if (taskInfo.value.faceCapture === "1") {
        showFaceVerify.value = true
      } else {
        canPlayVideo.value = true
      }
    }
  }

  // 人脸识别成功回调
  const handleVerifySuccess = () => {
    canPlayVideo.value = true
    needRecordProgress.value = true
  }

  // 人脸识别失败回调
  const handleVerifyFail = () => {
    canPlayVideo.value = true
    // 如果人脸识别未通过且不记录学分，则不记录学习进度
    if (taskInfo.value?.recordCredit === "0") {
      needRecordProgress.value = false
    }
  }

  // 添加人脸识别相关的响应式变量
  const showFaceVerify = ref(false)
  const canPlayVideo = ref(false)

  // 修改重试加载方法
  const retryLoadDocument = () => {
    docError.value = false
    docLoading.value = true

    // 重新加载 iframe
    if (dynamicIframe.value) {
      dynamicIframe.value.src = currentWordUrl.value
    }
  }

  // 修改文档变化监听
  watch(
    () => currentPlayItem.value?.fileUrl,
    newVal => {
      if (isWordCourse.value && newVal) {
        docLoading.value = true
        docError.value = false
        // 重置开始时间
        startTime.value = Date.now()
      }
    }
  )

  // 添加是否需要防挂机的计算属性
  const needHook = computed(() => {
    if (!domainName.value) return false
    return !NO_NEED_HOOK_DOMAIN_LIST.includes(domainName.value as any)
  })

  // 添加一个标记来判断是否需要记录学习进度
  const needRecordProgress = ref(true)

  // 添加视频类型判断
  const videoType = computed(() => {
    if (!currentPlayItem.value?.vodUrl) return ""
    const url = currentPlayItem.value.vodUrl.toLowerCase()
    
    // 使用更简单的视频类型映射，避免格式不支持的问题
    if (url.includes(".m3u8")) {
      return "application/x-mpegURL" // 更标准的HLS类型
    }
    if (url.includes(".mp4")) return "video/mp4"
    if (url.includes(".webm")) return "video/webm"
    if (url.includes(".ogg")) return "video/ogg"
    
    // 默认使用标准HLS类型
    return "application/x-mpegURL"
  })

  // 视频播放组件隔离封装
  const videoComponentKey = ref(0)  // 用于强制重新渲染视频组件
  const forceReloadVideo = () => {
    videoComponentKey.value += 1  // 增加key值，强制组件重新渲染
  }
  
  // 视频最大重试次数
  const MAX_AUTO_RETRY_COUNT = 2  // 自动重试上限降低，避免频繁请求

  // 修改视频错误处理
  const handleVideoError = (e: any) => {
    console.error("视频加载错误:", e)
    
    // 增加重试次数限制
    if (videoLoadRetryCount.value >= MAX_AUTO_RETRY_COUNT) {
      ElMessage.error({
        message: "视频加载失败，请手动点击重试",
        duration: 3000
      })
      videoLoading.value = false
      return
    }
    
    videoLoadRetryCount.value++
    
    ElMessage.warning({
      message: `视频加载失败，正在重试(${videoLoadRetryCount.value}/${MAX_AUTO_RETRY_COUNT})...`,
      duration: 2000
    })

    // 检查视频URL是否有效
    if (!videoUrl.value || videoUrl.value.length < 10) {
      console.error("视频URL无效:", videoUrl.value)
      ElMessage.error({
        message: "视频地址无效，请重新进入该课程",
        duration: 3000
      })
      videoLoading.value = false
      return
    }

    // 使用更安全的方式重试加载视频
    setTimeout(() => {
      try {
        videoLoading.value = true
        videoUrlValidated.value = false
        
        // 强制重新渲染视频组件，而不是重用现有组件
        forceReloadVideo()
        
        // 重新验证URL
        setTimeout(async () => {
          await ensureVideoUrlValid()
        }, 500)
      } catch (error) {
        console.error("重试加载视频失败:", error)
        videoLoading.value = false
      }
    }, 2000)
  }

  // 添加视频URL预加载验证状态
  const videoUrlValidated = ref(false)
  const isPreloading = ref(false)

  // 确保视频URL在播放前已验证
  const ensureVideoUrlValid = async () => {
    // 如果已经验证过，直接返回
    if (videoUrlValidated.value) return true
    
    // 清除videoLoading状态
    videoLoading.value = true
    videoLoadRetryCount.value = 0
    
    // 预加载验证
    const isValid = await preloadVideoUrl()
    
    if (!isValid) {
      // 如果预检查失败，尝试刷新token和URL
      console.log('预检查失败，尝试刷新token和URL')
      
      // 手动触发token刷新
      const token = getToken()
      if (token && currentPlayItem.value?.vodUrl) {
        const url = currentPlayItem.value.vodUrl
        // 完全重构URL，确保没有旧的token和缓存
        const baseUrl = url.split('?')[0]  // 只保留基本URL，移除所有参数
        const newUrl = `${baseUrl}?token=${token}&t=${Date.now()}`
        console.log('重新构建URL:', newUrl)
        currentPlayItem.value.vodUrl = newUrl
        
        // 再次尝试预加载验证
        const retryValid = await preloadVideoUrl()
        videoUrlValidated.value = retryValid
        videoLoading.value = false
        return retryValid
      }
      
      videoLoading.value = false
      return false
    }
    
    videoLoading.value = false
    return true
  }

  // 在加载视频URL前验证URL有效性的包装方法
  const safeVideoUrl = computed(() => {
    // 如果URL未验证或正在预加载，返回空字符串防止视频播放器加载无效URL
    if (!videoUrlValidated.value && !isPreloading.value) {
      // 如果还没有开始验证，自动触发验证
      if (!isPreloading.value) {
        ensureVideoUrlValid()
      }
      return ""
    }
    
    return videoUrl.value
  })

  // 添加视频加载状态
  const videoLoading = ref(false)
  const videoLoadRetryCount = ref(0)

  // 预检查视频URL可用性
  const preloadVideoUrl = async () => {
    try {
      if (!currentPlayItem.value?.vodUrl || !videoUrl.value) {
        console.error('无效的视频URL，无法预加载')
        return false
      }
      
      isPreloading.value = true
      
      // 创建临时的XHR请求来验证视频URL是否可访问
      const checkUrl = videoUrl.value
      console.log('正在预检查视频URL:', checkUrl)
      
      // 对于m3u8文件，我们可以尝试直接获取内容而不是HEAD请求
      // 因为有些服务器可能不支持HEAD请求
      const result = await new Promise<boolean>((resolve) => {
        const xhr = new XMLHttpRequest()
        
        // 使用GET请求而不是HEAD，确保能获取到真实内容
        xhr.open('GET', checkUrl, true)
        
        // 对于大文件，我们只获取头部内容即可
        xhr.responseType = 'text'
        
        // 设置超时时间
        xhr.timeout = 10000
        
        xhr.onload = () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            // 检查返回内容是否包含m3u8格式的特征
            const isValidContent = xhr.responseText && (
              xhr.responseText.includes('#EXTM3U') || 
              xhr.responseText.includes('.ts') ||
              xhr.responseText.includes('#EXT-X-')
            )
            
            if (isValidContent) {
              console.log('视频URL预检查成功，内容有效')
              resolve(true)
            } else {
              console.error('视频URL预检查失败，内容无效:', xhr.responseText.substring(0, 100))
              resolve(false)
            }
          } else {
            console.error('视频URL预检查失败，状态码:', xhr.status)
            resolve(false)
          }
        }
        
        xhr.onerror = () => {
          console.error('视频URL预检查请求错误')
          resolve(false)
        }
        
        xhr.ontimeout = () => {
          console.error('视频URL预检查超时')
          resolve(false)
        }
        
        xhr.send()
      })
      
      videoUrlValidated.value = result
      return result
    } catch (error) {
      console.error('预检查视频URL时出错:', error)
      videoUrlValidated.value = false
      return false
    } finally {
      isPreloading.value = false
    }
  }

  // 添加视频重试加载函数
  const retryLoadVideo = async () => {
    try {
      videoLoadRetryCount.value = 0
      videoLoading.value = true
      videoUrlValidated.value = false
      
      // 强制重新渲染视频组件
      forceReloadVideo()
      
      // 清除旧的token和缓存
      if (currentPlayItem.value?.vodUrl) {
        const url = currentPlayItem.value.vodUrl
        // 完全重构URL，确保没有旧的token和缓存
        const baseUrl = url.split('?')[0]  // 只保留基本URL，移除所有参数
        const token = getToken()
        const randomParamName = `t_${Math.floor(Math.random() * 1000000)}`
        if (token) {
          currentPlayItem.value.vodUrl = `${baseUrl}?token=${token}&${randomParamName}=${Date.now()}`
        }
      }
      
      // 验证重构后的URL
      console.log('重试：开始验证视频URL...')
      await ensureVideoUrlValid()
    } catch (error) {
      console.error("重新加载视频失败:", error)
      ElMessage.error("重新加载视频失败，请刷新页面")
      videoLoading.value = false
    }
  }

  // 在play事件之前的初始化，防止视频组件内部错误
  const handleVideoInit = () => {
    // 重置错误状态
    videoLoadRetryCount.value = 0
    console.log("视频组件初始化成功")
  }

  // 优化视频URL处理，添加特殊处理防止blob错误
  const videoUrl = computed(() => {
    try {
      if (!currentPlayItem.value?.vodUrl) return ""
      const token = getToken()
      if (!token) {
        console.error("无效的token")
        return ""
      }
      
      const url = currentPlayItem.value.vodUrl
      
      // 如果URL已经包含token和时间戳，直接返回
      if (url.includes("token=") && url.includes("t=")) {
        return url
      }
      
      // 完全清理URL，移除所有参数
      const baseUrl = url.split('?')[0]
      
      // 添加随机数作为参数名，确保每次URL都不同，彻底避免浏览器缓存
      const randomParamName = `t_${Math.floor(Math.random() * 1000000)}`
      
      // 添加token和时间戳参数，防止缓存问题
      return `${baseUrl}?token=${token}&${randomParamName}=${Date.now()}`
    } catch (error) {
      console.error("获取视频URL错误:", error)
      return ""
    }
  })

  onMounted(async () => {
    try {
      // 设置初始加载状态
      videoLoading.value = true
      
      // 先添加热度埋点
      addCourseHotPoint()
      
      // 按顺序加载数据
      await fetchCourseData()
      
      if (!courseInfo.value) {
        ElMessage.error("课程信息加载失败")
        videoLoading.value = false
        return
      }
      
      await fetchPassageData()
      
      if (!passageList.value.length || !passageList.value[0].videoFileList?.length) {
        ElMessage.error("课程章节加载失败")
        videoLoading.value = false
        return
      }
      
      // 设置初始播放项
      currentPlayItem.value = passageList.value[0].videoFileList[0]
      
      // 处理任务相关信息
      if (route.query.taskId) {
        await getTaskInfo()
      } else {
        // 如果没有taskId，直接设置可以播放视频
        canPlayVideo.value = true
      }
      
      // 最后获取进度信息
      await getCourseProgress()
      
      // 在数据加载完成后预验证视频URL
      if (!isWordCourse.value && currentPlayItem.value?.vodUrl) {
        try {
          // 延迟处理确保DOM已完全更新
          await nextTick()
          
          // 为视频URL添加时间戳参数，防止缓存
          if (currentPlayItem.value?.vodUrl) {
            const url = currentPlayItem.value.vodUrl
            // 完全重构URL，确保没有旧的token和缓存
            const baseUrl = url.split('?')[0]
            const token = getToken()
            const randomParamName = `t_${Math.floor(Math.random() * 1000000)}`
            if (token) {
              currentPlayItem.value.vodUrl = `${baseUrl}?token=${token}&${randomParamName}=${Date.now()}`
            }
          }
          
          console.log('开始预验证视频URL...')
          await ensureVideoUrlValid()
        } catch (err) {
          console.error('预验证视频URL失败:', err)
          videoLoading.value = false
        }
      } else {
        videoLoading.value = false
      }
    } catch (error) {
      console.error("初始化课程详情页出错:", error)
      ElMessage.error("加载课程信息失败，请重试")
      videoLoading.value = false
    }
  })

  onBeforeUnmount(() => {
    // 清除所有定时器
    clearInterval(timer.value)
    clearInterval(hookTimer.value)
    clearInterval(updateTimer.value)
  })
</script>

<template>
  <div class="container">
    <div
      class="detail-header"
      :style="
        specialTenantList.includes(domainName) ? 'padding-bottom:130px' : 'margin-bottom:50px'
      "
    >
      <!-- 加载状态 -->
      <el-skeleton v-if="loading" :rows="10" animated>
        <template #template>
          <div class="w-[75%] h-523px">
            <el-skeleton-item variant="image" class="w-full h-full" />
          </div>
          <div class="w-[25%] h-523px ml-4">
            <el-skeleton-item variant="text" class="mb-4" />
            <el-skeleton-item variant="text" class="mb-4" />
            <el-skeleton-item variant="text" class="mb-4" />
          </div>
        </template>
      </el-skeleton>

      <!-- 已加载完成的内容 -->
      <template v-else>
        <!-- 已下架的课程 -->
        <div v-if="courseInfo?.courseStatus !== '2'" class="player-tip">
          <div class="player-tip-inner">
            <div class="player-tip-info">该课程已下架，请联系管理员</div>
            <div class="player-btn">
              <el-button type="warning" size="large" round @click="handleClose">返回</el-button>
            </div>
          </div>
        </div>

        <!-- 正常课程内容 -->
        <template v-else>
          <!-- 文档预览 -->
          <div v-if="isWordCourse && currentPlayItem?.fileUrl" class="document-preview">
            <div class="document-container">
              <!-- 使用单一的 iframe 预览方案 -->
              <iframe
                v-show="!docError"
                ref="dynamicIframe"
                :src="currentWordUrl"
                frameborder="0"
                class="document-iframe"
                scrolling="auto"
                @load="handleIframeLoad"
                @error="handleIframeError"
              />

              <!-- 加载状态 -->
              <div v-if="docLoading" class="doc-loading">
                <el-icon class="is-loading"><loading /></el-icon>
                <span>文档加载中...</span>
              </div>

              <!-- 错误状态 -->
              <div v-if="docError" class="doc-error">
                <el-icon><warning /></el-icon>
                <span>文档加载失败，请刷新重试</span>
                <el-button type="primary" size="small" @click="retryLoadDocument"> 重试 </el-button>
              </div>
            </div>
          </div>

          <!-- 视频播放器 -->
          <div v-else-if="currentPlayItem?.vodUrl" class="video-container">
            <!-- 视频加载状态 -->
            <div v-if="videoLoading || isPreloading" class="video-loading">
              <el-icon class="is-loading"><loading /></el-icon>
              <span>{{ isPreloading ? '正在验证视频地址...' : '视频加载中...' }}</span>
            </div>
            
            <!-- 视频播放器 -->
            <vue3VideoPlay
              v-show="!videoLoading && !isPreloading && videoUrlValidated"
              :key="videoComponentKey"
              ref="vue3VideoPlayerRef"
              height="523px"
              width="100%"
              :speedRate="
                courseInfo?.multiplyPlayback === '0' ? ['1.0'] : ['0.75', '1.0', '1.25', '1.5', '2.0']
              "
              class="detail-header-video"
              :control="true"
              :autoPlay="false"
              :poster="courseInfo?.courseImage"
              :currentTime="currentPlayItem?.mediaProgress"
              :src="safeVideoUrl"
              :type="videoType"
              :speed="isSpeed"
              @play="startPlay"
              @pause="pauseTimer"
              @loadstart="loadStart"
              @ended="endVideo"
              @timeupdate="onTimeUpdate"
              @error="handleVideoError"
              @ready="handleVideoInit"
            />
            
            <!-- 视频加载失败时的重试按钮 -->
            <div v-if="(videoLoadRetryCount >= MAX_AUTO_RETRY_COUNT || !videoUrlValidated) && !videoLoading && !isPreloading" class="video-error">
              <el-icon><warning /></el-icon>
              <span>视频加载失败，请点击重试</span>
              <el-button type="primary" size="small" @click="retryLoadVideo">重试</el-button>
            </div>
            
            <!-- 防挂机弹窗移到视频容器内部 -->
            <div
              v-if="isShowOnHookDialog"
              class="detail-header-pre"
              :style="`height:${videoHeight || 523}px;width:${videoWidth || '100%'}`"
            >
              <div class="hook-dialog">
                <div class="text-lg font-medium mb-4">您还在继续学习吗？</div>
                <el-button type="primary" class="w-24 h-10 text-base" @click="isOK">继续学习</el-button>
              </div>
            </div>
          </div>
        </template>

        <!-- 右侧章节目录 -->
        <div class="detail-header-catalogue">
          <el-scrollbar>
            <div class="detail-header-catalogue-title">章节目录</div>
            <el-divider />
            <div
              class="detail-header-catalogue-content"
              v-for="(item, index) in passageList"
              :key="item.coursePassageId"
            >
              <div class="passage-title">{{ index + 1 }} . {{ item.coursePassageName }} </div>
              <div
                class="passage-file-list"
                v-if="item.videoFileList && item.videoFileList.length > 0"
              >
                <div
                  @click="setActive(item2, index, index2)"
                  class="passage-file-item"
                  v-for="(item2, index2) in item.videoFileList"
                  :key="item2.passageId"
                  :class="{ active: currentPlayItem?.passageId === item2.passageId }"
                >
                  <!-- 当章节下只有一个视频时，视频只展示该章节名称 -->
                  <div class="left" v-if="item.videoFileList?.length === 1">
                    {{ item.coursePassageName }}
                  </div>
                  <div class="left" v-else> {{ item2.fileName || getFileName(item2.fileUrl) }}</div>
                  <div class="right">{{ schedule(item2.passageId) || 0 }}%</div>
                </div>
              </div>
              <!-- 防灾减灾 -->
              <div class="completion-icon" v-if="learnedStatus">
                <i class="iconfont icon-yiwancheng"> </i>
              </div>
            </div>
          </el-scrollbar>
        </div>
      </template>

      <video class="video" v-show="false" ref="cameraRef" muted autoplay width="300" height="200">
      </video>
      <canvas class="video" v-show="false" ref="canvasRef" width="300" height="200"></canvas>
      <AnswerDialog ref="answerDialogRef" @done="answerQuestionDone" />
    </div>
    <template v-if="!specialTenantList.includes(domainName)">
      <detailIntro
        :courseItem="courseInfo"
        :rateVal="gradeVal"
        :learnedStatus="learnedStatus"
        @fetch-data="fetchCourseData"
      />
      <detailBody :courseItem="courseInfo" />
    </template>
    <!-- 添加人脸识别组件 -->
    <BaseFaceVerification
      v-model:visible="showFaceVerify"
      :record-credit="taskInfo?.recordCredit"
      @verify-success="handleVerifySuccess"
      @verify-fail="handleVerifyFail"
      :task-id="route.query.taskId"
      :sub-task-id="route.query.id"
      :sub-task-name="courseInfo?.courseName"
    />
  </div>
</template>

<style lang="less" scoped>
  :deep(.el-divider--horizontal) {
    margin: 12px 0;
  }
  .detail-header {
    display: flex;
    padding-top: 20px;

    .video-container {
      width: 75%;
      height: 523px;
      position: relative;
    }

    .video-loading, 
    .video-error {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background-color: rgba(0, 0, 0, 0.7);
      z-index: 10;
      color: white;
      gap: 12px;
    }

    .video-loading .el-icon {
      font-size: 36px;
      color: var(--el-color-primary);
    }

    .video-error .el-icon {
      font-size: 36px;
      color: var(--el-color-danger);
    }

    .player-tip {
      height: 523px;
      width: 75%;
      background-image: url("@/assets/images/vipPlayerbg.jpg");
      background-repeat: no-repeat;
      background-size: cover;
      position: relative;

      .player-tip-inner {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        position: absolute;
        top: 40%;
        left: 0;
        right: 0;
        width: 100%;
        color: #fff;

        .player-tip-info {
          margin-bottom: 22px;
          font-size: 17px;
          text-align: center;
        }

        .player-btn {
          :deep(.el-button) {
            width: 120px;
          }
        }
      }
    }

    .detail-header-pre {
      position: absolute;
      z-index: 99;
      top: 0;
      left: 0;
      width: 100% !important;
      height: 100% !important;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: rgba(0, 0, 0, 0.6);
      /* 确保弹窗只覆盖视频播放器区域 */
      max-width: 100%;
      max-height: 523px;

      .hook-dialog {
        width: 300px;
        min-height: 180px;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        animation: popup 0.3s ease-out, attention 2s 0.5s infinite;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 24px;
      }
      
      @keyframes popup {
        0% {
          transform: scale(0.8);
          opacity: 0;
        }
        100% {
          transform: scale(1);
          opacity: 1;
        }
      }
      
      @keyframes attention {
        0%, 100% {
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        }
        50% {
          box-shadow: 0 4px 25px rgba(64, 158, 255, 0.4);
        }
      }
    }

    .detail-header-catalogue {
      width: 25%;
      background-color: #f1f2f3;
      height: 523px;
      position: relative;

      .detail-header-catalogue-title {
        margin-top: 20px;
        margin-left: 30px;
      }

      .detail-header-catalogue-content {
        margin-bottom: 10px;
        border-bottom: 1px solid #dcdfe6;
        padding: 0 20px;
        font-weight: 700;
        display: flex;
        flex-direction: column;

        .passage-title {
          display: flex;
          justify-content: space-between;
          font-size: 14px;
          overflow: hidden;
          text-overflow: ellipsis;
          width: 100%;
        }

        .passage-file-list {
          padding-bottom: 5px;
          .passage-file-item {
            display: flex;
            justify-content: space-between;
            margin: 5px 30px 5px 22px;
            .left {
              flex: 1;
              padding-right: 10px;
              overflow: hidden;
              text-overflow: ellipsis;
              font-weight: 500;
            }
          }
        }
      }

      .passage-file-item:hover {
        color: #40acea;
        transition: 0.2s;
        cursor: pointer;
      }

      .completion-icon {
        position: absolute;
        top: 25%;
        left: 30%;

        .iconfont {
          font-size: 130px;
          font-weight: normal;
          color: #d81e06;
        }
      }
    }
  }

  .active {
    color: #40acea;
  }

  @keyframes shake {
    0% {
      transform: rotate(0deg) scale(1) perspective(1px);
    }
    10% {
      transform: rotate(15deg) scale(1.1) perspective(1px);
    }
    20% {
      transform: rotate(-10deg) scale(1.1) perspective(1px);
    }
    30% {
      transform: rotate(5deg) scale(1.2) perspective(1px);
    }
    40% {
      transform: rotate(-5deg) scale(1.2) perspective(1px);
    }
    50%,
    100% {
      transform: rotate(0deg) scale(1) perspective(1px);
    }
  }

  .el-button .custom-loading .circular {
    margin-right: 6px;
    width: 18px;
    height: 18px;
    animation: loading-rotate 2s linear infinite;
  }
  .el-button .custom-loading .circular .path {
    animation: loading-dash 1.5s ease-in-out infinite;
    stroke-dasharray: 90, 150;
    stroke-dashoffset: 0;
    stroke-width: 2;
    stroke: var(--el-button-text-color);
    stroke-linecap: round;
  }

  .document-preview {
    width: 75%;
    height: 523px;
    position: relative;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: center;

    .document-container {
      width: 100%;
      max-width: 1000px; // 设置最大宽度
      height: 100%;
      position: relative;
      overflow: hidden;
      padding: 20px;
      box-sizing: border-box;

      .document-iframe {
        width: 100%;
        height: 100%;
        border-radius: 4px;
        background: #fff;
      }

      .doc-loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        background: rgba(255, 255, 255, 0.9);
        padding: 20px;
        border-radius: 4px;

        .el-icon {
          font-size: 24px;
          color: var(--el-color-primary);
        }

        span {
          color: var(--el-text-color-secondary);
          font-size: 14px;
        }
      }

      .doc-error {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;
        background: #fff;
        padding: 24px;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

        .el-icon {
          font-size: 32px;
          color: var(--el-color-danger);
        }

        span {
          color: var(--el-text-color-regular);
          font-size: 14px;
        }
      }
    }
  }

  // 添加骨架屏样式
  :deep(.el-skeleton) {
    display: flex;
    width: 100%;
    padding: 20px;
  }
</style>
