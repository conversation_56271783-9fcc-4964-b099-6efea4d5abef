// 考试安排
export type ExamListInfo = ExamItem[]
export type ExamItem = {
  arrangeId: number
  baseId: number
  baseName: string
  startTime: string
  endTime: string
  examStatus: string
  examStatusDesc: string
  remark?: string
  taskId?: string
}

// 考试详情信息
export type ExamDetailInfoType = {
  baseName: string
  examConfig: ExamConfigType
  paperName: string
  paperId: number
}

// 考试规则、配置
export type ExamConfigType = {
  examDurationLimit: number
  switchingTimes?: number
  submintMinimumNumber: number
  beFiveLimitMinute: number
  lowestScore?: number
  minimumScoreRate?: number
  isViewScore: boolean
  isViewAnswer: boolean
  ruleMcq: string
  ruleMcqScore?: number
  ruleCloze: string
  ruleUserName: string
  limitNumber: number
  faceCapture: string
  recordCredit: string
}

// 考试记录详情
export type examRecordType = {
  startTime: string
  endTime: string
  remainExamCount: number
  examPaperAnswer: examPaperAnswerType
  examPaperAnswers: examPaperAnswerType[]
}

export type examPaperAnswerType = {
  baseId: number
  arrangeId: number
  paperAnswerId: number
  userPaperScore: number
  isPass: string
  startTime: string
  submitTime: string
  examStatus: string
  size?: number
}

// 试卷详情信息
export type paperQuestionType = {
  paperAnswerId: string
  startTime: string
  paperScore: number
  signFlag?: string
}

// 试卷详情-题型、试题信息
export type paperTacticsType = TacticsItem[]
export type TacticsItem = {
  examQuestions: ExamQuestionsType
}

export type ExamQuestionsType = ExamQuestionItem[]
export type ExamQuestionItem = {
  questionId: string
  questionType: string
  questionName: string
  questionName: string
  userAnswer: string | Array<string> | Array
  answer: string
  questionScore: number
  userQuestionScore: number
  blankCount: number
  fillAnswerList: Array
  analysis: string
}

// 试卷解析详情
export type completedPaperType = {
  paperName: string
  userPaperScore: number
  paperScore: number
}
