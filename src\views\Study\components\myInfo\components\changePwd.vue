<script setup lang="ts">
  import { ref, reactive, getCurrentInstance } from "vue"
  import { getPublicKey } from "@/api/login"
  import { encrypt } from "@/utils/jsencrypt"
  import { updateUserPwd, resetUserPwd } from "@/api/system/user"
  import { ElMessage } from "element-plus"
  import useUserStore from "@/store/modules/user"
  import { pushToAdmin } from "@/utils/common"

  const userStore = useUserStore()
  const { proxy } = getCurrentInstance()!

  const user = reactive({
    oldPassword: undefined,
    newPassword: undefined,
    confirmPassword: undefined
  })

  const equalToPassword = (rule, value, callback) => {
    if (user.newPassword !== value) {
      callback(new Error("两次输入的密码不一致"))
    } else {
      callback()
    }
  }

  const rules = ref({
    oldPassword: [{ required: true, message: "旧密码不能为空", trigger: "blur" }],
    newPassword: [
      { required: true, message: "新密码不能为空", trigger: "blur" },
      { min: 6, max: 20, message: "长度在 6 到 20 个字符", trigger: "blur" }
    ],
    confirmPassword: [
      { required: true, message: "确认密码不能为空", trigger: "blur" },
      { required: true, validator: equalToPassword, trigger: "blur" }
    ]
  })

  const fetchPublicKey = () => {
    return new Promise((resolve, reject) => {
      getPublicKey()
        .then(res => {
          resolve(res)
        })
        .catch(error => {
          reject(error)
        })
    })
  }

  const submit = () => {
    ;(proxy as any).$refs.pwdRef.validate(async valid => {
      if (valid) {
        await resetUserPwd(userStore.userInfo.userId, user.newPassword)
        proxy.$modal.msgSuccess("修改成功")
        userStore.logOutAction()
        pushToAdmin()
        // fetchPublicKey().then((res: any) => {
        //   let publicKey = res.publicKey
        //   const oldPassword = encrypt(user.oldPassword, publicKey)
        //   const newPassword = encrypt(user.newPassword, publicKey)
        //   updateUserPwd(oldPassword, newPassword).then(response => {
        //     ElMessage({
        //       message: "修改成功！",
        //       type: "success"
        //     })
        //     userStore.logOutAction()
        //     pushToAdmin()
        //   })
        // })
      }
    })
  }

  const dialogVisible = ref(false)
  const openDialog = () => {
    dialogVisible.value = true
  }

  defineExpose({ openDialog })
</script>

<template>
  <el-dialog v-model="dialogVisible" title="修改密码" width="35%">
    <el-form :model="user" :rules="rules" label-width="80px" style="max-width: 560px" ref="pwdRef">
      <el-form-item label="旧密码" prop="oldPassword">
        <el-input
          v-model="user.oldPassword"
          placeholder="请输入旧密码"
          type="password"
          show-password
        />
      </el-form-item>
      <el-form-item label="新密码" prop="newPassword">
        <el-input
          v-model="user.newPassword"
          placeholder="请输入新密码"
          type="password"
          show-password
        />
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input
          v-model="user.confirmPassword"
          placeholder="请确认新密码"
          type="password"
          show-password
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submit" style="width: 45%">保存</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<style lang="less" scoped></style>
