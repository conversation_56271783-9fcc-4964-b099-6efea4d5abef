apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: beckwell-web-learning
  name: beckwell-web-learning
  namespace: beckwell-edu
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  selector:
    matchLabels:
      app: beckwell-web-learning
  strategy:
    rollingUpdate:
      maxSurge: 50%
      maxUnavailable: 50%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: beckwell-web-learning
    spec:
      imagePullSecrets:
        - name: harbor
      containers:
        - image: $REGISTRY/$DOCKERHUB_NAMESPACE/beckwell-web-learning:SNAPSHOT-$BUILD_NUMBER
          imagePullPolicy: Always
          name: app
          ports:
            - containerPort: 80
              protocol: TCP
          resources:
            limits:
              cpu: 300m
              memory: 600Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: beckwell-web-learning
  name: beckwell-web-learning
  namespace: beckwell-edu
spec:
  ports:
    - name: http
      port: 80
      protocol: TCP
      targetPort: 80
      nodePort: 32700
      
  selector:
    app: beckwell-web-learning
  sessionAffinity: None
  type: NodePort