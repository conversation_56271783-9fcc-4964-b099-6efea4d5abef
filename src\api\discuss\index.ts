/*
 * @Description:
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON><PERSON>
 * @Date: 2023-05-15 15:19:12
 * @LastEditTime: 2023-05-15 17:07:54
 */
import request from "@/utils/request"

// 评论列表
export function listDiscuss(params) {
  return request({
    url: "/course/course-discuss/list",
    method: "get",
    params
  })
}

// 添加评论
export function addDiscuss(data) {
  return request({
    url: "/course/course-discuss",
    method: "post",
    data: data
  })
}
