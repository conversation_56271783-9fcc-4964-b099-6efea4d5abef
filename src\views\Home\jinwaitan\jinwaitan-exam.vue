<template>
  <div class="jinwaitan-exam">
    <div class="question-type"
      ><dict-tag
        :options="exam_question_type"
        :value="courseQuestionLinkList[questionIndex]?.questionType"
      />题
    </div>
    <div class="question-number">
      <div>第 {{ questionIndex + 1 }} 题 / 共 {{ courseQuestionLinkList?.length }} 题</div>
    </div>
    <div class="question-name">{{ courseQuestionLinkList[questionIndex]?.questionName }}</div>
    <div class="question-option" v-if="!showAnswer">
      <div
        class="option-item"
        v-for="(item, index) in countSubstr(
          courseQuestionLinkList[questionIndex]?.questionName,
          '（____）'
        )"
        v-if="courseQuestionLinkList[questionIndex]?.questionType === 'F'"
      >
        <div>填空{{ item }}：</div>
        <el-input v-model="fillingQS[item - 1]" class="input"></el-input>
      </div>

      <el-radio-group
        v-model="inputValue"
        v-if="
          courseQuestionLinkList[questionIndex]?.questionType === 'S' ||
          courseQuestionLinkList[questionIndex]?.questionType === 'J'
        "
        class="group"
      >
        <template v-for="choice in 11">
          <el-radio
            class="group-item"
            v-if="courseQuestionLinkList[questionIndex]?.[`item${choice}`]"
            :name="String.fromCharCode(64 + choice)"
            :key="choice"
            :label="`${String.fromCharCode(64 + choice)}`"
            >{{
              `${String.fromCharCode(64 + choice)}. &nbsp;${
                courseQuestionLinkList[questionIndex]?.[`item${choice}`]
              }`
            }}</el-radio
          >
        </template>
      </el-radio-group>

      <el-checkbox-group
        v-model="inputValue"
        class="group"
        v-if="courseQuestionLinkList[questionIndex]?.questionType === 'M'"
      >
        <template v-for="multiChoice in 11">
          <el-checkbox
            class="group-item"
            v-if="courseQuestionLinkList[questionIndex]?.[`item${multiChoice}`]"
            :key="multiChoice"
            :label="`${String.fromCharCode(64 + multiChoice)}`"
            >{{
              `${String.fromCharCode(64 + multiChoice)}. &nbsp;${
                courseQuestionLinkList[questionIndex]?.[`item${multiChoice}`]
              }`
            }}</el-checkbox
          >
        </template>
      </el-checkbox-group>
    </div>

    <div class="question-choice" v-else>
      <div
        class="option-item"
        v-for="item in countSubstr(courseQuestionLinkList[questionIndex].questionName, '（____）')"
        v-if="courseQuestionLinkList[questionIndex].questionType === 'F'"
      >
        <div>填空{{ item }}：</div>
        <el-input
          :value="courseQuestionLinkList[questionIndex].answer.split('::')[item - 1]"
          class="input"
          disabled
        ></el-input>
      </div>

      <template v-for="choice in 11">
        <div class="choice-item" v-if="courseQuestionLinkList[questionIndex][`item${choice}`]">
          <div class="index">
            <span
              v-if="
                isCorrect(courseQuestionLinkList[questionIndex], String.fromCharCode(64 + choice))
              "
            >
              <img src="@/assets/images/correct.png" alt="" />
            </span>
            <span
              v-else-if="
                isWrong(courseQuestionLinkList[questionIndex], String.fromCharCode(64 + choice))
              "
            >
              <img src="@/assets/images/error.png" alt="" />
            </span>
            <span class="letterOnly" v-else>{{ String.fromCharCode(64 + choice) }}</span>
          </div>
          <div class="content">
            {{ courseQuestionLinkList[questionIndex][`item${choice}`] }}
          </div>
        </div>
      </template>
    </div>

    <div class="question-switch">
      <div>
        <el-button v-if="questionIndex !== 0" @click="changeQuestionIndex('previous')"
          >上一题</el-button
        >
      </div>
      <el-button @click="showOrHide">{{ showAnswer ? "隐藏答案" : "显示答案" }}</el-button>

      <!-- <div class="skip">
        <div class="left">前往</div>
        <div class="right"
          ><span>第</span>
          <el-input
            v-model="skipIndex"
            class="right-input"
            @keyup.enter="changeQuestionIndex(skipIndex - 1)"
            @blur="changeQuestionIndex(skipIndex - 1)"
          ></el-input>
          <span>题</span>
        </div>
      </div> -->

      <div>
        <el-button
          v-if="questionIndex !== courseQuestionLinkList.length - 1"
          @click="changeQuestionIndex('next')"
          >下一题</el-button
        >
      </div>
    </div>
    <template v-if="showAnswer">
      <div class="question-answer">
        <div>答案：</div>
        <div>
          <div
            class="answer-item"
            v-for="(item, index) in courseQuestionLinkList[questionIndex].answer.split('::')"
          >
            <div v-if="courseQuestionLinkList[questionIndex].questionType === 'F'"
              >填空{{ index + 1 }}
            </div>
            <div>{{ item }}</div>
          </div>
        </div>
      </div>
      <div class="analysis">
        解析：<span class="analysis-content">
          {{ courseQuestionLinkList[questionIndex].analysis }}
        </span>
      </div>
    </template>
  </div>
</template>
<script lang="ts" setup>
  import { jinwaitanQuestionList } from "@/api/onlineExam"
  import { countSubstr } from "@/utils/common"

  const { proxy } = getCurrentInstance()!
  const { exam_question_type } = proxy!.useDict("exam_question_type")
  const courseQuestionLinkList = ref<any>([])
  const inputValue = ref()
  const fillingQS = ref([])
  const questionIndex = ref(0)
  const showAnswer = ref(false)
  const skipIndex = ref(1)

  const changeQuestionIndex = type => {
    showAnswer.value = false
    inputValue.value = undefined
    fillingQS.value = []
    if (type === "previous") questionIndex.value--
    else if (type === "next") questionIndex.value++
    else questionIndex.value = type * 1
  }

  const showOrHide = () => {
    if (showAnswer.value) showAnswer.value = false
    else showAnswer.value = true
  }

  // 正确的选项
  function isCorrect(item, choice) {
    if (item.questionType === "S" || item.questionType === "J") {
      return item.answer === choice
    } else if (item.questionType === "M") {
      if (item.answer.includes(",")) {
        return item.answer.split(",").indexOf(choice) > -1
      }
      return item.answer === choice
    }
    return false
  }

  // 错误的选项
  function isWrong(item, choice) {
    if (!inputValue.value) return false
    if (item.questionType === "S" || item.questionType === "J") {
      return inputValue.value === choice && inputValue.value !== item.answer
    } else if (item.questionType === "M") {
      return inputValue.value.includes(choice) && !item.answer.includes(choice)
    }
    return false
  }

  const fetchData = async () => {
    const res = await jinwaitanQuestionList("2058")
    // const res = await jinwaitanQuestionList("2068")   // 测试环境测试使用
    courseQuestionLinkList.value = res.data
  }

  onMounted(() => {
    fetchData()
  })
</script>

<style lang="less" scoped>
  :deep(.el-radio__label) {
    font-size: 18px;
  }
  :deep(.el-checkbox__label) {
    font-size: 18px;
  }
  .jinwaitan-exam {
    height: calc(100vh - 70px);
    padding: 50px;
    margin: 0 auto;
    position: relative;
    width: 1240px;
    .question-type {
      display: flex;
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 30px;
    }

    .question-number {
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 30px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .question-name {
      font-size: 18px;
      margin-bottom: 30px;
    }

    .question-option {
      margin-bottom: 30px;
      font-size: 16px;
      .option-item {
        display: flex;
        align-items: center;

        > div {
          margin-right: 10px;
          margin-bottom: 30px;
        }
        .input {
          width: 300px;
        }
      }

      .group {
        display: flex;
        flex-direction: column;
        align-items: start;
        .group-item {
          margin-bottom: 12px;
        }
      }
    }

    .question-choice {
      margin-bottom: 42px;
      .option-item {
        display: flex;
        align-items: center;

        > div {
          margin-right: 10px;
          margin-bottom: 30px;
        }
        .input {
          width: 300px;
        }
      }
      .choice-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        height: 32px;
        font-size: 18px;

        .index {
          width: 24px;
          height: 24px;

          .letterOnly {
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 50% 50%;
            width: 22px;
            height: 22px;
            border: 1px solid #ccc;
          }
        }

        .content {
          padding-left: 10px;
          // font-size: 16px;
        }
      }
    }

    .question-switch {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 30px;
      > div {
        text-align: center;
        width: 25%;
      }
      .skip {
        display: flex;
        align-items: center;
        justify-content: center;
        .left {
          margin-right: 10px;
        }
        .right {
          display: flex;
          align-items: center;
          .right-input {
            width: 70px;
            margin: 0 10px;
          }
        }
      }
    }

    .question-answer {
      display: flex;
      font-size: 18px;

      > :first-child {
        margin-right: 20px;
      }
      .answer-item {
        color: #000;
        margin-bottom: 30px;
        display: flex;
        > div {
          margin-right: 20px;
          display: flex;
          color: #2d79ae;
        }
      }
    }
    .analysis {
      font-size: 18px;
      .analysis-content {
        margin-left: 20px;
        color: #2d79ae;
      }
    }
  }
</style>
