/*
 * @Description: main.ts
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-14 09:43:19
 * @LastEditTime: 2024-11-29 14:46:10
 */
import { createApp } from "vue"
import App from "./App.vue"

// 统一不同浏览器标签默认样式
import "normalize.css"
// 按照项目需求，提供自己的公用样式
import "@/assets/styles/common.less"
//导入视频css文件
import "video.js/dist/video-js.css"
// 导入element-plus
import ElementPlus from "element-plus"
import zhCn from "element-plus/dist/locale/zh-cn.mjs"
import "element-plus/dist/index.css"
import * as ElementPlusIconsVue from "@element-plus/icons-vue"

import "./permission" // permission control
import "@/utils/console.ts"
import plugins from "./plugins" // plugins
import router from "./router"
import { createPinia } from "pinia"
import BaseUI from "./components/BaseUI"
import piniaPluginPersistedstate from "pinia-plugin-persistedstate"
import { useDict } from "@/utils/dict"
import { download } from "@/utils/request"
import vue3videoPlay from "goldenzqqq-vue3-video-play" // 引入组件
import "goldenzqqq-vue3-video-play/dist/style.css" // 引入css
// 字典标签组件
import DictTag from "@/components/BaseUI/DictTag/index.vue"
import "virtual:uno.css"

// 创建 pinia 实例
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)

const app = createApp(App)

// 全局方法挂载
app.config.globalProperties.useDict = useDict
// @ts-ignore
app.config.globalProperties.download = download
app.config.globalProperties.router = router
app.use(plugins)
// 全局组件挂载
app.component("DictTag", DictTag)

// 使用 @element-plus/icons-vue
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}
// 使用 vueRouter
app.use(router)
// 使用 pinia
app.use(pinia)
// 使用 BaseUI
app.use(BaseUI)
app.use(vue3videoPlay)
app.use(ElementPlus, {
  locale: zhCn
})
app.mount("#app")
