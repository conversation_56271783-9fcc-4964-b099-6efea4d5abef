<!--
 * @Description: Layout
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-17 08:49:10
 * @LastEditTime: 2025-02-25 10:17:38
-->
<script setup lang="ts">
  import { specialTenantList, unepTenantList, noNeedLayOutDomainList } from "@/utils/constant"
  import AppHeader from "./components/layout-header.vue"
  import AppFooter from "./components/layout-footer.vue"
  import AppHeaderSticky from "./components/layout-header-sticky.vue"
  import AppTopnav from "./components/layout-topnav.vue"
  import UnepTopNav from "./components/unep-topnav.vue"
  import TownTopNav from "./components/town-topnav.vue"
  import CjaqTopNav from "./components/cjaq-topnav.vue"
  import PtTopNav from "./components/pt-topnav.vue"
  import useTenantStore from "@/store/modules/tenant"
  // import useStore from "@/store";
  // const { home } = useStore();
  // 在父组件发送请求更合理，分类请求只需要发一次即可
  // home.getAllCategory();
  const route = useRoute()
  const { domainName } = storeToRefs(useTenantStore())

  const noNeedHFList = ["/examing", "/examed"]
  const isShowHF = computed(() => {
    return (
      noNeedHFList.indexOf(route.path) === -1 && !noNeedLayOutDomainList.includes(domainName.value)
    )
  })
  const noNeedFooterList = ["/examed"]
  const isShowFooter = computed(() => {
    return (
      noNeedFooterList.indexOf(route.path) === -1 &&
      !noNeedLayOutDomainList.includes(domainName.value)
    )
  })
</script>

<template>
  <template v-if="isShowHF">
    <UnepTopNav v-if="unepTenantList.includes(domainName)" />
    <TownTopNav v-else-if="domainName === 'town'" />
    <CjaqTopNav v-else-if="domainName === 'cjaq'" />
    <PtTopNav v-else-if="domainName === 'pt'" />
    <AppTopnav v-else />
    <!-- <AppHeader /> -->
  </template>
  <!-- <AppHeaderSticky /> -->
  <main
    class="app-body"
    :style="{
      paddingTop: isShowHF ? '70px' : '0',
      background: '#f4fafe'
    }"
  >
    <RouterView :key="$route.fullPath" />
  </main>
  <AppFooter v-if="isShowHF && isShowFooter && !specialTenantList.includes(domainName)" />
  <BaseBackTop></BaseBackTop>
</template>

<style lang="less" scoped>
  .app-body {
    min-height: 100vh;
    // background-color: #f4fafe;
  }
</style>
