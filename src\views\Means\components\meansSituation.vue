<!--
 * @Description: 资料情况展示
 * @Author: <PERSON>
 * @LastEditors: sun<PERSON><PERSON>
 * @Date: 2023-05-08 11:50:18
 * @LastEditTime: 2023-09-04 17:16:05
-->

<script lang="ts" setup name="meansSituation">
  import { ref, getCurrentInstance, computed } from "vue"
  import { getManageStatisticInfo } from "@/api/trainingTask/means"
  import type { meansCategoryItemType } from "@/types"

  const meansTypeListData = ref<meansCategoryItemType[]>([
    {
      icon: "icon-shiyongwendang",
      label: "文档",
      value: 1,
      count: 0
    },
    {
      icon: "icon-shipin",
      label: "视频",
      value: 2,
      count: 0
    },
    {
      icon: "icon-music",
      label: "音频",
      value: 3,
      count: 0
    },
    {
      icon: "icon-zip",
      label: "ZIP",
      value: 4,
      count: 0
    }
  ])

  const totalCount = computed(() => {
    return meansTypeListData.value.reduce((prev, curr) => {
      return prev + curr.count
    }, 0)
  })

  const fetchData = async () => {
    const { data } = await getManageStatisticInfo()
    if (!data) return
    Object.keys(data).forEach(key => {
      if (!key) return
      let realItem = meansTypeListData.value.find(item => item.value === Number(key))
      if (realItem) {
        realItem!.count = data[key] || 0
      }
    })
  }

  fetchData()
</script>

<template>
  <div class="means-situation">
    <div class="situation-area">
      <div class="Hzqh1">资料情况</div>

      <div class="total">
        共有<span class="total-number">{{ totalCount }}</span
        >个资料
      </div>
      <div class="type-list">
        <template v-for="item in meansTypeListData">
          <div class="type-item">
            <div class="type-icon">
              <i :class="`iconfont ${item.icon}`"></i>
            </div>
            <div class="type-name">{{ item.label }}</div>
            <div class="type-number">{{ item.count }}</div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
  .means-situation {
    margin-top: 20px;

    .Hzqh1 {
      margin-left: 6px;
    }
    .situation-area {
      background-color: #fff;
      border: 1px solid #e8e8e8;
      border-radius: 7px;

      width: 300px;
      padding: 10px 0 0 15px;
      flex-grow: inherit;

      .total {
        font-size: 14px;
        padding: 5px 0 8px 12px;
        font-weight: bold;

        .total-number {
          font-size: 26px;
          font-weight: bold;
          color: @warmOrange;
          padding: 0 6px;
        }
      }

      .type-list {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        align-items: center;
        padding-left: 10px;
        margin-bottom: 30px;
        .type-item {
          display: flex;
          align-items: center;
          height: 60px;
          min-width: calc(100% / 2);
          font-size: 18px;

          .type-icon {
            display: flex;
            justify-content: center;
            align-items: center;
            color: #ffffff;
            background-color: #bdc6d9;
            padding: 2px 0;
            border-radius: 4px;
            margin: 0 8px 0 0;
            width: 30px;
            height: 30px;
            box-sizing: border-box;
            text-align: center;
          }

          .type-name {
            display: flex;
            justify-content: center;
            width: 40px;
            margin-right: 10px;
            font-size:16px;
          }

          .type-number {
            font-family: Arial, Helvetica, sans-serif;
          }
        }
      }
    }
  }
</style>
