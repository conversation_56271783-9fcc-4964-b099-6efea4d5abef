<!--
 * @Description: 首页-学习排行榜
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-08-24 14:12:37
 * @LastEditTime: 2024-04-25 16:31:22
-->

<script lang="ts" setup>
  import { formatSeconds } from "@/utils/common"
  import { getLearningRank } from "@/api/home"
  import useTenantStore from "@/store/modules/tenant"
  import dayjs from "dayjs"

  const { proxy } = getCurrentInstance()!
  const { domainName } = storeToRefs(useTenantStore())
  const rankingTableData = ref()
  const fetchRankingData = async () => {
    const { rows } = await getLearningRank()
    rankingTableData.value = rows || []
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy!.download(
      "course/study-log/rank-export",
      {},
      `学习排行榜_${dayjs().format("YYYY-MM-DD HH:mm:ss")}.xlsx`
    )
  }

  fetchRankingData()
</script>
<template>
  <div class="rank_con">
    <div class="learning-ranking">
      <h2 class="title">学习排行榜</h2>
      <div class="btn-container" v-if="domainName === 'taibao'">
        <el-button class="export-btn" type="warning" @click="handleExport" style="width: 80px">
          导出
        </el-button>
      </div>
      <div class="ranking-table">
        <el-table
          size="large"
          :data="rankingTableData"
          style="width: 100%"
          :header-cell-style="{
            backgroundColor: '#f58335',
            color: 'white',
            fontSize: '18px'
          }"
          :row-style="{
            height: '60px'
          }"
        >
          <el-table-column label="排名" width="150" align="center">
            <template #default="scope">
              <img class="rank-img-1" v-if="scope.$index === 0" src="@/assets/images/one.png" />
              <img
                class="rank-img-2"
                v-else-if="scope.$index === 1"
                src="@/assets/images/two.png"
              />
              <img
                class="rank-img-3"
                v-else-if="scope.$index === 2"
                src="@/assets/images/three.png"
              />
              <span v-else>{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="姓名" prop="userName" align="center" />
          <el-table-column
            v-if="domainName === 'taibao'"
            label="分公司"
            prop="orgName"
            align="center"
          />
          <el-table-column label="所在部门" prop="deptName" align="center" />
          <el-table-column label="学习时长" align="center">
            <template #default="scope">
              {{ formatSeconds(scope.row.lastDeltaDuration) }}
            </template>
          </el-table-column>
          <el-table-column
            :label="domainName === 'taibao' ? '最高成绩' : '平均成绩'"
            prop="avgScore"
            align="center"
          />
        </el-table>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
  :deep(.el-table__inner-wrapper::before) {
    background: none;
  }
  :deep(.el-table__body) {
    tr:last-child {
      td {
        border-bottom: none;
      }
    }
  }
  .rank_con {
    padding: 30px 0 60px 0;

    background: rgb(247, 248, 252);
  }
  .learning-ranking {
    width: 1240px;
    margin: 0 auto;
    .title {
      color: #333;
      font-size: 30px;
      font-weight: 500;
      line-height: 1;
      margin-bottom: 32px;
      text-align: center;
    }

    .btn-container {
      display: flex;
      flex-direction: row-reverse;
      margin-bottom: 10px;
      margin-right: 10px;
    }
  }
  .ranking-table {
    /* border: 1px solid #ccc; */
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 8px 0 rgba(95, 101, 105, 0.1);
  }
  .rank-img-1 {
    width: 35px;
    height: 35px;
  }

  .rank-img-2 {
    width: 30px;
    height: 30px;
  }

  .rank-img-3 {
    width: 25px;
    height: 25px;
  }
</style>
