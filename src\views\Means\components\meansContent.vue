<script lang="ts" setup name="menasContent">
  import useStore from "@/store"
  import { editRelateNumber } from "@/api/trainingTask/means"

  const { proxy } = getCurrentInstance()!
  const { sys_flie_type } = proxy!.useDict("sys_flie_type")!
  const radioValue = ref("")
  const { means } = useStore()
  const { meansList, dataTotal, pageNum, pageSize, manageType } = storeToRefs(means)
  const filePreviewRef = ref<any>(null)
  const filePreview = async item => {
    await editRelateNumber({ manageId: item.manageId, viewNumber: 0 })
    filePreviewRef.value.fileLoad(item)
  }

  const downloadCountRecord = async manageId => {
    await editRelateNumber({ manageId, downloadNumber: 0 })
  }

  const changeRadio = value => {
    if (radioValue.value === value) {
      radioValue.value = ""
    } else {
      radioValue.value = value
    }
    manageType.value = radioValue.value
    means.getMeansInfo()
  }

  const getAssetURL = url => {
    if (url) return url
    return new URL(`@/assets/images/deault_means.png`, import.meta.url).href
  }

  const filePreviewClose = () => {
    means.getMeansInfo()
    means.getMeansCatalogueList()
  }
</script>

<template>
  <div class="means-content">
    <div class="content-list">
      <div class="content-list-search">
        <BaseArrowSort
          class="arrow"
          :data-list="[
            { name: 'create_time', label: '按时间' },
            { name: 'like_number', label: '点赞数' },
            { name: 'view_number', label: '浏览数' }
          ]"
          @sort="means.getMeansInfo"
          :isReset="true"
        ></BaseArrowSort>
        <div class="content-list-radio">
          <el-radio-group v-model="radioValue" class="ml-4">
            <el-radio
              v-for="item in sys_flie_type"
              :label="item.value"
              size="large"
              @click.native.prevent="changeRadio(item.value)"
              >{{ item.label }}</el-radio
            >
          </el-radio-group>
        </div>
      </div>
      <el-row :guter="20" v-if="meansList.length > 0">
        <template v-for="(item, index) in meansList">
          <el-col :span="12" class="col">
            <img class="new" src="@/assets/images/new.png" alt="" v-if="!item.viewNumber" />
            <el-card shadow="hover" class="content-item" @click="filePreview(item)">
              <template #header>
                <div class="item-main">
                  <img class="image" :src="getAssetURL(item.cover)" alt="" />
                  <div class="item-right">
                    <div class="right-top">
                      <i class="iconfont icon-word"></i>
                      <p class="title ellipsis-2">{{ item.manageName }}</p>
                    </div>
                    <div class="right-bottom">
                      <div class="share">分享人员：{{ item.createBy }}</div>
                      <div class="uploadTime"> 上传时间：{{ item.createTime }} </div>
                    </div>
                  </div>
                </div>
              </template>
              <div class="number-statistic">
                <div class="download-number">
                  <i class="iconfont icon-view"></i>
                  {{ item.viewNumber || 0 }}
                </div>
                <div class="view-number">
                  <i class="iconfont icon-xiazai"></i>
                  {{ item.downloadNumber || 0 }}
                </div>
                <div class="view-number">
                  <i class="iconfont icon-collect"></i>
                  {{ item.likeNumber || 0 }}
                </div>
              </div>
            </el-card>
          </el-col>
        </template>
      </el-row>

      <BasePagination
        v-if="meansList.length > 0"
        :total="dataTotal"
        v-model:pageNum="pageNum"
        v-model:pageSize="pageSize"
        @pagination="means.getMeansInfo"
      />
      <el-empty v-else description="暂无数据" :image-size="200" />

      <BaseFilePreview
        ref="filePreviewRef"
        @clickDownload="downloadCountRecord"
        @closeDialog="filePreviewClose"
        :meansList="meansList"
      />
    </div>
  </div>
</template>

<style scoped lang="less">
  .means-content {
    margin-left: 20px;
  }
  .content-list {
    // background-color: #fff;
    border-radius: 7px;
    width: 875px;

    .content-list-search {
      width: 855px;
      margin-left: 10px;
      padding: 5px;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      justify-content: space-around;
      background-color: white;
      .arrow {
        margin-left: 0;
      }
      .content-list-radio {
        display: flex;
        > div {
          margin-left: 40px;
        }
      }
    }

    .content-item {
      cursor: pointer;
      margin: 10px;
      position: relative;

      .item-main {
        display: flex;
        height: 120px;

        .image {
          border: 1px solid #ccc;
          height: 100%;
          width: 100px;
        }

        .item-right {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          margin-left: 10px;

          .right-top {
            display: flex;
            align-items: center;
          }
          .iconfont {
            font-size: 30px;
            color: #2695f9;
          }
          .title {
            margin-left: 10px;
            font-size: 20px;
            font-weight: bold;
          }

          .right-bottom {
            margin-left: 10px;
            font-size: 14px;
            line-height: 30px;
          }
        }
      }

      .number-statistic {
        display: flex;
        justify-content: space-around;
        color: #333;
        .iconfont {
          margin-right: 5px;
        }
      }
    }
  }

  :deep(.el-card__body) {
    height: 45px;
    padding-top: 12px;
  }

  .col {
    position: relative;
    .new {
      position: absolute;
      width: 30px;
      top: 0;
      left: 0;
      z-index: 999;
    }
  }
</style>
