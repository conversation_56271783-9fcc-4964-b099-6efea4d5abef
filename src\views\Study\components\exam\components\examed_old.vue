<script setup lang="ts">
  import { useRoute, useRouter } from "vue-router"
  import { ref, getCurrentInstance } from "vue"
  import type {
    completedPaperType,
    paperTacticsType,
    ExamQuestionsType,
    ExamQuestionItem
  } from "@/types"
  import { completedPaperDetail } from "@/api/onlineExam"
  import useTenantStore from "@/store/modules/tenant"
  import { noNeedExamScoreDomainList } from "@/utils/constant"

  const { proxy } = getCurrentInstance()!
  const { exam_question_type } = proxy?.useDict("exam_question_type")!
  const { domainName } = storeToRefs(useTenantStore())
  const route = useRoute()
  const router = useRouter()

  const { baseId, paperAnswerId, arrangeId } = route.query
  const completedPaperInfo = ref<completedPaperType>()
  const paperTacticsList = ref<paperTacticsType>()
  const examQuestionList = ref<ExamQuestionsType>([])

  const loadData = async () => {
    let queryData = {
      baseId,
      arrangeId,
      paperAnswerId
    }
    const { data } = await completedPaperDetail(queryData)
    completedPaperInfo.value = data
    paperTacticsList.value = data.paperTactics
    paperTacticsList.value?.forEach(item => {
      item?.examQuestions.forEach((question: ExamQuestionItem) => {
        examQuestionList.value.push(question)
      })
    })
    examQuestionList.value.forEach(item => {
      if (item.questionType === "F") {
        item.fillAnswerList = []
        if (item.userAnswer && item.userAnswer.includes("::")) {
          item.fillAnswerList = item.userAnswer.split("::") || []
        }
      }
    })
  }

  // 点击左侧数字平滑移动到某一题位置
  const scrollToQuestion = (index: number) => {
    const questionTop = proxy?.$refs[`questionBox${index}`]![0].offsetTop - 200
    window.scrollTo({ top: questionTop, behavior: "smooth" })
  }

  const goBack = () => {
    router.go(-1)
  }

  // 正确的选项
  function isCorrect(item: ExamQuestionItem, choice: string): boolean {
    if (item.questionType === "S" || item.questionType === "J") {
      return item.answer === choice
    } else if (item.questionType === "M") {
      if (item.answer.includes(",")) {
        return item.answer.split(",").indexOf(choice) > -1
      }
      return item.answer === choice
    }
    return false
  }

  // 错误的选项
  function isWrong(item: ExamQuestionItem, choice: string): boolean {
    if (!item.userAnswer) return false
    if (item.questionType === "S" || item.questionType === "J") {
      return item.userAnswer === choice && item.userAnswer !== item.answer
    } else if (item.questionType === "M") {
      return item.userAnswer.includes(choice) && !item.answer.includes(choice)
    }
    return false
  }

  loadData()
</script>

<template>
  <div class="examing">
    <div class="header">
      <div class="title">
        <el-icon class="backIcon" @click="goBack"><ArrowLeftBold /></el-icon>
        <span class="baseName">{{ completedPaperInfo?.paperName }}</span>
      </div>
      <div class="operation" v-if="!noNeedExamScoreDomainList.includes(domainName)">
        考试得分： <span style="color: red">{{ completedPaperInfo?.userPaperScore }}分</span> /{{
          completedPaperInfo?.paperScore
        }}分
      </div>
    </div>
    <div class="fix-left-box">
      <div class="number-box">
        <template v-for="(item, index) in examQuestionList">
          <div
            class="num"
            :class="item.userAnswer === item.answer ? 'green' : 'red'"
            @click="scrollToQuestion(index)"
          >
            {{ index + 1 }}
          </div>
        </template>
      </div>
    </div>
    <div class="right-box-list">
      <template v-for="(item, index) in examQuestionList">
        <div class="box-item" :ref="`questionBox${index}`">
          <div class="questionType">
            {{ index + 1 }}.
            <dict-tag :options="exam_question_type" :value="item.questionType" />
          </div>
          <div class="questionName">
            {{ item.questionName }}
          </div>
          <div class="choice-box">
            <div class="choice-tap-item" v-if="item.questionType !== 'F'">
              <template v-for="choice in 11">
                <div class="choice-item" v-if="item[`item${choice}`]">
                  <div class="item">
                    <div class="index">
                      <span v-if="isCorrect(item, String.fromCharCode(64 + choice))">
                        <img src="@/assets/images/correct.png" alt="" />
                      </span>
                      <span v-else-if="isWrong(item, String.fromCharCode(64 + choice))">
                        <img src="@/assets/images/error.png" alt="" />
                      </span>
                      <span class="letterOnly" v-else>{{ String.fromCharCode(64 + choice) }}</span>
                    </div>
                    <div class="content">
                      {{ item[`item${choice}`] }}
                    </div>
                  </div>
                </div>
              </template>
            </div>
            <div class="blank" v-else v-for="blankChoice in item.blankCount">
              <div>填空{{ blankChoice }}：</div>
              <el-input
                v-model="item.fillAnswerList[blankChoice - 1]"
                disabled
                style="width: 200px"
              ></el-input>
              <img
                class="img"
                v-if="
                  item.fillAnswerList[blankChoice - 1] === item.answer.split('::')[blankChoice - 1]
                "
                src="@/assets/images/correct.png"
                alt=""
              />
              <img v-else class="img" src="@/assets/images/error.png" alt="" />
              <div
                class="correct-answer"
                v-if="
                  item.fillAnswerList[blankChoice - 1] !== item.answer.split('::')[blankChoice - 1]
                "
                >正确答案：{{ item.answer.split("::")[blankChoice - 1] }}</div
              >
            </div>
          </div>
          <div class="analysis-box">
            <div class="answer" v-if="item.questionType !== 'F'">
              <i>&nbsp;</i>
              <span>答案：{{ item.answer }}</span>
            </div>
            <div
              class="myAnswer"
              v-if="item.answer !== item.userAnswer && item.questionType !== 'F'"
            >
              <i>&nbsp;</i>
              <span v-if="item.userAnswer"
                >我的答案：{{
                  Array.isArray(item.userAnswer) ? item.userAnswer?.sort().join() : item.userAnswer
                }}</span
              >
              <span v-else>我的答案：未选择</span>
            </div>
            <div class="score" v-if="!noNeedExamScoreDomainList.includes(domainName)">
              <i>&nbsp;</i>
              <span>得分：{{ item.userQuestionScore || 0 }}</span>
            </div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<style scoped lang="less">
  .header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    display: flex;
    height: 70px;
    justify-content: space-between;
    align-items: center;
    background-color: #f4fafe;
    padding: 15px 20%;

    .title {
      font-size: 18px;

      .backIcon {
        cursor: pointer;
        vertical-align: text-bottom;
      }
      .baseName {
        margin-left: 20px;
      }
    }

    .operation {
      width: 60%;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      font-size: 18px;
      font-weight: bold;
      color: #333;
    }
  }

  .fix-left-box {
    position: fixed;
    top: 80px;
    background-color: #fff;
    width: 300px;
    min-height: 500px;
    border-radius: 10px;

    .number-box {
      width: 100%;
      padding: 30px;
      box-sizing: border-box;
      display: grid;
      grid-row-gap: 30px;
      grid-column-gap: 22px;
      grid-template-columns: repeat(5, minmax(0, 1fr));
      .green {
        background-color: #04c877 !important;
      }
      .red {
        background-color: #f63b46 !important;
      }
      .num {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 30px;
        width: 30px;
        background-color: #999;
        border-radius: 50%;
        color: #fff;
        cursor: pointer;
      }
    }
  }

  .right-box-list {
    padding-top: 100px;
    .box-item {
      display: flex;
      flex-direction: column;
      padding: 15px 30px;
      margin: 0 0 0 330px;
      background-color: #fff;
      border-radius: 10px;

      .questionType {
        display: flex;
        font-size: 20px;
      }

      .questionName {
        margin-top: 8px;
        font-size: 16px;
      }

      .choice-box {
        padding: 10px 0;
        .choice-tap-item {
          line-height: 40px;
          display: flex;
          flex-direction: column;
          .choice-item {
            display: flex;
            align-items: center;
            .item {
              display: flex;
              align-items: center;
            }

            .choice-selected {
              background-color: @warmOrange;
              border: 1px solid @warmOrange;
            }

            .index {
              display: flex;
              justify-content: center;
              align-items: center;
              width: 24px;
              min-width: 24px;
              height: 24px;

              > img {
                width: 95%;
                height: 95%;
              }

              .letterOnly {
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 50% 50%;
                width: 22px;
                height: 22px;
                border: 1px solid #ccc;
              }
            }

            .content {
              padding-left: 10px;
              font-size: 16px;
            }
          }
        }

        .blank {
          display: flex;
          align-items: center;
          margin-top: 20px;
          > :first-child {
            width: 90px;
          }

          .img {
            width: 24px;
            height: 24px;
            margin-left: 20px;
          }
          .correct-answer {
            margin-left: 20px;
          }
        }
      }

      .analysis-box {
        display: flex;
        height: auto;
        background: #f4fafe;
        border-radius: 10px;
        padding: 13px 20px;
        flex-direction: row;
        justify-content: flex-start;

        .answer {
          margin-right: 30px;
          > i {
            width: 4px;
            height: 18px;
            border-radius: 2px;
            margin-right: 10px;
            background: #04c877;
          }
        }
        .myAnswer {
          margin-right: 30px;
          > i {
            width: 4px;
            height: 18px;
            border-radius: 2px;
            margin-right: 10px;
            background: #ff4d4f;
          }
        }
        .score {
          > i {
            width: 4px;
            height: 18px;
            border-radius: 2px;
            margin-right: 10px;
            background: #3ca7fa;
          }
        }
      }
    }
  }
</style>
