<!--
 * @Description: 
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON><PERSON>
 * @Date: 2023-04-17 08:49:10
 * @LastEditTime: 2023-05-31 09:58:34
-->
<script setup lang="ts">
import { useRoute } from "vue-router"
import { ref } from "vue"
import { getListById } from "@/api/develops/news"
import type { newsType } from "@/types"
let route = useRoute()
let newsList = ref<newsType>()

//获取章节
async function fetchNewsData() {
  const { data } = await getListById(route.query.id)
  newsList.value = data
}

fetchNewsData()
</script>

<template>
  <div class="container">
    <div class="news-detail-header">
      <div class="news-detail-header-title">{{ newsList?.newsTitle }}</div>
      <div class="news-detail-header-time">
        发布时间：{{ newsList?.publishStartTime }}
      </div>
    </div>
    <div class="news-detail-body">
      {{ newsList?.newsContent }}
    </div>
  </div>
</template>

<style scoped lang="less">
.container {
  padding: 20px;
  min-height: 100vh;
  .news-detail-header {
    background-color: #fff;
    display: flex;
    align-items: center;
    flex-direction: column;
    border-bottom: 1px solid #eeeeee;
    padding: 20px 0;

    .news-detail-header-title {
      margin-bottom: 20px;
      font-size: 25px;
      font-weight: 500;
    }
  }

  .news-detail-body {
    background-color: #fff;
    padding: 20px;
  }
}
</style>
