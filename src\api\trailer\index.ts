/*
 * @Description: 
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-11-14 15:43:59
 * @LastEditTime: 2024-11-14 15:44:03
 */
import request from "@/utils/request"

// 查询公益宣传片列表
export function listVideo(query) {
  return request({
    url: "/devops/video/list",
    method: "get",
    params: query
  })
}

// 查询公益宣传片详细
export function getVideo(psaId) {
  return request({
    url: "/devops/video/" + psaId,
    method: "get"
  })
}

// 新增公益宣传片
export function addVideo(data) {
  return request({
    url: "/devops/video",
    method: "post",
    data: data
  })
}

// 修改公益宣传片
export function updateVideo(data) {
  return request({
    url: "/devops/video",
    method: "put",
    data: data
  })
}

// 删除公益宣传片
export function delVideo(psaId) {
  return request({
    url: "/devops/video/" + psaId,
    method: "delete"
  })
}
