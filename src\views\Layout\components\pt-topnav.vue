<!--
 * @Description: layout-topnav
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-14 09:43:19
 * @LastEditTime: 2025-06-30 08:58:30
-->
<script setup lang="ts">
  import { ElMessageBox } from "element-plus"
  import useStore from "@/store"
  import { pushToAdmin } from "@/utils/common"
  import useTenantStore from "@/store/modules/tenant"
  import AppHeaderNav from "./layout-header-nav.vue"
  import { Search } from "@element-plus/icons-vue"
  import LoginDialog from "@/views/Home/pt/LoginDialog.vue"
  import { onMounted, onUnmounted } from "vue"
  import { getToken } from "@/utils/auth"
  import QrcodeVue from "qrcode.vue"

  const { user } = useStore()
  const { name, avatar, token } = storeToRefs(user)
  const router = useRouter()
  const { domainName } = storeToRefs(useTenantStore())

  // 判断是否已经登陆 - 从响应式store中获取token状态
  const eitherToken = computed(() => {
    return token.value || getToken()
  })
  const toHome = () => {
    router.push({
      path: "/"
    })
  }

  const handleCommand = command => {
    switch (command) {
      case "pushAdmin":
        pushToAdmin("/index")
        break
      case "logout":
        logout()
        break
      default:
        break
    }
  }

  const logout = () => {
    ElMessageBox.alert("确定退出系统吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      confirmButtonClass: "logout-confirm-btn",
      type: "warning"
    })
      .then(() => {
        user.logOutAction().then(() => {
          // 根据租户决定登出后的行为
          if (domainName.value === "pt") {
            // pt租户登出后刷新当前页面
            location.reload()
          } else {
            location.href = "/index"
          }
        })
      })
      .catch(() => {})
  }

  // 显示登录弹窗状态
  const showLoginDialog = ref(false)
  const mobileEntryUrl = computed(() => {
    // 获取当前域名并替换
    let host = window.location.hostname.replace(/edu(.*?)user/, "edu$1app")
    // 拼接协议、host、路径和参数
    return `${window.location.protocol}//${host}${window.location.pathname}${window.location.search}`
  })
  const registerOrLogin = () => {
    // 判断是否为pt租户
    if (domainName.value === "pt") {
      showLoginDialog.value = true
    } else {
      pushToAdmin()
    }
  }

  // 登录成功回调
  const handleLoginSuccess = () => {
    // 触发全局登录成功事件，让其他组件可以响应
    const customEvent = new CustomEvent("global-login-success")
    window.dispatchEvent(customEvent)
  }

  // 处理全局登录弹窗事件
  const handleGlobalLoginDialog = event => {
    showLoginDialog.value = true
  }

  // 监听用户未授权事件
  const handleUserUnauthorized = () => {
    showLoginDialog.value = true
  }

  // 添加全局事件监听
  onMounted(() => {
    // 页面加载时检查是否有token但没有用户信息，如果有则获取用户信息
    if (getToken() && !name.value) {
      user.getInfo()
    }

    window.addEventListener("global-login-dialog", handleGlobalLoginDialog)
    window.addEventListener("pt-user-unauthorized", handleUserUnauthorized)
  })

  // 移除全局事件监听
  onUnmounted(() => {
    window.removeEventListener("global-login-dialog", handleGlobalLoginDialog)
    window.removeEventListener("pt-user-unauthorized", handleUserUnauthorized)
  })

  let rotated = ref(false)
  const handleVisibleChange = val => {
    rotated.value = val
  }

  let courseName = ref("")
  const searchCourse = () => {
    router.push({
      path: "/course",
      query: {
        courseName: courseName.value
      }
    })
  }
</script>

<template>
  <nav
    class="bg-white fixed top-0 left-0 w-full z-100 shadow-[0_2px_15px_0_hsla(0,0%,45%,0.2)] h-70px flex justify-center items-center"
  >
    <div class="flex w-full px-48px mx-auto items-center">
      <!-- 左侧Logo区域 -->
      <div class="flex items-center flex-shrink-0" style="min-width: 300px">
        <h1 class="cursor-pointer flex items-center" @click="toHome">
          <div
            class="text-18px font-bold text-#024c86 border-l-3 border-#024c86 pl-8px whitespace-nowrap"
          >
            上海市普陀区企业合同信用促进会
          </div>
        </h1>
      </div>

      <!-- 中间导航和搜索区域 -->
      <div class="flex justify-center items-center flex-1">
        <AppHeaderNav />

        <el-input
          v-model="courseName"
          class="!w-200px ml-15px"
          :prefix-icon="Search"
          @keyup.enter="searchCourse"
          placeholder="搜索课程"
          clearable
        ></el-input>
      </div>

      <!-- 右侧用户区域 -->
      <div
        class="flex items-center text-center text-deepColor text-16px cursor-pointer flex-shrink-0 gap-10px"
      >
        <el-popover placement="bottom" trigger="hover" width="180">
          <template #reference>
            <div class="mobile-entry flex items-center cursor-pointer">
              <el-icon :size="20" color="#67C23A"><Iphone /></el-icon>
              <span class="text-sm font-medium text-gray-700">手机端入口</span>
            </div>
          </template>
          <div class="flex flex-col items-center p-4 rounded-md shadow-md bg-white">
            <div class="qr-code-wrapper">
              <qrcode-vue :value="mobileEntryUrl" :size="120" />
            </div>
            <p class="text-xs text-gray-500 mt-2">扫码访问手机端</p>
          </div>
        </el-popover>
        <div to="/login" v-if="!eitherToken" @click="registerOrLogin">
          <i class="iconfont icon-shouji"></i>注册/登录
        </div>

        <div class="flex h-40px justify-center items-center" v-else>
          <el-dropdown
            @visible-change="handleVisibleChange"
            @command="handleCommand"
            trigger="hover"
          >
            <div class="relative flex justify-center items-center">
              <img :src="avatar" class="w-40px h-40px rounded-full mr-3px cursor-pointer" />
              <div class="ml-8px mr-8px font-bold">{{ name }}</div>
              <i
                class="iconfont icon-xiangxia cursor-pointer"
                :class="{
                  'transform rotate-180 transition-all duration-300 origin-center': rotated,
                  'transform rotate-0 transition-all duration-300 origin-center': !rotated
                }"
              ></i>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="logout">
                  <span>退出登录</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>

    <!-- 登录弹窗 -->
    <LoginDialog v-model:visible="showLoginDialog" @login-success="handleLoginSuccess" />
  </nav>
</template>

<style lang="less" scoped>
  .mobile-entry {
    padding: 8px 12px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
  }

  .mobile-entry:hover {
    background-color: rgba(0, 0, 0, 0.05); /* 轻微的背景色变化 */
  }

  .qr-code-wrapper {
    border: 1px solid #eee;
    padding: 4px;
    border-radius: 4px;
  }
  :deep(.el-input__wrapper) {
    border-radius: 30px;
  }

  // 响应式适配
  @media screen and (max-width: 1600px) {
    .flex.w-full {
      padding: 0 30px;
    }
    .flex.items-center:first-child {
      min-width: 280px;
    }
    h1 div {
      font-size: 16px;
    }
    .el-input {
      width: 180px !important;
      margin-left: 10px;
    }
  }

  @media screen and (max-width: 1440px) {
    .flex.w-full {
      padding: 0 20px;
    }
    .flex.items-center:first-child {
      min-width: unset;
    }
    h1 div {
      font-size: 15px;
    }
    .el-input {
      width: 160px !important;
      margin-left: 8px;
    }
  }

  @media screen and (max-width: 1200px) {
    .flex.w-full {
      padding: 0 15px;
    }
    .flex.items-center:first-child {
      min-width: 220px;
    }
    h1 div {
      font-size: 14px;
    }
    .flex.items-center:last-child {
      font-size: 14px;
      gap: 8px;
      .font-bold {
        display: none;
      }
    }
    .el-input {
      width: 140px !important;
      margin-left: 5px;
    }
  }
</style>
