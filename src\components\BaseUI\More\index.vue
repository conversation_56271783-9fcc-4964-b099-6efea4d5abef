<template>
  <router-link :to="path" class="base-more">
    <span>查看全部</span>
    <i class="iconfont icon-angle-right"></i>
  </router-link>
</template>

<script setup lang="ts">
  defineProps({
    path: {
      type: String,
      default: "/"
    }
  })
</script>

<style scoped lang="less">
  .base-more {
    position: absolute;
    right: 10px;
    margin-bottom: 2px;
    span {
      font-size: 16px;
      vertical-align: top;
      margin-right: 4px;
      color: #999;
    }
    i {
      font-size: 14px;
      vertical-align: top;
      position: relative;
      top: 2px;
      color: #ccc;
    }
    &:hover {
      span,
      i {
        color: @warmOrange;
      }
    }
  }
</style>
