<!--
 * @Description: 学习中心页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-14 09:43:19
 * @LastEditTime: 2024-12-02 14:12:42
-->
<script setup lang="ts">
  import { computed, ref, onMounted, nextTick, onBeforeUnmount } from "vue"
  import { useRoute } from "vue-router"
  import useUserStore from "@/store/modules/user"
  import { storeToRefs } from "pinia"
  import signInDialog from "./components/signInDialog.vue"
  import personnalInfo from "./components/personnalInfo.vue"
  import studyCenterSideBarVue from "./components/studyCenterSideBar.vue"

  const route = useRoute()
  const userStore = useUserStore()
  const { extraInfo } = storeToRefs(userStore)

  const contentRef = ref()
  const containerRef = ref()
  // const myObserver = ref<ResizeObserver | null>(null)

  onMounted(async () => {
    userStore.fetchExtraInfo()

    // myObserver.value = new ResizeObserver(entries => {
    //   entries.forEach(entry => {
    //     const contentHeight = entry.contentRect.height
    //     if (contentHeight < 1000) return
    //     if (!containerRef.value) return
    //     containerRef.value.style.height = contentHeight + "px"
    //   })
    // })

    // await nextTick()
    // if (contentRef.value) {
    //   myObserver.value.observe(contentRef.value?.[0])
    // }
  })

  onBeforeUnmount(() => {
    // if (myObserver.value) {
    //   myObserver.value.disconnect()
    // }
  })

  const signInDialogRef = ref()
  const openSigninDialog = () => {
    signInDialogRef.value.openDialog()
  }
</script>

<template>
  <div class="bg-white">
    <personnalInfo ref="personnalInfoRef" :extraInfo="extraInfo" @signin="openSigninDialog" />
    <div class="flex relative min-h-1200px" ref="containerRef">
      <div class="w-250px flex-shrink-0">
        <studyCenterSideBarVue />
      </div>
      <div class="flex-1 p-20px pl-50px rounded-10px bg-white min-h-740px overflow-x-hidden" ref="contentRef">
        <router-view />
      </div>
    </div>
    <signInDialog ref="signInDialogRef" @emit-data="() => userStore.fetchExtraInfo(true)" />
  </div>
</template>
