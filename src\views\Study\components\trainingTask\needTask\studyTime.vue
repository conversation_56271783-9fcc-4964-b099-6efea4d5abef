<!--
 * @Description: 
 * @Author: sunyun<PERSON>
 * @LastEditors: sunyunwu
 * @Date: 2023-09-08 15:40:09
 * @LastEditTime: 2023-10-07 15:13:59
-->
<template>
  <div class="study-view">
    <div>
      <span class="title">学习时长表</span>

      <ul class="timeSlot">
        <li
          v-for="item in dateList"
          @click="changeDate(item.value)"
          :class="activeVal === item.value ? 'active' : ''"
        >
          {{ item.label }}
        </li>
      </ul>
    </div>
    <div class="view-con">
      <BaseEcharts :option="option" />
    </div>
  </div>
</template>

<script setup>
  import { reactive, ref } from "vue"
  import { graphic } from "echarts"
  import useStore from "@/store"
  import { learnDistribution } from "@/api/course/study-log.ts"
  const { user } = useStore()
  const { roles } = storeToRefs(user)
  const isAdmin =() => {
    let hasAdminRole = false
    if (roles.value.length > 0) {
      roles.value.forEach(item => {
        if (item.includes("admin")) {
          hasAdminRole = true
        }
      })
    }
    return hasAdminRole
  }
  const activeVal = ref("0")
  const dateList = ref([
    { label: "近1个月", value: "0" },
    { label: "近6个月", value: "1" },
    { label: "近1年", value: "2" }
  ])

  const queryParams = reactive({
    sortRange: "0"
  })

  if (!isAdmin()) {
    queryParams.userId = user.userInfo.userId
  }

  const getData = () => {
    learnDistribution(queryParams).then(res => {
      setOption(res.data)
    })
  }
  // 日期切换
  const changeDate = value => {
    activeVal.value = value
    queryParams.sortRange = value
    getData()
  }

  getData()
  // 学习时长
  const option = ref(null)

  const setOption = data => {
    let xarr = []
    let yarr = []
    for (let item of data) {
      yarr.push((item.study_duration / 3600).toFixed(2))
      xarr.push(item.study_time)
    }

    let rotate = 0
    if (queryParams.sortRange === "0") {
      rotate = 60
    }
    option.value = {
      xAxis: {
        type: "category",
        data: xarr,
        axisLabel: {
          interval: 0,
          rotate
        },
        boundaryGap: false
      },
      tooltip: {
        trigger: "axis",
        backgroundColor: "#6a7985",
        borderColor: "#6a7985",
        textStyle: {
          color: "#FFF",
          fontFamily: "Arial",
          fontSize: 14
        },
        axisPointer: {
          type: "cross",
          label: {
            backgroundColor: "#6a7985"
          }
        },
        formatter: function (params) {
          return params[0]["value"] + "小时"
        }
      },
      yAxis: {
        type: "value",
        name: "时长（小时）"
      },
      grid: {
        left: "30",
        top: "30",
        bottom: "0",
        right: "30",
        containLabel: true
      },
      color: ["rgb(1, 191, 236)"],
      series: [
        {
          data: yarr,
          type: "line",
          smooth: true,
          showSymbol: false,
          areaStyle: {
            opacity: 0.8,
            color: new graphic.LinearGradient(0, 0, 1, 0, [
              {
                offset: 0,
                color: "rgb(128, 255, 165)"
              },
              {
                offset: 1,
                color: "rgb(1, 191, 236)"
              }
            ])
          }
        }
      ]
    }
  }
</script>

<style scoped lang="less">
  .timeSlot {
    float: right;
    .active {
      background: #867ffd;
      color: #fff;
    }
    > li {
      display: inline-block;
      width: 80px;
      line-height: 34px;
      background: #fff;
      border-radius: 5px;
      margin: 0 6px;
      text-align: center;
      font-size: 14px;
      border: 1px solid #d7d7d7;
      cursor: pointer;
    }
  }
  .title {
    line-height: 40px;
    font-size: 16px;
    font-weight: bold;
    text-indent: 10px;
    color: #ee9d32;
  }
  .view-con {
    background: #fff;
    padding: 14px 20px;
    border-radius: 10px;
    height: 360px;
  }
</style>
