/*
 * @Description: uno.config.ts
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-11-14 14:29:18
 * @LastEditTime: 2024-12-16 16:41:07
 */
import { defineConfig } from "unocss"
import { presetUno } from "unocss/preset-uno"
export default defineConfig({
  safelist: [
    'bg-red-500',
    'bg-green-500'
  ],
  // ...UnoCSS options
  presets: [presetUno({ dark: "class", attributify: false })],
  // transformers: [transformerVariantGroup()],
  shortcuts: {
    "wh-full": "w-full h-full",
    'nav-link': 'text-17px leading-32px inline-block w-full h-70px leading-70px hover:text-skyblueColor hover:border-b-3 hover:border-b-skyblueColor'
  },
  theme: {
    colors: {
      deepColor: 'var(--deepColor)',
      skyblueColor: 'var(--skyblueColor)',
      warmOrange: 'var(--warmOrange)'
    }
  },
  rules: [
    ['nav-link-active', { 
      'color': 'var(--skyblueColor)',
      'border-bottom': '3px solid var(--skyblueColor)'
    }]
  ],
})
