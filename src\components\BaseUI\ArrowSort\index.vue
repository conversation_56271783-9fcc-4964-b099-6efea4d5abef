<!--
 * @Description: 列表按条件排序（升序/降序）
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-04 16:20:34
 * @LastEditTime: 2024-11-18 17:01:12
-->
<script lang="ts" setup>
  import type { ArrowSortType } from "@/types"

  interface Emit {
    (e: "sort", value: any): void
  }
  const emit = defineEmits<Emit>()

  const props = defineProps({
    // 控制按钮大小 size: large middle small mini
    dataList: {
      type: Array as PropType<ArrowSortType[]>,
      default: () => []
    },
    sortFieldName: {
      type: String,
      default: "sortField"
    },
    sortOrderName: {
      type: String,
      default: "sortOrder"
    },
    //若不希望点击默认的时候排序重置为undefined时，例子：资料列表默认改为按浏览数升序排列
    isReset: {
      type: Boolean,
      default: false
    }
  })

  let arrowSort = ref<ArrowSortType[]>()
  arrowSort.value = props.dataList.map(item => {
    return {
      ...item,
      isSort: false
    }
  })

  let activeIndex = ref(0)
  const onSort = (index: number, item?) => {
    // 切换到默认
    if (index === 0 && activeIndex.value !== index) {
      activeIndex.value = index
      if (props.isReset) {
        emit("sort", { [props.sortFieldName]: "view_number", [props.sortOrderName]: "asc" })
      } else {
        emit("sort", { [props.sortFieldName]: undefined, [props.sortOrderName]: undefined })
      }
    }
    // 不同条件需要切换
    else if (activeIndex.value !== index) {
      item.isSort = true
      activeIndex.value = index
      emit("sort", { [props.sortFieldName]: item.name, [props.sortOrderName]: "desc" })
    }
    // 同一个条件切换升序/降序
    else {
      if (index !== 0) {
        item.isSort = !item.isSort
        emit("sort", {
          [props.sortFieldName]: item.name,
          [props.sortOrderName]: item.isSort ? "desc" : "asc"
        })
      }
    }
  }
</script>

<template>
  <div class="arrow">
    <span
      style="padding: 3px 8px; margin-right: 20px"
      :class="{ activeBorder: activeIndex === 0 }"
      @click="onSort(0)"
      >默认</span
    >
    <el-divider direction="vertical"></el-divider>
    <template v-for="(item, index) in arrowSort">
      <div class="arrowSortItem" @click="onSort(index + 1, item)">
        <div class="arrowSortLabel">{{ item.label }}</div>
        <div class="arrowSortIcon">
          <i
            :class="{ act: activeIndex === index + 1 && item.isSort === false }"
            class="iconfont icon-shangjiantou"
          ></i>
          <i
            :class="{ act: activeIndex === index + 1 && item.isSort === true }"
            class="iconfont icon-xiajiantou"
          ></i>
        </div>
        <el-divider
          direction="vertical"
          v-if="arrowSort && index !== arrowSort!.length-1"
        ></el-divider>
      </div>
    </template>
  </div>
</template>

<style lang="less" scoped>
  .arrow {
    display: flex;
    align-items: center;
    cursor: pointer;

    .activeBorder {
      // border: 1px solid #e9bb53;
      border-radius: 5px;
      // background-color: @warmOrange;
      color: var(--el-color-primary );
    }

    .arrowSortItem {
      display: flex;
      align-items: center;
      margin-left: 20px;
      // padding: 3px 8px;

      .arrowSortIcon {
        display: flex;
        flex-direction: column;
        margin-right: 20px;
      }
    }
  }
  .iconfont {
    display: flex;
    flex-direction: column;
    margin-bottom: -6px;
    margin-top: -6px;
    color: #dad6d6;
    margin-left: 5px;
  }
  .act {
    color: var(--el-color-primary);
  }
</style>
