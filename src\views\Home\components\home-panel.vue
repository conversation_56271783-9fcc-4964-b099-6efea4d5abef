<!--
 * @Description: home-panel
 * @Author: <PERSON>
 * @LastEditors: sun<PERSON><PERSON>
 * @Date: 2023-03-14 09:43:19
 * @LastEditTime: 2023-09-06 15:40:20
-->
<script setup lang="ts">
  defineProps({
    title: {
      type: String,
      default: ""
    },
    isShowMore: {
      type: Boolean,
      default: true
    },
    backgroundColor: {
      type: String,
      default: ""
    }
  })
</script>

<template>
  <div class="home-panel" :style="backgroundColor ? `background-color: ${backgroundColor}` : ''">
    <div class="container">
      <div class="head">
        <div class="title"> <i class="iconfont icon-jiaoxuefansi"></i>{{ title }} </div>
        <!-- 右侧内容区域 -->
        <div class="right" v-if="isShowMore">
          <RouterLink to="/course">更多课程</RouterLink>
        </div>
      </div>
      <!-- 主体内容区域 -->
      <slot></slot>
    </div>
  </div>
</template>

<style scoped lang="less">
  .home-panel {
    /*  margin-top: 30px; */
    padding: 30px 0 60px 0;
    background: #fff;

    position: sticky;
    // box-shadow: 0 2px 15px 0 hsla(0, 0%, 45%, 0.2);
    .head {
      padding-bottom: 20px;
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      .title {
        font-size: 20px;
        font-weight: 550;

        line-height: 34px;

        .iconfont {
          color: @warmOrange;
          font-size: 24px;
          margin-right: 12px;
          vertical-align: middle;
        }
      }

      .right {
        > a {
          color: #646363;
          font-size: 16px;
          cursor: pointer;
        }

        > a:hover {
          color: @warmOrange;
        }
      }
    }
  }
</style>
