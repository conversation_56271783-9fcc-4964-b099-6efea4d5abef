<script setup lang="ts">
import AppHeaderNav from "./layout-header-nav.vue";
</script>

<template>
  <header class="app-header">
    <div class="container">
      <AppHeaderNav />
    </div>
  </header>
</template>

<style scoped lang="less">
.app-header {
  background: #fff !important;
  border-bottom: 1px solid #f1f2fa;
  .container {
    height: 45px;
    padding-bottom: 15px;
    display: flex;
    align-items: center;
  }
}
</style>
