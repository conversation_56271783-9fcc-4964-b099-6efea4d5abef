/*
 * @Description:
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-17 08:49:10
 * @LastEditTime: 2024-11-14 17:24:16
 */
import { listMeans } from "@/api/trainingTask/means"
import type { MeansItemType, CatalogueList } from "@/types"
import { catalogueType } from "@/constants"
import { catalogueList } from "@/api/system/catalogue"

export default defineStore("means", {
  state: () => ({
    manageName: "" as string,
    catalogueId: null as number | null,
    meansList: [] as MeansItemType[],
    pageSize: 6,
    pageNum: 1,
    dataTotal: 0,
    manageType: "",
    isNew: false,
    newItem: [] as number[],
    catalogueTableData: [] as CatalogueList
  }),

  actions: {
    async getMeansInfo(otherParams?) {
      const { rows, total } = await listMeans({
        pageSize: this.pageSize,
        pageNum: this.pageNum,
        manageType: this.manageType,
        sortField: "view_number",
        sortOrder: "asc",
        ...otherParams,
        catalogueId: this.catalogueId || null
      })
      this.meansList = rows
      this.dataTotal = total || 0
    },

    async getMeansCatalogueList() {
      catalogueList({ catalogueType: catalogueType.MEANS_CATALOGUE }).then(response => {
        this.catalogueTableData = response.rows
        this.catalogueTableData.forEach(item => {
          item.isNew = false
        })
      })
    }
  }
})
