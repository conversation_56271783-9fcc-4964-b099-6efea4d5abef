<!--
 * @Description: detail-intro
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-09-05 14:26:28
 * @LastEditTime: 2023-09-19 14:12:09
-->
<script lang="ts" setup>
  import { editFavoriteOrNot } from "@/api/course"
  import useUserStore from "@/store/modules/user"
  import { ElMessage } from "element-plus"
  import type { classType } from "@/types"
  import { storeToRefs } from "pinia"
  import { editCourseGrade } from "@/api/course/grade"

  interface Emit {
    (e: "fetch-data"): void
  }
  const emit = defineEmits<Emit>()

  const props = defineProps({
    courseItem: {
      type: Object as PropType<classType>,
      default: () => ({})
    },
    rateVal: {
      type: Number
    },
    learnedStatus: {
      type: Boolean,
      default: false
    }
  })

  watch(
    () => props.rateVal,
    val => {
      gradeVal.value = val
    }
  )

  let route = useRoute()
  let gradeVal = ref() // 评分值

  let userStore = useUserStore()
  const { userInfo } = storeToRefs(userStore)
  // const instance = getCurrentInstance()
  // const { completion_conditions } = instance?.proxy?.useDict("completion_conditions")

  // const conditions = computed(() => {
  //   return props.courseItem?.completionConditions?.split(",")
  // })
  // 收藏
  const collectCourse = async () => {
    if (props.courseItem?.favoriteOrNot == true) {
      let queryData = {
        courseId: route.query.id,
        userId: userInfo.value.userId,
        favoriteOrNot: false
      }
      await editFavoriteOrNot(queryData)
      ElMessage({
        message: "取消收藏成功！",
        type: "success"
      })
    } else {
      let queryData = {
        courseId: route.query.id,
        userId: userInfo.value.userId,
        favoriteOrNot: true
      }
      await editFavoriteOrNot(queryData)
      ElMessage({
        message: "收藏成功！",
        type: "success"
      })
    }
    emit("fetch-data")
  }

  // 评分
  const changeRate = async () => {
    if (props.learnedStatus && !props.courseItem?.selfCourseGrade) {
      let queryData = {
        courseId: route.query.id,
        grade: gradeVal.value
      }
      await editCourseGrade(queryData)
      ElMessage({
        message: "感谢您的评分！",
        type: "success"
      })
    } else {
      ElMessage({
        message: "请等待课程学习完成后在评价",
        type: "error"
      })
      gradeVal.value = null
    }
    emit("fetch-data")
  }
</script>

<template>
  <div class="detail-intro">
    <div class="detail-intro-top">
      <div class="content">
        <div>收藏人数：{{ courseItem?.favoriteCounts || 0 }}</div>
        <el-divider direction="vertical" />
        <div>讲师：{{ courseItem?.lecturer }}</div>
        <el-divider direction="vertical" />
        <div>学分：{{ courseItem?.credits || 0 }}</div>
        <el-divider direction="vertical" />
        <div>学时：{{ courseItem?.creditHours || 0 }}</div>
      </div>

      <div class="favor" @click="collectCourse">
        <i
          :class="
            courseItem?.favoriteOrNot == true
              ? 'active-collect iconfont icon-xihuan'
              : 'iconfont icon-collect'
          "
        ></i>
        <span style="margin-left: 10px">收藏</span>
      </div>
      <div class="info">
        <img v-if="learnedStatus" src="@/assets/images/ok.png" alt="" />
        <div>评分：</div>
        <el-rate
          @change="changeRate"
          v-model="gradeVal"
          allow-half
          :show-score="true"
          :score-template="`${Number(gradeVal).toFixed(2)}`"
          :disabled="!!courseItem?.selfCourseGrade"
      /></div>
    </div>
    <!-- <el-divider border-style="dashed" />
    <div class="detail-intro-bottom">
      <div>
        完成条件：
        <template v-for="item in conditions">
          <div class="checkItem">
            <el-checkbox checked disabled>
              <dict-tag :options="completion_conditions" :value="item" />
            </el-checkbox>
          </div>
        </template>
      </div>
    </div> -->
  </div>
</template>

<style lang="less" scoped>
  .detail-intro {
    background-color: #ffffff;

    .detail-intro-top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px;
      .content {
        display: flex;
        align-items: center;
        > div {
          margin-left: 20px;
          margin-right: 10px;
        }
      }

      .favor {
        cursor: pointer;
      }

      .info {
        width: 250px;
        display: flex;
        align-items: center;
        > img {
          position: absolute;
          width: 70px;
          right: 22%;
        }
        > div {
          margin-right: 10px;
        }
      }
    }

    .detail-intro-bottom {
      display: flex;
      align-items: center;
      margin-left: 10px;
      padding-bottom: 20px;
      justify-content: space-between;
      > div {
        display: flex;
        align-items: center;
        margin-left: 20px;
        margin-right: 40px;

        .checkItem {
          margin-left: 20px;
        }

        .icon-xihuan {
          color: #8a8a8a;
        }
      }
    }
  }

  .active-collect {
    color: #dc4853 !important;
    transition: all 0.3s;
  }
</style>
