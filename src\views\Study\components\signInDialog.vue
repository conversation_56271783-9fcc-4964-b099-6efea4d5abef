<template>
  <el-dialog
    v-model="centerDialogVisible"
    title="签到"
    width="50%"
    align-center
    :close-on-click-modal="false"
    @close="closeSignInDialog"
  >
    <div class="dialog_body">
      <div class="left">
        <div class="dayCount">您已连续签到{{ continuousDays }}天</div>
        <img src="@/assets/images/qd-jb.jpg" alt="" />
        <div class="integralCount" v-if="isSigned">本次签到获得{{ score }}积分</div>
        <!-- <el-button class="signButton" @click="sign" v-if="!isSigned">签到</el-button>
        <el-button class="signButton" disabled v-else>已签到</el-button> -->
      </div>
      <div class="right">
        <el-calendar ref="calendarRef">
          <template #header="{ date }">
            <span>{{ date }}</span>
          </template>
          <template #date-cell="{ data }">
            <p>
              {{ data.day.split("-").slice(-1).join() }}
              <template v-if="dateInfo.includes(data.day)">
                <i class="iconfont icon-duigou"></i>
              </template>
            </p>
          </template>
        </el-calendar>
      </div>
    </div>
  </el-dialog>
</template>
<script setup lang="ts">
  import { getSignByMonth, addPointsToUser } from "@/api/develops/point"
  import { ruleType } from "@/utils/constant"
  import { ElMessage } from "element-plus"
  const emit = defineEmits(["emit-data"])

  const centerDialogVisible = ref(false)
  const dateInfo: any = ref([])
  const calendarRef = ref()
  let continuousDays = ref(0)
  let getScore = ref(0)
  let isSigned = ref(false)

  const openDialog = () => {
    sign()
    centerDialogVisible.value = true
  }

  const sign = async () => {
    let requestData = {
      ruleType: ruleType.SIGN_IN_POINTS_RULE
    }
    const { data } = await addPointsToUser(requestData)
    ElMessage({
      message: "签到成功！",
      type: "success"
    })
    getScore.value = data
    fetchSignList()
    emit("emit-data")
  }

  const score = ref(0)
  const fetchSignList = async () => {
    let requestData = {
      year: new Date().getFullYear(), // 当前年
      month: new Date().getMonth() + 1 // 当前月
    }
    const { code, data } = await getSignByMonth(requestData)
    if (data.signListMonths) {
      if (data?.signListMonths[new Date().getDate() - 1]?.signInStatus) {
        isSigned.value = true
      }
      if (code === 200) {
        data?.signListMonths.map(item => {
          if (item.signInStatus) {
            dateInfo.value.push(item.date)
          }
        })
      }
      continuousDays.value = data.alreadyRunSignDay || 0
      score.value = data.score || 0
    }
  }

  const closeSignInDialog = () => {
    centerDialogVisible.value = false
  }

  defineExpose({
    openDialog
  })
</script>

<style lang="less" scoped>
  :deep(.el-button.is-disabled, .el-button.is-disabled:focus, .el-button.is-disabled:hover) {
    color: #606266 !important;
  }
  .dialog_body {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    .left {
      width: 25%;
      background-color: #f5f5f5;
      .dayCount {
        height: 80px;
        background-color: #eeecec;
        text-align: center;
        line-height: 80px;
        font-size: 16px;
        color: #000;
        letter-spacing: 2px;
      }

      > img {
        margin: 80px auto;
        display: block;
      }

      .integralCount {
        height: 80px;
        background-color: #ffffff;
        margin: 20px;
        padding: 10px;
      }

      .signButton {
        width: 80%;
        margin: 60px auto 0 auto;
        display: block;
      }
    }

    .right {
      flex: 1;
    }
  }
</style>
