<!--
 * @Description: 我的资料页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-19 15:26:55
 * @LastEditTime: 2024-12-19 11:07:41
-->

<script setup lang="ts">
  import { ref, getCurrentInstance } from "vue"
  import { Search, ArrowRight } from "@element-plus/icons-vue"
  import useUserStore from "@/store/modules/user"
  import { storeToRefs } from "pinia"
  import type { CatalogueList } from "@/types"
  import { catalogueType } from "@/constants"
  import { catalogueList } from "@/api/system/catalogue"
  import type { MeansItemType } from "@/types"
  import { listMeans } from "@/api/trainingTask/means"
  import { editRelateNumber } from "@/api/trainingTask/means"

  const catalogueTableData = ref<CatalogueList>([])
  const { proxy } = getCurrentInstance()!
  const { sys_flie_type } = proxy?.useDict("sys_flie_type")!
  const dataTotal = ref(0)
  const pageNum = ref(1)
  const pageSize = ref(6)
  const meansList = ref<MeansItemType[]>([])
  const userStore = useUserStore()
  const { userInfo } = storeToRefs(userStore)
  const searchValue = ref("")
  const filePreviewRef = ref<any>(null)
  const filePreview = async item => {
    await editRelateNumber({ manageId: item.manageId, viewNumber: 0 })
    filePreviewRef.value.fileLoad(item, item.manageId)
  }

  const downloadCountRecord = async manageId => {
    await editRelateNumber({ manageId, downloadNumber: 0 })
  }

  function getCatalogueList() {
    catalogueList({ catalogueType: catalogueType.MEANS_CATALOGUE }).then(response => {
      catalogueTableData.value = response.rows
    })
  }

  async function getMeansInfo() {
    const queryData = {
      pageSize: pageSize.value,
      pageNum: pageNum.value,
      dataUserIds: userInfo.value.userId,
      manageName: searchValue.value,
      ...baseTypeFilterData.value,
      ...extraQueryData.value
    }
    const { rows, total } = await listMeans(queryData)
    dataTotal.value = total || 0
    meansList.value = rows
  }

  const extraQueryData = ref({})
  const arrowSortChange = value => {
    pageNum.value = 1
    extraQueryData.value = { ...value }
    getMeansInfo()
  }

  const baseTypeFilterData = ref({})
  const activeCatalogue = ref("")
  const activeType = ref("")

  const handleFilterChange = () => {
    pageNum.value = 1
    baseTypeFilterData.value = {
      catalogueId: activeCatalogue.value,
      manageType: activeType.value
    }
    getMeansInfo()
  }

  const getAssetURL = url => {
    if (url) return url
    return new URL(`@/assets/images/deault_means.png`, import.meta.url).href
  }

  onMounted(() => {
    getCatalogueList()
    getMeansInfo()
  })
</script>

<template>
  <div class="bg-white">
    <div class="p-4">
      <div class="mb-4">
        <span class="text-gray-600 text-16px mr-5 text-skyblueColor font-bold">目录：</span>
        <el-radio-group v-model="activeCatalogue" @change="handleFilterChange">
          <el-radio label="">全部</el-radio>
          <el-radio
            v-for="item in catalogueTableData"
            :key="item.catalogueId"
            :label="item.catalogueId"
            >{{ item.catalogueName }}</el-radio
          >
        </el-radio-group>
      </div>
      <div>
        <span class="text-gray-600 text-16px mr-5 text-skyblueColor font-bold">类型：</span>
        <el-radio-group v-model="activeType" @change="handleFilterChange">
          <el-radio label="">全部</el-radio>
          <el-radio v-for="item in sys_flie_type" :key="item.value" :label="item.value">
            {{ item.label }}
          </el-radio>
        </el-radio-group>
      </div>
    </div>

    <div class="flex justify-between items-center p-4 border-b-2px border-gray-200 border-b-dashed">
      <BaseArrowSort
        :data-list="[{ name: 'create_time', label: '按时间' }]"
        @sort="arrowSortChange"
      ></BaseArrowSort>
      <el-input
        v-model="searchValue"
        class="w-300px"
        :prefix-icon="Search"
        @keyup.enter="handleFilterChange"
        @clear="handleFilterChange"
        clearable
      ></el-input>
    </div>

    <div v-if="meansList.length > 0" class="p-5">
      <div
        v-for="item in meansList"
        :key="item.manageId"
        class="flex items-center pb-6 mb-6 border-b border-gray-200"
      >
        <div class="w-150px h-180px overflow-hidden rounded-lg">
          <img :src="getAssetURL(item.cover)" alt="" class="w-full h-full object-cover" />
        </div>
        <div class="flex-1 ml-6 h-180px flex justify-between">
          <div class="flex flex-col justify-around">
            <div class="text-24px font-bold mb-4">
              {{ item.manageName }}
            </div>
            <div class="text-16px text-gray-600">
              <span class="mr-6">浏览({{ item.viewNumber || 0 }})</span>
              <span class="mr-6">赞({{ item.likeNumber || 0 }})</span>
              <span class="mr-6">下载({{ item.downloadNumber || 0 }})</span>
              <span>分享于{{ item.createTime }}</span>
            </div>
          </div>

          <div class="flex justify-between items-center mb-4">
            <div class="text-16px text-gray-600 w-70%">{{ item.profile }}</div>
            <el-button type="primary" @click="filePreview(item)">
              查看详情
              <el-icon class="ml-2"><ArrowRight /></el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <el-empty v-else description="暂无数据" :image-size="200"></el-empty>
    <BasePagination
      class="pagination"
      v-if="meansList.length > 0"
      :total="dataTotal"
      v-model:pageNum="pageNum"
      v-model:pageSize="pageSize"
      @pagination="getMeansInfo"
    />
    <BaseFilePreview ref="filePreviewRef" @clickDownload="downloadCountRecord" />
  </div>
</template>

<style lang="less" scoped>
  :deep(.el-button) {
    min-width: 120px;
  }
</style>
