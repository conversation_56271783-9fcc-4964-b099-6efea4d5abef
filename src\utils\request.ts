import errorCode from "@/utils/errorCode"
import axios from "axios"
import { getToken } from "@/utils/auth"
import { tansParams, getSubdomain, pushToAdmin, blobValidate } from "@/utils/common"
import cache from "@/plugins/cache"
import { saveAs } from "file-saver"
import { ElMessageBox, ElMessage, ElNotification, ElLoading } from "element-plus"
import { productionEnvList } from "@/utils/constant"
import useTenantStore from "@/store/modules/tenant"
import useUserStore from "@/store/modules/user"
import { useRouter } from "vue-router"

// 创建 axios 实例
const service = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API + "",
  timeout: 30000
})
const loginPageUrl = import.meta.env.VITE_APP_LOGIN_URL
const redirectUrl = import.meta.env.VITE_APP_REDIRECT_URL
const env = import.meta.env.VITE_APP_ENV
const subDomain = getSubdomain()
let downloadLoadingInstance
let loadingInstance
const dontNeedLoadingApi = [
  "/course/progress/saveOrUpdate",
  "/exam/question-answer",
  "/course/answer"
]

// 是否显示重新登录
export let isRelogin = { show: false }

// 官方说明：https://pinia.vuejs.org/core-concepts/outside-component-usage.html
// ❌ 非组件中，常见错误写法
// const { member } = useStore();

// request拦截器
service.interceptors.request.use(
  (config: any) => {
    if (!dontNeedLoadingApi.includes(config.url)) {
      loadingInstance = ElLoading.service({
        text: "正在加载，请稍候",
        background: "rgba(0, 0, 0, 0.3)"
      })
    }
    // 是否需要设置 token
    const isToken = (config.headers || {}).isToken === false
    // 是否需要防止数据重复提交
    // const isRepeatSubmit = (config.headers || {}).repeatSubmit === false
    if (getToken() && !isToken) {
      config.headers!["Authorization"] = "Bearer " + getToken() // 让每个请求携带自定义token 请根据实际情况自行修改
    }
    if (!productionEnvList.includes(env)) {
      const tenantStore = useTenantStore()
      const testTenant = tenantStore.domainName ?? localStorage.getItem("testTenant")
      if (testTenant) {
        config.headers!["Test-Tenant"] = testTenant
        config.headers!["Tenant"] = testTenant
      }
    }
    // get请求映射params参数
    if (config.method === "get" && config.params) {
      let url = config.url + "?" + tansParams(config.params)
      url = url.slice(0, -1)
      config.params = {}
      config.url = url
    }
    // 暂时注释禁止重复提交逻辑
    // if (!isRepeatSubmit && (config.method === "post" || config.method === "put")) {
    //   const requestObj = {
    //     url: config.url,
    //     data: typeof config.data === "object" ? JSON.stringify(config.data) : config.data,
    //     time: new Date().getTime()
    //   }
    //   const sessionObj = cache.session.getJSON("sessionObj")
    //   if (sessionObj === undefined || sessionObj === null || sessionObj === "") {
    //     cache.session.setJSON("sessionObj", requestObj)
    //   } else {
    //     const s_url = sessionObj.url // 请求地址
    //     const s_data = sessionObj.data // 请求数据
    //     const s_time = sessionObj.time // 请求时间
    //     const interval = 1000 // 间隔时间(ms)，小于此时间视为重复提交
    //     if (
    //       s_data === requestObj.data &&
    //       requestObj.time - s_time < interval &&
    //       s_url === requestObj.url
    //     ) {
    //       const message = "数据正在处理，请勿重复提交"
    //       console.warn(`[${s_url}]: ` + message)
    //       return Promise.reject(new Error(message))
    //     } else {
    //       cache.session.setJSON("sessionObj", requestObj)
    //     }
    //   }
    // }
    return config
  },
  error => {
    loadingInstance?.close()
    Promise.reject(error)
  }
)

// 添加响应拦截器
service.interceptors.response.use(
  res => {
    loadingInstance?.close()
    // 未设置状态码则默认成功状态
    const code = res.data.code || 200
    // 获取错误信息
    const msg = errorCode[code] || res.data.msg || errorCode["default"]
    // 二进制数据则直接返回
    if (res.request.responseType === "blob" || res.request.responseType === "arraybuffer") {
      return res.data
    }
    if (code === 401) {
      // 判断是否为pt租户，是的话不跳转到管理端
      const tenantStore = useTenantStore()
      const userStore = useUserStore()
      if (tenantStore.domainName === "pt") {
        userStore.logOutAction()
        // 触发自定义未登录事件，让组件知道用户未登录
        const customEvent = new CustomEvent("global-login-dialog", {
          detail: { source: "request", code: 401 }
        })
        window.dispatchEvent(customEvent)
        return Promise.reject("未登录或登录状态已过期")
      } else if (tenantStore.domainName === "pthtg") {
        const customEvent = new CustomEvent("pthtg-login", {
          detail: { source: "request", code: 401 }
        })
        window.dispatchEvent(customEvent)
        return Promise.reject("未登录或登录状态已过期")
      }

      if (!isRelogin.show) {
        isRelogin.show = true
        pushToAdmin()
      } else {
        ElMessageBox.alert("登录状态已过期，您可以继续留在该页面，或者重新登录", "系统提示", {
          confirmButtonText: "重新登录",
          cancelButtonText: "取消",
          type: "warning",
          beforeClose: (action, instance, done) => {
            isRelogin.show = false
            done()
          }
        })
          .then(() => {
            pushToAdmin()
            isRelogin.show = false
          })
          .catch(() => {
            isRelogin.show = false
          })
      }
      return Promise.reject("无效的会话，或者会话已过期，请重新登录。")
    } else if (code === 500) {
      ElMessage({ message: msg, type: "error", grouping: true })
      return Promise.reject(new Error(msg))
    } else if (code === 601) {
      ElMessage({ message: msg, type: "warning", grouping: true })
      return Promise.reject(new Error(msg))
    } else if (code !== 200) {
      ElNotification.error({ title: msg })
      return Promise.reject("error")
    } else {
      return Promise.resolve(res.data)
    }
  },
  error => {
    loadingInstance?.close()
    console.log("err" + error)
    let { message } = error
    if (message == "Network Error") {
      message = "后端接口连接异常"
    } else if (message.includes("timeout")) {
      message = "系统接口请求超时"
    } else if (message.includes("Request failed with status code")) {
      message = "系统接口" + message.substr(message.length - 3) + "异常"
    }
    ElMessage({ message: message, type: "error", duration: 5 * 1000, grouping: true })
    return Promise.reject(error)
  }
)

// 通用下载方法
export function download(url, params, filename, config) {
  downloadLoadingInstance = ElLoading.service({
    text: "正在下载数据，请稍候",
    background: "rgba(0, 0, 0, 0.7)"
  })
  return service
    .post(url, params, {
      transformRequest: [
        params => {
          return tansParams(params)
        }
      ],
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
      responseType: "blob",
      ...config
    })
    .then(async data => {
      const isLogin = await blobValidate(data)
      if (isLogin) {
        const blob = new Blob([data as any])
        saveAs(blob, filename)
      } else {
        const resText = await (data as any).text()
        const rspObj = JSON.parse(resText)
        const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode["default"]
        ElMessage.error(errMsg)
      }
      downloadLoadingInstance.close()
    })
    .catch(r => {
      console.error(r)
      ElMessage.error("下载文件出现错误，请联系管理员！")
      downloadLoadingInstance.close()
    })
}

export default service
