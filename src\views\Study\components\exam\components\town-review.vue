<script setup lang="ts">
  import type { examPaperAnswerType } from "@/types"
  import { getTownHistoryList } from "@/api/onlineExam"

  const router = useRouter()
  // 已完成考试列表
  const exerciseRecordData = ref<examPaperAnswerType[]>()
  const examRecordData = ref<examPaperAnswerType[]>()
  const loadRecordData = async () => {
    const { data } = await getTownHistoryList()
    exerciseRecordData.value = data["2066"]
      ?.map(item => {
        if (item.examStatus === "2") {
          return item
        }
      })
      .filter(ite => typeof ite !== "undefined")
    examRecordData.value = data["2057"]
      ?.map(item => {
        if (item.examStatus === "2") {
          return item
        }
      })
      .filter(ite => typeof ite !== "undefined")
  }

  const activeName = ref("1")

  // 查看试卷解析
  const pushToExamed = item => {
    router.push({
      path: "/examed",
      query: {
        baseId: item.baseId,
        arrangeId: item.arrangeId,
        paperAnswerId: item.paperAnswerId
      }
    })
  }

  loadRecordData()
</script>

<template>
  <div class="p10">
    <div class="config">
      <el-card class="box-card">
        <el-tabs v-model="activeName" class="demo-tabs">
          <el-tab-pane label="做题记录" name="1"> </el-tab-pane>
          <el-tab-pane label="考试记录" name="2"></el-tab-pane>
          <div class="configDetail">
            <div
              class="record"
              v-if="activeName == '1' ? exerciseRecordData && exerciseRecordData.length > 0 : examRecordData && examRecordData!.length > 0"
            >
              <div class="subTitle">
                <div class="subtitle-text">{{ activeName === "1" ? "做题记录" : "考试记录" }}</div>
                <div class="count-text">
                  共
                  {{ activeName == "1" ? exerciseRecordData?.length : examRecordData?.length }}
                  次考试
                </div>
              </div>

              <div class="recordList">
                <div
                  class="recordItem"
                  v-for="item in activeName == '1' ? exerciseRecordData : examRecordData"
                >
                  <span class="configText">
                    开考时间：
                    {{ item.startTime }}</span
                  >
                  <span class="configText">
                    交卷时间：
                    {{ item.submitTime }}</span
                  >
                  <span class="configText">
                    测试成绩：
                    <span :class="item.isPass === '0' ? 'redText' : ''">
                      {{ item.userPaperScore }}
                    </span>
                  </span>
                  <el-button type="primary" @click="pushToExamed(item)"> 查看详情 </el-button>
                </div>
              </div>
            </div>
            <div class="examRecordNull" v-else>
              <img src="@/assets/images/no_exam_detail.png" alt="" />
              <div>暂无测试详情</div>
            </div>
          </div>
        </el-tabs>
      </el-card>
    </div>
  </div>
</template>

<style scoped lang="less">
  .examInfo:hover {
    box-shadow: 0 0 10px 2px rgba(0, 0, 255, 0.5); /* 添加阴影效果 */
  }

  .examInfo {
    margin: 20px 0 30px 0;
    height: 210px;
    width: 100%;
    border-radius: 20px;
    background-color: #2299f1;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    color: #fff;
    transition: box-shadow 0.3s ease-in-out;

    .examInfo-title {
      font-size: 28px;
      font-weight: bold;
      margin-bottom: 20px;
    }

    .examInfo-info {
      display: flex;
      align-items: center;
      width: 340px;
      justify-content: space-evenly;
      margin-bottom: 20px;
      .yellow-text {
        color: #fcff2d;
        font-weight: bold;
      }
    }

    .examInfo-time {
      margin-bottom: 20px;
    }

    .examInfo-button {
      background-color: #fae8c6;
      color: #d01f25;
      padding: 15px 100px;
      cursor: pointer;
    }

    .examInfo-end {
      margin-bottom: 20px;
      font-size: 22px;
    }
  }

  .config {
    .box-card {
      min-height: 600px;
      border-radius: 20px;

      .demo-tabs {
        :deep(.el-tabs__item) {
          font-size: 16px;
        }
        .configDetail {
          padding: 20px;
          display: flex;

          .subTitle {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            font-size: 20px;
            color: #000;
            font-weight: bold;

            .count-text {
              font-size: 16px;
              margin-left: 50px;
            }
          }

          .configText {
            line-height: 35px;
            font-size: 17px;
            font-weight: 550;
          }
          .redText {
            color: red;
          }
        }

        .examRecordNull {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 450px;
          font-size: 18px;
          color: #d7d7d7;
          font-weight: bold;
          flex-direction: column;
          > img {
            width: 250px;
            margin-bottom: 30px;
          }
        }
        .rules {
          display: flex;
          flex-direction: column;
          margin-left: 30px;
          width: 55%;
        }
        .record {
          flex: 1;

          .recordList {
            display: flex;
            flex-wrap: wrap;
            .recordItem {
              margin: 0 130px 30px 20px;
              display: flex;
              justify-content: center;
              flex-direction: column;
              padding-left: 30px;
              width: 400px;
              height: 200px;
              border: 1px solid #eee;

              .el-button {
                margin: 5px 30px 5px 0;
                font-size: 16px;
              }
            }
          }
        }
      }
    }
  }
</style>
