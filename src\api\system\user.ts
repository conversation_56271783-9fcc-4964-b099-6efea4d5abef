/*
 * @Description: 系统模块相关API
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-23 14:45:23
 * @LastEditTime: 2025-06-23 10:00:42
 */
import request from "@/utils/request"

// 获取用户详细信息
export function getInfo() {
  return request({
    url: "/system/user/getInfo",
    method: "get"
  })
}

// 退出方法
export function logout() {
  return request({
    url: "/auth/logout",
    method: "delete"
  })
}

//编辑用户信息
export function editInfo(data) {
  return request({
    url: "/system/user",
    method: "put",
    data: data
  })
}

// 用户密码重置
export function updateUserPwd(oldPassword, newPassword) {
  const data = {
    oldPassword,
    newPassword
  }
  return request({
    url: "/system/user/profile/updatePwd",
    method: "put",
    params: data
  })
}

// 双控跳转自动登录
export function autoLogin(data) {
  return request({
    url: "/auth/sysL<PERSON>",
    method: "post",
    data: data
  })
}

// 采集人脸
export function faceCollection(data) {
  return request({
    headers: {
      "Content-Type": "multipart/form-data"
    },
    url: "/system/user/faceCollection",
    method: "post",
    data: data
  })
}

// 采集视频中人脸
export function faceVerify(data) {
  return request({
    headers: {
      "Content-Type": "multipart/form-data"
    },
    url: "/system/user/faceVerify",
    method: "post",
    data: data
  })
}

// 获取用户积分、学分、学时
export function getUserExtraInfo() {
  return request({
    url: "/system/user/extraInfo",
    method: "get"
  })
}

// 用户密码重置
export function resetUserPwd(userId, password) {
  const data = {
    userId,
    password
  }
  return request({
    url: "/system/user/resetPwd",
    method: "put",
    data: data
  })
}
