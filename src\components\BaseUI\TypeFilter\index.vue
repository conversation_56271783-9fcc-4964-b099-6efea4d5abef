<!--
 * @Description: 类型条件筛选栏
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-12 17:07:23
 * @LastEditTime: 2024-11-14 15:36:08
-->
<script lang="ts" setup>
  import type { typeFilterType } from "@/types"

  interface Emit {
    (e: "switch", value: any): void
  }
  const emit = defineEmits<Emit>()

  const props = defineProps({
    dataList: {
      type: Array as PropType<typeFilterType[]>,
      default: () => []
    }
  })
  let filterParams = ref({})

  const switchFilter = (paramName, item?) => {
    if (!item) filterParams.value[paramName] = null
    filterParams.value[paramName] = item
    emit("switch", filterParams.value)
  }

  const typeFilterList = ref<typeFilterType[]>([])

  const isReplace = ref(false)
  watch(
    () => props.dataList,
    newVal => {
      const isRenderList = newVal.every(item => {
        return item.optionsList?.length > 0
      })
      if (isRenderList && !isReplace.value) {
        isReplace.value = true
        typeFilterList.value = newVal.map(item => {
          // 没有全部选项的时候特殊处理，默认选中第一个
          if (item.isShow === false) {
            filterParams.value[item.backParamField] = item.optionsList?.[0]?.[item.comparisonField]
          }
          return {
            ...item,
            rotate: false
          }
        })
      }
    },
    { deep: true, immediate: true }
  )

  const ulRef = ref()
  const changeHeight = (item, index) => {
    item.rotate = !item.rotate
    if (!ulRef?.value[index].style.height || ulRef?.value[index].style.height === "35px") {
      nextTick(() => {
        let autoHeight = ulRef?.value[index].scrollHeight
        ulRef.value[index].style.height = autoHeight + "px"
      })
    } else {
      ulRef.value[index].style.height = "35px"
    }
  }

  defineExpose({
    switchFilter
  })
</script>
<template>
  <div class="type-filter-container" v-if="typeFilterList">
    <div class="type-filter" v-for="(item, index) in typeFilterList">
      <!-- <el-divider v-if="index !== 0" /> -->
      <div class="filter-item">
        <div class="filter-title">{{ item.title }}：</div>
        <ul
          ref="ulRef"
          class="certain-type"
          :style="`border-bottom:${
            index === dataList?.length - 1 ? 'none' : '1px dashed #cccccc'
          };}`"
        >
          <li
            v-if="item.isShow !== false"
            :class="{
              active:
                filterParams[item.backParamField] === null ||
                filterParams[item.backParamField] === undefined
            }"
            class="HzqLi"
            @click="switchFilter(item?.backParamField, null)"
          >
            全部
          </li>
          <li
            v-for="(item2, index2) in item.optionsList"
            :class="{
              active: filterParams[item.backParamField] == item2[item.comparisonField]
            }"
            @click="switchFilter(item.backParamField, item2[item.comparisonField])"
            class="HzqLi"
          >
            {{ item2[item.labelField] }}
          </li>
        </ul>
        <div
          class="arrow"
          v-if="item.optionsList.length > 10"
          @click="changeHeight(item, index)"
          :class="{ rotate: item.rotate, rotateBack: !item.rotate }"
        >
          <span class="arrow-right"></span>
          <span class="arrow-right"></span>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
  .type-filter-container {
    width: 100%;
    margin: 0 auto;
  }
  .filter-item {
    display: flex;
    padding-left: 15px;
    position: relative;
    width: 100%;
    .filter-title {
      display: flex;
      justify-content: start;
      margin-top: 17px;
      width: 80px;
      font-weight: bold;
    }
    .certain-type {
      display: flex;
      flex-wrap: wrap;
      margin-left: 10px;
      margin-top: 15px;
      margin-bottom: 5px;
      height: 35px;
      width: 95%;
      transition: height 0.6s ease;
      overflow: hidden;
    }
  }

  .active {
    background-color: @warmOrange;
    color: white;
    border-radius: 5px;
  }

  :deep(.el-divider--horizontal) {
    border-top: 1px solid #f1f1f1;
  }

  .arrow {
    cursor: pointer;
    display: flex;
    transform: rotate(90deg);
    position: absolute;
    top: 13px;
    right: 0;
  }

  .arrow-right {
    height: 20px;
    width: 7px;
    display: inline-block;
    position: relative;
  }

  .arrow-right::after {
    content: "";
    height: 10px;
    width: 10px;
    top: 10px;
    border-width: 2px 2px 0 0;
    border-color: #ccc;
    border-style: solid;
    transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
    position: absolute;
  }

  .rotate {
    transform-origin: 50% 100%;
    transform: rotate(270deg);
    transition: all 0.3s;
  }

  .rotateBack {
    transform: rotate(90deg);
    transition: all 0.3s;
  }
</style>
