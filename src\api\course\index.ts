/*
 * @Description: 课程相关API
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-05 14:24:18
 * @LastEditTime: 2024-12-19 09:22:11
 */
import request from "@/utils/request"

// 课程换一换
export function courseRandomizer() {
  return request({
    url: "/course/base/courseRandomizer",
    method: "get"
  })
}

// 上方提醒去学习信息获取
export function baseRemind() {
  return request({
    url: "/course/base/remind",
    method: "get"
  })
}

// 课程列表
export function fetchCourseList(query: Object) {
  return request({
    url: "/course/base/list",
    method: "get",
    params: query
  })
}

//通过 ID 获取课程信息
export function getCourse(courseId) {
  return request({
    url: "/course/base/" + courseId,
    method: "get"
  })
}

// 我的课程列表
export function myCourseList(params: Object) {
  return request({
    url: "/course/progress/list",
    method: "get",
    params
  })
}

// 获取课程学习进度详细信息
export function getCourseStats(courseId, params?) {
  return request({
    url: "/course/stats/" + courseId,
    method: "get",
    params
  })
}

// 更新课程的学习进度
export function updateCourseProgress(data) {
  return request({
    url: "/course/progress/saveOrUpdate",
    method: "post",
    data: data
  })
}
// 传参：
//  courseId 课程Id
//  passageId 课程章节Id
//  learnFileType   “0” 字符串
//  deltaDuration   增量时间，非视频为0，视频就传新增的看视频时长
//  mediaProgress 视频播放节点，非视频为0，视频就传已看到的视频节点
//  mediaDuration   视频总时长，非视频为0

// 防灾减灾-课程超市
export function fetchDisasterCourseList(params) {
  return request({
    url: "/course/base/unepList",
    method: "get",
    params
  })
}
//防灾减灾-课程超市 总数与已学习数统计
export function fetchDisasterCourseCount(params?) {
  return request({
    url: "/course/passage/getStudyCount",
    method: "get",
    params
  })
}

// 课程热度埋点
export function courseBuriedPoint(courseId) {
  return request({
    url: "/course/base/editView/" + courseId,
    method: "get"
  })
}

// 课程收藏/取消收藏接口
export function editFavoriteOrNot(data) {
  return request({
    url: "/course/link/favoriteOrNot",
    method: "post",
    data: data
  })
}

// 清空课程章节进度
export function clearCourseProgress(data) {
  return request({
    url: "/course/stats/reset",
    method: "post",
    data
  })
}
