/*
 * @Description: 首页相关API
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-08-24 09:35:52
 * @LastEditTime: 2024-11-29 14:18:58
 */

import request from "@/utils/request"

// 首页最新动态
export function getLatestLearningLog(params?) {
  return request({
    url: "/course/study-log/getLatestLearningLog",
    method: "get",
    params
  })
}

// 首页热门课程
export function hotCourseList(params?) {
  return request({
    url: "/course/base/like",
    method: "get",
    params
  })
}

// 首页学习排行榜
export function getLearningRank(params?) {
  return request({
    url: "/course/study-log/getHoursRank",
    method: "get",
    params
  })
}

// 获取树结构目录
export function getCatalogueList(params?) {
  return request({
    url: "/system/catalogue/treeselect",
    method: "get",
    params
  })
}
// 积分排行
export function getDisasterRanking(params?) {
  return request({
    url: "/system/user/getUnepStudy<PERSON>ank",
    method: "get",
    params
  })
}
