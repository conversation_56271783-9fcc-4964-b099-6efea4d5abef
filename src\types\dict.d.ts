/*
 * @Description: dict.d.ts
 * @Author: <PERSON>
 * @LastEditors: <PERSON><PERSON>
 * @Date: 2023-03-22 17:12:59
 * @LastEditTime: 2023-06-09 15:36:27
 */
export type DictItemType = {
  value: string
  elTagType: string
  elTagClass: string
  label: string
}

export type CatalogueList = CatalogueItem[]
export type CatalogueItem = {
  catalogueId: number
  catalogueName: string
  isNew: boolean
}

export type LabelItem = {}
