<template>
  <div class="base-bread-item">
    <!--
      如果to存在 有值 我们就渲染一个router-link标签
      如果to不存在  那就渲染一个span标签
    -->
    <router-link v-if="to" :to="to"><slot></slot></router-link>
    <span v-else><slot></slot></span>
    <!-- 分隔符 -->
    <i v-if="separator">{{ separator }}</i>
    <i v-else class="iconfont icon-angle-right"></i>
  </div>
</template>

<script setup lang="ts">
import { inject } from "vue";
defineProps({
  to: {
    type: String,
  },
});
const separator = inject("separator");
</script>

<style scoped lang="less">
.base-bread-item {
  i {
    margin: 0 6px;
    font-size: 10px;
  }
  // 最后一个i隐藏
  &:nth-last-of-type(1) {
    i {
      display: none;
    }
  }
}
</style>
