<!--
 * @Description: 资料目录选择框
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-03 15:35:00
 * @LastEditTime: 2024-11-14 17:24:01
-->
<script lang="ts" setup name="menasCatalogue">
  import useStore from "@/store"

  const { means } = useStore()
  const { catalogueId, meansList, catalogueTableData } = storeToRefs(means)

  function getCatalogueList() {
    means.getMeansCatalogueList()
  }

  const menuSelect = async id => {
    if (catalogueId.value === id) return
    catalogueId.value = id
    await means.getMeansInfo()
  }

  watch(
    () => meansList.value,
    val => {
      catalogueTableData.value.forEach(item => {
        if (
          !meansList.value[0] ||
          !meansList.value[0].newUploadCatalogueList ||
          meansList.value[0].newUploadCatalogueList.length === 0
        )
          return
        if (meansList.value[0].newUploadCatalogueList.includes(item.catalogueId)) item.isNew = true
      })
    }
  )

  getCatalogueList()
</script>

<template>
  <div class="means-catalogue">
    <el-menu :default-active="0" class="catalogue-list" @select="menuSelect">
      <div class="Hzqh1">资料库</div>
      <el-menu-item :index="0">
        <span>全部分类</span>
      </el-menu-item>
      <template v-for="item in catalogueTableData">
        <el-menu-item :index="String(item.catalogueId)">
          <span>{{ item.catalogueName }}</span>
          <img class="img" src="@/assets/images/new.png" alt="" v-if="item.isNew" />
        </el-menu-item>
      </template>
    </el-menu>
  </div>
</template>

<style scoped lang="less">
  .catalogue-list {
    border: 1px solid #e8e8e8;
    border-radius: 7px;
    width: 300px;
    margin: 0 20px 0 0;
    padding: 10px 15px;
    flex-grow: inherit;

    .Hzqh1 {
      margin-left: 6px;
      margin-bottom: 10px;
      font-size: 20px !important;
      color: black !important;
      background-color: white !important;
    }
  }

  :deep(.el-menu-item) {
    font-size: 15px;
    /*     font-weight: bold; */
    height: 50px;
  }
  .is-active {
    color: @warmOrange;
    font-weight: bold;
  }

  .catalogue-list :hover {
    background-color: white;
    color: @warmOrange;
    font-size: 16px;
  }

  .img {
    width: 20px;
    margin-left: 10px;
  }
</style>
