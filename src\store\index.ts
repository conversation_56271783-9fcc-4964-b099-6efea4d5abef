/*
 * @Description: pinia-store入口文件
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-14 09:43:19
 * @LastEditTime: 2023-08-24 08:52:18
 */

// 合并 Pinia 模块
import useHomeStore from "./modules/home"
import useCategoryStore from "./modules/category"
import useUserStore from "./modules/user"
import useSingleRichTextStore from "./modules/singleRichText"
import useMeansStore from "./modules/means"

// 封装 useStore 合并管理所有模块
const useStore = () => {
  return {
    home: useHomeStore(),
    category: useCategoryStore(),
    user: useUserStore(),
    singleRichText: useSingleRichTextStore(),
    means: useMeansStore()
  }
}

// 默认导出
export default useStore
