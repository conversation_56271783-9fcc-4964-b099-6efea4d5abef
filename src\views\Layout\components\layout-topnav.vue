<!--
 * @Description: layout-topnav
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-14 09:43:19
 * @LastEditTime: 2025-06-30 08:57:46
-->
<script setup lang="ts">
  import { ElMessageBox } from "element-plus"
  import useStore from "@/store"
  import { pushToAdmin } from "@/utils/common"
  import useTenantStore from "@/store/modules/tenant"
  import AppHeaderNav from "./layout-header-nav.vue"
  import { Search } from "@element-plus/icons-vue"
  import { noNeedSystemLogoDomainList } from "@/utils/constant"
  import QrcodeVue from "qrcode.vue"
  const { user } = useStore()
  const tenantStore = useTenantStore()
  const { name, avatar, token, roles } = storeToRefs(user)
  const { tenantLogo } = storeToRefs(tenantStore)
  const router = useRouter()
  const route = useRoute()
  const { domainName } = storeToRefs(useTenantStore())

  // 判断是否是管理员角色
  const isAdmin = computed(() => {
    let hasAdminRole = false
    if (roles.value.length > 0) {
      roles.value.forEach(item => {
        if (item.includes("admin")) {
          hasAdminRole = true
        }
      })
    }
    return hasAdminRole
  })

  // 判断是否有退出登录按钮
  const canExit = computed(() => {
    return !localStorage.getItem("domainName")
  })

  // 判断是否已经登陆
  const eitherToken = computed(() => {
    return token.value || localStorage.getItem("token")
  })
  const toHome = () => {
    router.push({
      path: "/"
    })
  }

  const handleCommand = command => {
    switch (command) {
      case "pushAdmin":
        pushToAdmin("/index")
        break
      case "logout":
        logout()
        break
      default:
        break
    }
  }

  const logout = () => {
    ElMessageBox.alert("确定退出系统吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      confirmButtonClass: "logout-confirm-btn",
      type: "warning"
    })
      .then(() => {
        user.logOutAction().then(() => {
          location.href = "/index"
        })
      })
      .catch(() => {})
  }

  const registerOrLogin = () => {
    pushToAdmin()
  }

  const rotated = ref(false)
  const handleVisibleChange = val => {
    rotated.value = val
  }

  const courseName = ref("")
  const searchCourse = () => {
    router.push({
      path: "/course",
      query: {
        courseName: courseName.value
      }
    })
  }
  // 监听用户未授权事件
  const handleUserUnauthorized = () => {
    router.push("/pthtg-login")
  }

  const sysName = computed(() => {
    switch (domainName.value) {
      case "pthtg":
        return "上海市企业首席合同官培训认证平台"
      case "jd":
        return "嘉定镇街道防灾减灾知识学习（校园版）"
      case "ctmc":
        return "上海烟草机械有限责任公司在线安全教育学习平台"
      default:
        return "在线学习平台"
    }
  })

  // 添加全局事件监听
  onMounted(() => {
    window.addEventListener("pthtg-login", handleUserUnauthorized)
  })

  // 移除全局事件监听
  onUnmounted(() => {
    window.removeEventListener("pthtg-login", handleUserUnauthorized)
  })

  const mobileEntryUrl = computed(() => {
    // 获取当前域名并替换
    let host = window.location.hostname.replace(/edu(.*?)user/, "edu$1app")
    // 拼接协议、host、路径和参数
    return `${window.location.protocol}//${host}${window.location.pathname}${window.location.search}`
  })
</script>

<template>
  <nav
    class="bg-white fixed top-0 left-0 w-full z-100 shadow-[0_2px_15px_0_hsla(0,0%,45%,0.2)] h-70px flex justify-center items-center"
  >
    <div class="flex w-full mx-auto items-center px-48px">
      <!-- 左侧Logo区域 -->
      <div class="flex items-center flex-shrink-0" style="min-width: 280px">
        <h1 class="cursor-pointer flex items-center" @click="toHome">
          <RouterLink
            v-if="!noNeedSystemLogoDomainList.includes(domainName)"
            to="/"
            class="h-45px w-40px text-indent--9999px bg-no-repeat bg-center bg-contain mr-8px"
            :style="
              tenantLogo
                ? `background-image: url(${tenantLogo})`
                : `background-image: url('https://training-voc.obs.cn-north-4.myhuaweicloud.com:443/beckwell.png')`
            "
          ></RouterLink>
          <div
            class="text-20px font-bold text-#024c86 font-fangsong whitespace-nowrap"
            :class="domainName === 'jd' ? '' : 'border-l-3 border-#024c86 pl-8px'"
          >
            {{ sysName }}
          </div>
        </h1>
      </div>

      <!-- 中间导航和搜索区域 -->
      <div
        class="flex justify-center items-center flex-1"
        v-if="route.path !== '/examed' && domainName !== 'jd'"
      >
        <AppHeaderNav />

        <el-input
          v-model="courseName"
          class="!w-200px ml-15px"
          :prefix-icon="Search"
          @keyup.enter="searchCourse"
          placeholder="搜索课程"
          clearable
        ></el-input>
      </div>

      <!-- 右侧用户区域 -->
      <div
        class="flex items-center text-center text-deepColor text-16px cursor-pointer flex-shrink-0 gap-10px"
      >
        <el-popover placement="bottom" trigger="hover" width="180">
          <template #reference>
            <div class="mobile-entry flex items-center cursor-pointer">
              <el-icon :size="20" color="#67C23A"><Iphone /></el-icon>
              <span class="text-sm font-medium text-gray-700">手机端入口</span>
            </div>
          </template>
          <div class="flex flex-col items-center p-4 rounded-md shadow-md bg-white">
            <div class="qr-code-wrapper">
              <qrcode-vue :value="mobileEntryUrl" :size="120" />
            </div>
            <p class="text-xs text-gray-500 mt-2">扫码访问手机端</p>
          </div>
        </el-popover>
        <div to="/login" v-if="!eitherToken" @click="registerOrLogin">
          <i class="iconfont icon-shouji"></i>注册/登录
        </div>

        <div class="flex h-40px justify-center items-center" v-else>
          <el-dropdown
            @visible-change="handleVisibleChange"
            @command="handleCommand"
            trigger="hover"
            :disabled="!isAdmin && !canExit"
          >
            <div class="relative flex justify-center items-center">
              <img :src="avatar" class="w-40px h-40px rounded-full mr-3px cursor-pointer" />
              <div class="ml-8px mr-8px font-bold">{{ name }}</div>
              <i
                v-if="isAdmin || canExit"
                class="iconfont icon-xiangxia cursor-pointer"
                :class="{
                  'transform rotate-180 transition-all duration-300 origin-center': rotated,
                  'transform rotate-0 transition-all duration-300 origin-center': !rotated
                }"
              ></i>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="pushAdmin" v-if="isAdmin">
                  <span>管理员专区</span>
                </el-dropdown-item>
                <el-dropdown-item command="logout" v-if="canExit">
                  <span>退出登录</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>
  </nav>
</template>

<style lang="less" scoped>
  .mobile-entry {
    padding: 8px 12px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
  }

  .mobile-entry:hover {
    background-color: rgba(0, 0, 0, 0.05); /* 轻微的背景色变化 */
  }

  .qr-code-wrapper {
    border: 1px solid #eee;
    padding: 4px;
    border-radius: 4px;
  }
  :deep(.el-input__wrapper) {
    border-radius: 30px;
  }

  // 移除下拉菜单触发器的边框
  :deep(.el-dropdown) {
    .el-dropdown-selfdefine:focus {
      outline: none !important;
    }
  }

  // 覆盖鼠标悬停时的边框样式
  :deep(.el-popper) {
    border: none !important;
  }

  // 移除用户头像区域的边框
  .el-dropdown:hover,
  .el-dropdown:focus,
  .el-dropdown:focus-visible {
    outline: none !important;
    border: none !important;
    box-shadow: none !important;
  }

  // 特别针对用户头像区域
  .el-dropdown .relative {
    &:hover,
    &:focus,
    &:focus-visible {
      outline: none !important;
      border: none !important;
      box-shadow: none !important;
    }
  }

  // 响应式适配
  @media screen and (max-width: 1600px) {
    .flex.w-full {
      padding: 0 30px;
    }
    .flex.items-center:first-child {
      min-width: 260px;
    }
    h1 div {
      font-size: 18px;
    }
    .el-input {
      width: 180px !important;
      margin-left: 10px;
    }
  }

  @media screen and (max-width: 1440px) {
    .flex.w-full {
      padding: 0 20px;
    }
    .flex.items-center:first-child {
      min-width: unset;
    }
    h1 div {
      font-size: 16px;
    }
    .el-input {
      width: 160px !important;
      margin-left: 8px;
    }
  }

  @media screen and (max-width: 1200px) {
    .flex.w-full {
      padding: 0 15px;
    }
    .flex.items-center:first-child {
      min-width: 200px;
    }
    h1 div {
      font-size: 14px;
    }
    .flex.items-center:last-child {
      font-size: 14px;
      gap: 8px;
      .font-bold {
        display: none;
      }
    }
    .el-input {
      width: 140px !important;
      margin-left: 5px;
    }
  }
</style>
