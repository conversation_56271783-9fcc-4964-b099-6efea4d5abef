/*
 * @Description:
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-22 13:56:47
 * @LastEditTime: 2024-11-29 14:44:56
 */
import request from "@/utils/request"

//获取问卷题目
export function getQuestionList(query) {
  return request({
    url: "/course/question/paperList",
    method: "get",
    params: query
  })
}

//问卷信息
export function getQuestionnaire(query) {
  return request({
    url: "/course/issued/list",
    method: "get",
    params: query
  })
}

// 我的问卷列表
export function myList(query: Object) {
  return request({
    url: "/course/questionnaire/myList",
    method: "get",
    params: query
  })
}

//通过ID获取问卷信息
export function getQuestionById(questionnaireId) {
  return request({
    url: "/course/questionnaire/" + questionnaireId,
    method: "get"
  })
}

// 提交问卷
export function submitQuestionnaire(data) {
  return request({
    url: "/course/questionnaire/submit",
    method: "post",
    data: data
  })
}

// 每道题目完成
export function addAnswer(data) {
  return request({
    url: "/course/answer",
    method: "post",
    data: data
  })
}
