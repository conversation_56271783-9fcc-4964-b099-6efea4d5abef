<!--
 * @Description: 课程超市
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-17 08:49:10
 * @LastEditTime: 2025-03-04 13:34:09
-->
<script lang="ts" setup>
  import { fetchCourseList } from "@/api/course"
  import { catalogueList } from "@/api/system/catalogue"
  import type { classType, CatalogueItem } from "@/types"
  import { catalogueType } from "@/constants"
  import { BaseTypeFilter } from "@/components/BaseUI"
  import { ElMessageBox } from "element-plus"
  import useTenantStore from "@/store/modules/tenant"

  const tenantStore = useTenantStore()
  const { proxy } = getCurrentInstance()!
  const { course_type, course_label_type, course_level_type } = proxy!.useDict(
    "course_type",
    "course_label_type",
    "course_level_type"
  )
  const route = useRoute()
  const router = useRouter()
  const videoList = ref<classType[]>([])
  const catalogueTypeList = ref<CatalogueItem[]>([])

  const queryParams = ref({
    pageNum: 1,
    pageSize: 20,
    courseName: ""
  })
  const dataTotal = ref<number>(0)

  async function fetchData() {
    const queryData = {
      ...baseTypeFilterData.value,
      ...extraQueryData.value,
      fromType: "learning",
      courseStatus: "2",
      ...queryParams.value
    }
    const { rows, total } = await fetchCourseList(queryData)
    dataTotal.value = total || 0
    videoList.value = rows
  }

  const extraQueryData = ref({})
  const arrowSortChange = value => {
    extraQueryData.value = { ...value }
    fetchData()
  }

  const baseTypeFilterData = ref({})
  const baseTypeFilterChange = value => {
    baseTypeFilterData.value = { ...value }
    fetchData()
  }

  const dontShowCourseType = ["柏科内部课程", "宣传", "培训学习类"]
  async function fetchCatalogueData() {
    const queryData = {
      catalogueType: catalogueType.COURSE_CATALOGUE
    }
    const { rows } = await catalogueList(queryData)
    // 2024.10.14需求：课程分类不展示["柏科内部课程","宣传","培训学习类"]
    catalogueTypeList.value = rows.filter(item => {
      if (dontShowCourseType.includes(item.catalogueName)) return false
      return true
    })
  }
  const baseTypeFilterRef = ref()
  const manualFilter = ref(false)
  // 获取参数
  const getQuery = () => {
    // 获取当前页面URL
    const queryString = window.location.href.split("?")[1]
    // let urlNoParams = window.location.href.split("?")[0]
    if (!queryString) return
    // 创建URLSearchParams对象
    const urlParams = new URLSearchParams(queryString)

    // 获取参数值
    const courseName = urlParams.get("courseName")
    const catalogueId = urlParams.get("catalogueId")
    if (courseName) {
      queryParams.value.courseName = courseName
    }
    if (catalogueId) {
      manualFilter.value = true
      nextTick(() => {
        baseTypeFilterRef.value.switchFilter("catalogueId", catalogueId)
      })
    }
  }

  // 添加课程点击处理方法
  const handleCourseClick = (courseItem: any, event: Event) => {
    event.preventDefault() // 阻止默认的路由跳转行为
    const { courseId, hasTask } = courseItem
    const taskId = tenantStore.domainName === "pt" ? 1 : route.query.taskId

    // 如果课程有任务，跳转到待办页面
    if (hasTask) {
      router.push({
        path: "/study/todo"
      })
      return
    }

    // 否则跳转到课程详情页
    const routeData = router.resolve({
      path: "/course/detail",
      query: {
        id: courseId,
        // @ts-ignore
        ...(taskId && { taskId })
      }
    })
    window.open(routeData.href, "_blank")
  }

  onMounted(() => {
    fetchCatalogueData()
    getQuery()
    if (!manualFilter.value) {
      fetchData()
    }
  })
</script>

<template>
  <div class="course-container">
    <div class="right-head">
      <BaseTypeFilter
        ref="baseTypeFilterRef"
        class="filter"
        :dataList="[
          {
            title: '课程分类',
            optionsList: catalogueTypeList,
            labelField: 'catalogueName',
            comparisonField: 'catalogueId',
            backParamField: 'catalogueId'
          },
          // {
          //   title: '课程标签',
          //   optionsList: course_label_type,
          //   labelField: 'label',
          //   comparisonField: 'value',
          //   backParamField: 'courseLabel'
          // },
          {
            title: '课程类型',
            optionsList: course_type,
            labelField: 'label',
            comparisonField: 'value',
            backParamField: 'courseType'
          },
          {
            title: '课程等级',
            optionsList: course_level_type,
            labelField: 'label',
            comparisonField: 'value',
            backParamField: 'courseLevel'
          }
        ]"
        @switch="baseTypeFilterChange"
      />
    </div>
    <div class="container">
      <div class="home-panel">
        <div class="container">
          <div class="head">
            <div class="title"> <i class="iconfont icon-jiaoxuefansi"></i>全部课程</div>
          </div>
          <!-- 主体内容区域 -->
          <div class="course-sort">
            <BaseArrowSort
              :data-list="[
                { name: 'a.update_time', label: '按时间' },
                { name: 'hot_value', label: '按热度' }
              ]"
              @sort="arrowSortChange"
            ></BaseArrowSort>
          </div>
          <div class="course-list" v-if="videoList.length > 0">
            <div class="course-item" v-for="item in videoList" :key="item.courseId">
              <RouterLink
                :to="'/course/detail?id=' + item.courseId"
                target="_blank"
                @click="handleCourseClick(item, $event)"
              >
                <div class="course-img">
                  <img :src="item.courseImage" alt="" />
                </div>
                <div class="foot">
                  <div class="meta ellipsis">{{ item.courseName }} </div>
                  <div class="info">
                    <div class="info-left">
                      <div class="lecturer" v-if="item.lecturer">
                        <i class="iconfont icon-yonghu"></i>
                        {{ item.lecturer }}
                      </div>
                      <div class="hot">
                        <i class="iconfont icon-remen"></i>
                        {{ item.hotValue || 0 }}</div
                      >
                    </div>
                    <div class="info-right">
                      <el-button type="primary" class="study_immediate"> 立即学习 </el-button>
                    </div>
                  </div>
                </div>
              </RouterLink>
            </div>
          </div>
          <el-empty
            class="empty-container"
            v-else
            description="暂无数据"
            :image-size="200"
          ></el-empty>
        </div>
      </div>
    </div>
  </div>
  <BasePagination
    class="pagination"
    v-if="videoList.length > 0"
    :total="dataTotal"
    v-model:pageNum="queryParams.pageNum"
    v-model:pageSize="queryParams.pageSize"
    @pagination="fetchData"
  />
</template>

<style scoped lang="less">
  .home-panel {
    /*  margin-top: 30px; */
    padding: 30px 0 60px 0;

    .head {
      padding-bottom: 20px;
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      .title {
        font-size: 20px;
        font-weight: 550;

        line-height: 34px;

        .iconfont {
          color: @warmOrange;
          font-size: 24px;
          margin-right: 12px;
          vertical-align: middle;
        }
      }
    }
  }
  .empty-container {
    padding-bottom: 200px;
  }
  .course-container {
    .right-head {
      background-color: #fff;
      width: 100%;
      margin: 0 auto;
      position: relative;
      border-bottom: 1px solid #dddddd;
      .filter {
        width: 1240px;
      }
    }
  }

  :deep(.el-divider--horizontal) {
    margin: 0;
  }

  :deep(.el-input__wrapper) {
    border-radius: 30px;
  }
  .active {
    background-color: @warmOrange;
    color: white;
    font-weight: bold;
    border-radius: 5px;
  }

  .pagination {
    background-color: #f4fafe;
    padding-bottom: 100px;
  }

  .course-sort {
    margin-bottom: 20px;
  }

  .disaster-tips {
    margin: 0 0 20px 30px;
    font-size: 15px;
  }

  .course-list {
    padding: 0;
    display: grid;
    gap: 20px;
    grid-template-columns: repeat(4, 1fr);
    .course-item {
      box-shadow: 0 2px 10px 0 rgba(103, 111, 144, 0.15);
      box-sizing: border-box;
      border-radius: 5px;
      width: 290px;
      background: #fff;
      margin-bottom: 10px;
      cursor: pointer;
      .hoverShadow();
      a {
        display: block;
        width: 100%;
        position: relative;
        img {
          border-radius: 5px;
          width: 100%;
          height: 160px;
        }
      }
      .foot {
        padding: 13px;
        font-size: 16px;
        .meta {
          margin-bottom: 10px;
          font-size: 16px;
          font-weight: 500;
        }
        // 一行省略
        .ellipsis {
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }

        .info {
          display: flex;
          align-items: center;
          justify-content: space-between;
          &-left {
            display: flex;
            font-size: 13px;

            .iconfont {
              color: @warmOrange;
              font-weight: bold;
            }

            .lecturer {
              margin-right: 15px;
            }
          }
          &-right {
            .study_immediate {
              background: #eff4fe;
              border-radius: 5px;
              color: #666;
              font-family: Microsoft YaHei;
              font-size: 14px;
              font-weight: 400;
              height: 28px;
              line-height: 28px;
              text-align: center;
              width: 88px;
              border: none;
            }
          }
        }
      }
    }

    .course-item:hover .study_immediate {
      background: @warmOrange !important;
      color: #fff !important;
    }
  }

  .home-skeleton {
    width: 1240px;
    height: 365px;
    display: flex;
    justify-content: space-between;
    margin-bottom: 300px;
    .item {
      width: 306px;

      .base-skeleton ~ .base-skeleton {
        display: block;
        margin: 5px 0 0 0;
      }
    }
  }
</style>
