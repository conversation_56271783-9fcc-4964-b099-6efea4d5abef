<!--
 * @Description: layout-header-nav
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-17 08:49:10
 * @LastEditTime: 2025-06-25 09:05:58
-->
<script lang="ts" setup>
  import useStore from "@/store"
  import { baseRemind } from "@/api/course"
  import useTenantStore from "@/store/modules/tenant"
  import { specialTenantList } from "@/utils/constant"

  const { category } = useStore()
  const { domainName } = storeToRefs(useTenantStore())
  const router = useRouter()
  const route = useRoute()
  const tipRef = ref()
  const remindItem = ref()

  const handleRoute = () => {
    router.push({
      path: "/course/detail",
      query: { id: remindItem.value?.courseId }
    })
    tipRef.value?.[0]?.remove()
  }
  // 菜单跳转 隐藏提示
  const handleTo = item => {
    if (item.id === 12) {
      tipRef.value?.[0]?.remove()
    }
  }
  const getBaseRemind = async () => {
    const { rows } = await baseRemind()
    if (rows && rows.length > 0) {
      remindItem.value = rows[0]
    } else {
      remindItem.value = ""
    }
  }

  const handleClose = () => {
    tipRef.value?.[0]?.remove()
  }

  onMounted(() => {
    if (
      !specialTenantList.includes(domainName.value) &&
      domainName.value !== "pt" &&
      route.path !== "/course/detail"
    ) {
      getBaseRemind()
    }
  })
</script>

<template>
  <ul class="flex relative z-998 items-center h-70px">
    <li
      v-for="item in category.categoryList"
      :key="item.id"
      class="mr-20px w-65px text-center relative h-70px leading-70px"
    >
      <RouterLink
        :to="item.url"
        class="nav-link"
        :class="{ 'nav-link-active': $route.path === item.url }"
      >
        <span @click="handleTo(item)" class="inline-block w-full h-full">
          {{ item.name }}
        </span>
      </RouterLink>
      <div
        v-if="item.id === 12 && remindItem && route.path !== '/course/detail'"
        ref="tipRef"
        class="z-9999 absolute top-60px left--98px bg-gradient-to-b from-[#f56b1f] to-[#f68621] p-y-6px pl-27px pr-40px pb-10px text-white text-14px rounded-6px w-504px text-left leading-20px shadow-[0_2px_10px_0_rgba(245,107,31,0.5)]"
      >
        <div class="absolute cursor-pointer top-8px right-8px" @click="handleClose">
          <el-icon><CircleClose /></el-icon>
        </div>

        <i class="iconfont icon-xuexi_nor text-[#fbed94] font-200 align-bottom pr-4px"></i>
        <span class="text-[#fbed94]">
          您有一条新的任务需要完成，学习《{{ remindItem?.courseName }}》任务待完成，
        </span>
        <span
          class="border-b-[0.5px] border-b-solid border-b-white pb-2px cursor-pointer text-white"
          @click="handleRoute"
        >
          快去学习吧
        </span>
      </div>
    </li>
  </ul>
</template>

<style lang="less" scoped>
  /* 添加全局样式来确保边框显示 */
  .router-link-active {
    color: var(--skyblueColor) !important;
    border-bottom: 3px solid var(--skyblueColor) !important;
  }

  .router-link-active:hover {
    color: var(--skyblueColor) !important;
    border-bottom: 3px solid var(--skyblueColor) !important;
  }
</style>
