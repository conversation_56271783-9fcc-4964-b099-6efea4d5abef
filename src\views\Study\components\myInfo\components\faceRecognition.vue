<!--
 * @Description: 人脸识别页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-05 14:41:43
 * @LastEditTime: 2024-12-24 15:20:41
-->

<script setup lang="ts">
  import { CameraFilled, Close, Select } from "@element-plus/icons-vue"
  import { faceCollection } from "@/api/system/user"
  import { ElMessage } from "element-plus"
  import useUserStore from "@/store/modules/user"
  import { dataURLtoFile } from "@/utils/common"

  let userStore = useUserStore()
  const { faceImg } = storeToRefs(userStore)

  const showCamera = ref("1")
  const imageRef = ref<HTMLImageElement | null>(null)
  const videoRef = ref<HTMLVideoElement | null>(null)
  const canvasRef = ref<HTMLCanvasElement | null>(null)
  const mediaStreamTrack = ref()

  const startFaceDetection = () => {
    showCamera.value = "2"
    // 使用getUserMedia获取视频流
    navigator.mediaDevices
      .getUserMedia({
        video: true,
        audio: false
      })
      .then(function (stream) {
        // 将视频流绑定到video元素上
        videoRef.value!.srcObject = stream
        videoRef.value!.play()
        mediaStreamTrack.value = stream
      })
      .catch(function (error) {
        ElMessage({
          message: "无法获取摄像头视频流",
          type: "error"
        })
        showCamera.value = "1"
      })
  }

  let blobFile = ref()
  let dataurl = ref()
  const takePhoto = () => {
    showCamera.value = "3"
    canvasRef.value!.getContext("2d")!.drawImage(videoRef.value!, 0, 0, 300, 200)
    dataurl.value = canvasRef.value!.toDataURL("image/jpeg")
    blobFile.value = dataURLtoFile(
      dataurl.value,
      `${userStore.userInfo.userName}-selfProtrait-${new Date().getTime()}.jpg`
    )
    mediaStreamTrack.value.getTracks()[0].stop()
  }

  const handleSave = () => {
    // 调用接口上传图片
    const formData = new FormData()
    formData.append("file", blobFile.value)
    faceCollection(formData)
      .then(res => {
        showCamera.value = "1"
        ElMessage({
          message: `上传个人信息成功！`,
          type: "success"
        })
        userStore.getInfo()
      })
      .catch(err => {
        showCamera.value = "1"
      })
  }

  const getAssetURL = url => {
    if (url) return url
    return new URL("@/assets/images/face.png", import.meta.url).href
  }

  const cancel = () => {
    mediaStreamTrack.value?.getTracks()[0].stop()
    showCamera.value = "1"
  }
</script>

<template>
  <div class="info">
    <img
      class="video"
      v-show="showCamera === '1'"
      ref="imageRef"
      :src="getAssetURL(faceImg)"
      alt=""
    />
    <video
      class="video"
      v-show="showCamera === '2'"
      ref="videoRef"
      muted
      autoplay
      width="300"
      height="200"
    ></video>
    <canvas
      class="video"
      v-show="showCamera === '3'"
      ref="canvasRef"
      width="300"
      height="200"
    ></canvas>
    <div class="info-content">
      <div>您可以进行以下操作:</div>
      <el-button v-if="showCamera === '1'" :icon="CameraFilled" round @click="startFaceDetection">
        我要采样
      </el-button>
      <template v-else-if="showCamera === '2'">
        <el-button :icon="CameraFilled" round @click="takePhoto"> 拍摄 </el-button>
        <el-button :icon="Close" round @click="cancel"> 取消拍摄 </el-button>
      </template>
      <template v-else>
        <el-button :icon="CameraFilled" round @click="startFaceDetection"> 重新采样 </el-button>
        <el-button :icon="Select" round @click="handleSave"> 保存 </el-button>
      </template>
    </div>
  </div>
  <el-divider> 采集头像提醒 </el-divider>
  <div style="color: #a3a3a3; line-height: 30px">
    <div>1.请确保面部整洁干净，照片光线充足。</div>
    <div>2.请确保摄像头中的照片只有本人的正面照片。</div>
    <div>3.采集通过之后，如果对照片不满意可以反复采集，但保存后不可修改。</div>
    <div>4.保存采集后，管理员通过审核后方才可以使用。</div>
    <div>5.管理员审核后如果被驳回则可以再次采集本人人脸样本。</div>
  </div>
</template>

<style scoped lang="less">
  .info {
    display: flex;
    margin-left: 30px;
    margin-top: 25px;

    .info-content {
      margin-top: 20px;
      margin-left: 30px;
      > div {
        margin-bottom: 20px;
      }
    }
  }
  .video {
    transform: rotateY(180deg);
  }
</style>
