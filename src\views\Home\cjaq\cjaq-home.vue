<!--
 * @Description: 工会促进安全生产学习平台-首页
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-01-15 11:45:02
 * @LastEditTime: 2024-01-22 16:21:22
-->

<script lang="ts" setup>
  import { getCjaqExamInfo } from "@/api/onlineExam"
  import { ElMessage } from "element-plus"
  import type { examPaperAnswerType } from "@/types/study"

  const router = useRouter()
  const route = useRoute()
  const startLearn = () => {
    router.push("/safetyCourse")
  }
  const examData = ref<examPaperAnswerType>()
  const startExam = () => {
    if (!examData.value || !examData.value.baseId) {
      return ElMessage({ message: "暂无可用考试", type: "warning" })
    }
    router.push({
      path: "/prepare",
      query: {
        baseId: examData.value.baseId,
        arrangeId: examData.value.arrangeId
      }
    })
  }
  const fetchData = async () => {
    const { data } = await getCjaqExamInfo({
      taskId: route.query.taskId ? route.query.taskId : undefined
    })
    examData.value = data?.[0]
  }

  fetchData()
</script>
<template>
  <div class="prevent-disaster-home">
    <h2 class="title"> 工会促进安全生产学习平台 </h2>
    <div class="disaster-content">
      <div class="activity-rule">
        安全生产是关系到国家和人民群众生命财产的安全和人民群众的切身利益的大事;安全生产管理最根本的目的是保护人民的生命和健康，安全生产是对企业的最根本要求，安全管理是每个管理人员必须遵守的行为准则，这也是安全生产管理的重要内容。安全工作是一项常抓不懈的主题，是生产的保证，也是员工效益的最大体现。“安全”是我们年年讲、月月讲、时时讲、人人讲、大会讲、小会讲班前班后讲的共同主题，“安全”对于我们每一个人来说都是重要的。
      </div>
    </div>

    <div class="bottom-btn">
      <div style="background-color: #f59a23" @click="startLearn">学习</div>
      <div style="background-color: #75affd" @click="startExam">考试</div>
    </div>
  </div>
</template>

<style scoped lang="less">
  .prevent-disaster-home {
    padding: 30px 0 45px 0;
    width: 1240px;
    margin: 0 auto;
  }
  .title {
    color: #f59a23;
    font-size: 50px;
    font-weight: bolder;
    line-height: 1;
    margin-bottom: 50px;
    text-align: center;
    text-shadow: 2px 2px 2px #333333;
  }

  .disaster-content {
    width: 1240px;
    background-color: #fff;
    padding: 50px 80px;
    line-height: 30px;
    font-size: 15px;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
  }

  .bottom-btn {
    margin-top: 46px;
    display: flex;
    font-size: 24px;
    font-weight: bold;
    justify-content: space-around;
    align-items: center;
    flex-wrap: wrap;
    div {
      margin: 30px 80px 0 80px;
      height: 60px;
      width: 25%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      color: #fff;
    }
  }
</style>
