<!--
 * @Description: 公益宣传片
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-11-14 13:35:07
 * @LastEditTime: 2024-11-29 17:10:47
-->
<script setup lang="ts">
  import TrailerForm from "./TrailerForm.vue"
  import { listVideo, delVideo } from "@/api/trailer"
  import { Search, ArrowRight } from "@element-plus/icons-vue"
  import { useRouter } from "vue-router"
  import { getCurrentInstance } from "vue"
  import { DEFAULT_COVER } from "@/utils/constant"
  const trailerFormRef = ref()
  const { proxy } = getCurrentInstance()!
  // @ts-ignore
  const { public_trailer_type, review_status, review_result } = proxy?.useDict(
    "public_trailer_type",
    "review_status",
    "review_result"
  )
  const pageNum = ref<number>(1)
  const pageSize = ref<number>(8)
  const dataTotal = ref<number>(0)
  const searchValue = ref("")
  const trailerList = ref<any>([])
  const activeType = ref("")
  const activeStatus = ref("")

  async function fetchData() {
    const queryData = {
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      title: searchValue.value,
      ...baseTypeFilterData.value
    }
    const { rows, total } = await listVideo(queryData)
    dataTotal.value = total || 0
    trailerList.value = rows
  }

  const baseTypeFilterData = ref({})
  const handleFilterChange = () => {
    pageNum.value = 1
    baseTypeFilterData.value = {
      approveStatus: activeStatus.value,
      videoType: activeType.value
    }
    fetchData()
  }

  const handleDelete = async row => {
    try {
      await proxy?.$confirm("是否确认删除该宣传片？", "警告")
      await delVideo(row.psaId)
      proxy?.$message.success("删除成功")
      fetchData()
    } catch (error) {
      console.error(error)
    }
  }

  const handleView = row => {
    trailerFormRef.value.openDialog("detail", row)
  }

  const handlUpload = () => {
    trailerFormRef.value.openDialog("add")
  }

  onMounted(() => {
    fetchData()
  })
</script>

<template>
  <div class="bg-white">
    <div class="p-4">
      <div class="mb-4">
        <span class="text-gray-600 text-16px mr-5 text-skyblueColor font-bold">状态：</span>
        <el-radio-group v-model="activeStatus" @change="handleFilterChange">
          <el-radio label="">全部</el-radio>
          <el-radio v-for="item in review_status" :key="item.value" :label="item.value">
            {{ item.label }}
          </el-radio>
        </el-radio-group>
      </div>
      <div>
        <span class="text-gray-600 text-16px mr-5 text-skyblueColor font-bold">类型：</span>
        <el-radio-group v-model="activeType" @change="handleFilterChange">
          <el-radio label="">全部</el-radio>
          <el-radio v-for="item in public_trailer_type" :key="item.value" :label="item.value">
            {{ item.label }}
          </el-radio>
        </el-radio-group>
      </div>
    </div>

    <div class="flex justify-between items-center p-4 border-b-2px border-gray-200 border-b-dashed">
      <el-button class="!w-90px" type="primary" @click="handlUpload">
        上 传
        <el-icon class="ml-2"><Upload /></el-icon>
      </el-button>
      <el-input
        v-model="searchValue"
        class="w-300px"
        :prefix-icon="Search"
        @keyup.enter="handleFilterChange"
        @clear="handleFilterChange"
        clearable
      ></el-input>
    </div>

    <div v-if="trailerList.length > 0" class="p-5">
      <div
        v-for="item in trailerList"
        :key="item.psaId"
        class="flex items-center pb-6 mb-6 border-b border-gray-200 border-b-solid"
      >
        <div class="w-320px h-180px overflow-hidden rounded-lg">
          <img
            :src="item.cover || DEFAULT_COVER"
            :alt="item.title"
            class="w-full h-full object-cover"
          />
        </div>
        <div class="flex-1 ml-6 flex justify-between items-center">
          <div class="flex flex-col justify-between h-180px">
            <div class="flex items-center">
              <dict-tag class="mr-2" :options="public_trailer_type" :value="item.videoType" />
              <span class="text-24px font-bold">{{ item.title }}</span>
            </div>
            <div class="flex flex-col gap-2">
              <div class="text-16px text-gray-600">上传时间：{{ item.createTime }}</div>
              <div
                class="flex items-center text-16px text-gray-600"
                v-if="item.approveResult !== null"
              >
                <div>审核结果：</div>
                <dict-tag class="inline" :options="review_result" :value="item.approveResult" />
              </div>
            </div>
          </div>
          <div class="flex flex-col gap-4 justify-center items-center">
            <el-button type="primary" @click="handleView(item)">
              查看详情
              <el-icon class="ml-2"><ArrowRight /></el-icon>
            </el-button>
            <el-button v-if="item.approveResult === 0" type="danger" @click="handleDelete(item)">
              删 除
              <el-icon class="ml-2"><Delete /></el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <el-empty v-else description="暂无数据" :image-size="200"></el-empty>
    <BasePagination
      class="pagination"
      v-if="trailerList.length > 0"
      :total="dataTotal"
      v-model:pageNum="pageNum"
      v-model:pageSize="pageSize"
      @pagination="fetchData"
    />
    <TrailerForm ref="trailerFormRef" @success="fetchData" />
  </div>
</template>

<style lang="less" scoped>
  :deep(.el-button) {
    width: 110px;
  }
  :deep(.el-button + .el-button) {
    margin-left: 0;
  }
</style>
