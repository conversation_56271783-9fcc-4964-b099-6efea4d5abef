<!--
 * @Description: dict-tag
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-22 17:07:41
 * @LastEditTime: 2024-11-19 09:42:01
-->
<script setup lang="ts" name="DictTag">
  import { computed, type PropType } from "vue"
  import type { DictItemType } from "@/types"
  const props = defineProps({
    // 数据
    options: {
      type: Array as PropType<DictItemType[]>,
      default: null
    },
    // 当前的值
    value: [Number, String, Array]
  })

  const values = computed(() => {
    if (props.value !== null && typeof props.value !== "undefined") {
      return Array.isArray(props.value) ? props.value : [String(props.value)]
    } else {
      return []
    }
  })
</script>

<template>
  <div>
    <template v-for="(item, index) in options">
      <template v-if="values.includes(item.value)">
        <span
          v-if="item.elTagType == 'default' || item.elTagType == ''"
          :key="item.value"
          :index="index"
          :class="item.elTagClass"
          >{{ item.label }}</span
        >
        <el-tag
          v-else
          v-bind="$attrs"
          :disable-transitions="true"
          :key="item.value + ''"
          :index="index"
          :type="item.elTagType === 'primary' ? '' : item.elTagType"
          :class="item.elTagClass"
          >{{ item.label }}</el-tag
        >
      </template>
    </template>
  </div>
</template>

<style scoped>
  .el-tag + .el-tag {
    margin-left: 10px;
  }
</style>
