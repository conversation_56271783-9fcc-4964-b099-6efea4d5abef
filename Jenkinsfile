pipeline {
  agent {
    node {
      label 'nodejs'
    }

  }
  stages {
    stage('拉取阿里云效代码1') {
      agent none
      steps {
        container('nodejs') {
          git(url: 'https://codeup.aliyun.com/bw/beckwell-web-learning.git', credentialsId: 'aly-yunxiao-id', branch: 'master', changelog: true, poll: false)
          sh 'ls -al'
        }

      }
    }

  stage('nodejs编译') {
      agent none
      steps {
        container('nodejs') {
          // sh 'npm i node-sass --sass_binary_site=https://npm.taobao.org/mirrors/node-sass/'
          sh 'npm install -g typescript --registry=https://registry.npm.taobao.org'
          sh 'node -v'
          sh 'npm i'
          sh 'npm run build:prod'
          sh 'ls'
        }

      }
    }

    stage('构建learning镜像') {
      agent none
      steps {
         container('nodejs') {
            withCredentials([kubeconfigFile(credentialsId: 'kubeconfig',variable: 'KUBECONFIG')])
		    {
                sh 'envsubst < deploy/deploy.yaml | kubectl apply -f -'
			}
          }

      }
    }

   stage('推送learning镜像至Harbor') {
      agent none
      steps {
        container('nodejs') {
          withCredentials([usernamePassword(credentialsId : 'harbor-id' ,passwordVariable : 'HARBOR_PWD_VAL' ,usernameVariable : 'HARBOR_USER_VAL' ,)]) {
            sh 'echo "$HARBOR_PWD_VAL" | docker login $REGISTRY -u "$HARBOR_USER_VAL" --password-stdin'
            sh 'docker tag beckwell-web-learning:latest $REGISTRY/$DOCKERHUB_NAMESPACE/beckwell-web-learning:SNAPSHOT-$BUILD_NUMBER'
            sh 'docker push $REGISTRY/$DOCKERHUB_NAMESPACE/beckwell-web-learning:SNAPSHOT-$BUILD_NUMBER'
          }

        }

      }
    }

   stage('部署learning到生产环境') {
      agent none
      steps {
        kubernetesDeploy(configs: 'deploy/**', enableConfigSubstitution: true, kubeconfigId: "$KUBECONFIG_CREDENTIAL_ID")
      }
    }


    stage('发送确认邮件') {
      agent none
      steps {
        mail(to: '<EMAIL>', subject: '学习平台构建结果', body: '构建成功了')
      }
    }

  }
  environment {
    DOCKER_CREDENTIAL_ID = 'dockerhub-id'
    GITHUB_CREDENTIAL_ID = 'github-id'
    KUBECONFIG_CREDENTIAL_ID = 'demo-kubeconfig'
    REGISTRY = '*************:8084'
    DOCKERHUB_NAMESPACE = 'edu'
    GITHUB_ACCOUNT = 'kubesphere'
    APP_NAME = 'devops-java-sample'
  }
  parameters {
    string(name: 'TAG_NAME', defaultValue: '', description: '')
  }
}