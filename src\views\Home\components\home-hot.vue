<!--
 * @Description: 首页-热门课程
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-14 09:43:19
 * @LastEditTime: 2025-03-26 16:21:28
-->
<script lang="ts" setup>
  import { hotCourseList } from "@/api/home"
  import { ref } from "vue"
  import type { classType } from "@/types"

  const router = useRouter()
  const hotHandleList = ref<classType[]>()
  const getHotCourseList = async () => {
    const { data } = await hotCourseList()
    hotHandleList.value = data.slice(0, 3) || []
    if (hotHandleList.value?.length === 0) return
  }

  const getAssetUrl = index => {
    return new URL(`../../../assets/images/bg${index}.png`, import.meta.url).href
  }

  // 课程点击处理
  const handleCourseClick = (courseItem: any, event: Event) => {
    const { courseId, hasTask } = courseItem

    // 如果课程有任务，跳转到待办页面
    if (hasTask) {
      event.preventDefault()
      router.push({
        path: "/study/todo"
      })
      return
    }
    router.push(`/course/detail?id=${courseId}`)
  }

  getHotCourseList()
</script>
<template>
  <div class="hot">
    <div class="hot-title">
      <div class="hot-title-left">
        <div>热门课程</div>
        <div>HOT COURSES</div>
      </div>
      <RouterLink class="hot-title-right" to="/course">
        <div style="margin-right: 5px">更多课程</div>
        <el-icon><ArrowRight /></el-icon>
      </RouterLink>
    </div>
    <div class="hot-list">
      <div
        class="hot-list-item cursor-pointer"
        v-for="(item, index) in hotHandleList"
        :key="item.courseId"
        @click="e => handleCourseClick(item, e)"
      >
        <img :src="getAssetUrl(index + 1)" alt="" />
        <div class="learn-title"> {{ item.courseName }}</div>
        <div class="course-btn">
          <div style="margin-right: 5px">我要学习</div>
          <el-icon><ArrowRight /></el-icon>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped lang="less">
  .hot {
    border-radius: 15px;
    padding: 20px 20px 5px 20px;
    width: 38%;
    background-color: white;
    .hot-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
      .hot-title-left {
        border-left: 5px solid #6b7dfa;
        padding-left: 10px;
        :first-child {
          font-size: 22px;
        }
        :last-child {
          font-size: 8px;
          padding-bottom: 3px;
          border-bottom: 1px solid #6b7dfa;
        }
      }
      .hot-title-right {
        display: flex;
        align-items: center;
        cursor: pointer;
      }
    }
    .hot-list {
      display: flex;
      flex-direction: column;
      .hot-list-item {
        position: relative;
        margin-bottom: 25px;
        > img {
          width: 100%;
          height: 95px;
          border-radius: 10px;
        }

        .learn-title {
          color: #333;
          font-weight: bold;
          position: absolute;
          top: 15px;
          left: 20px;
          font-size: 18px;
        }
        .course-btn {
          font-size: 14px;
          color: white;
          position: absolute;
          top: 50px;
          left: 20px;
          display: flex;
          align-items: center;
          background-color: #f79148;
          padding: 5px;
          padding-left: 10px;
          border-radius: 5px;
        }
      }
    }
  }
</style>
