/*
 * @Description: 企业和产品相关API
 * @Author: Claude
 * @LastEditors: <PERSON>
 * @Date: 2025-02-25 11:50:01
 * @LastEditTime: 2025-02-26 10:16:40
 */
import request from "@/utils/request"

// 获取企业列表
export function getEnterpriseList(query = {}) {
  return request({
    url: "/system/enterprise/list",
    method: "get",
    params: {
      pageSize: 999,
      status: 1,
      ...query
    }
  })
}
// 查询企业信息详细
export function getEnterprise(id) {
  return request({
    url: "/system/enterprise/" + id,
    method: "get"
  })
}

// 新增企业信息
export function addEnterprise(data) {
  return request({
    url: "/system/enterprise",
    method: "post",
    data: data
  })
}

// 修改企业信息
export function updateEnterprise(data) {
  return request({
    url: "/system/enterprise",
    method: "put",
    data: data
  })
}

// 删除企业信息
export function delEnterprise(id) {
  return request({
    url: "/system/enterprise/" + id,
    method: "delete"
  })
}

// 获取产品列表
export function getProductList(enterpriseId) {
  return request({
    url: "/system/product/list",
    method: "get",
    params: {
      pageSize: 999,
      enterpriseId
    }
  })
}

// 查询产品信息详细
export function getProduct(id) {
  return request({
    url: "/system/product/" + id,
    method: "get"
  })
}

// 新增产品信息
export function addProduct(data) {
  return request({
    url: "/system/product",
    method: "post",
    data: data
  })
}

// 修改产品信息
export function updateProduct(data) {
  return request({
    url: "/system/product",
    method: "put",
    data: data
  })
}

// 删除产品信息
export function delProduct(id) {
  return request({
    url: "/system/product/" + id,
    method: "delete"
  })
}
