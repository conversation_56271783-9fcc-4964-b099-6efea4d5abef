<script setup lang="ts">
import type { LeftImgRightText } from "@/types";
import type { PropType } from "vue";
defineProps({
  dataSource: {
    type: Object as PropType<LeftImgRightText>,
    default: () => ({}),
  },
});

import { ref, reactive, watch } from "vue";
import type { NewsListInfo } from "@/types";

// 对象结构确定，可以用 reactive 定义响应式对象
const query = reactive({
  orderState: 0,
  page: 1,
  pageSize: 5,
});

const newsListInfo = ref<NewsListInfo>();
const loadData = async () => {};

// tabs切换时更新 orderState 参数
const changeTabs = (index: number) => {
  query.orderState = index;
};

watch(
  query,
  () => {
    loadData();
  },
  // 立刻执行
  { immediate: true }
);
</script>
<template>
  <!--左侧广告显示-->
  <div class="ad-info">
    <div class="spec">
      <div class="news-list">
        <div
          class="news-item"
          v-for="(item, index) in newsListInfo?.items"
          :key="item.id"
        >
          <div class="body">
            <div class="column products">
              <div class="media" v-if="index == 0">
                <div class="ad-image">
                  <img :src="dataSource.imgUrl" />
                </div>
                <!-- 信息区 -->
                <div class="spec">
                  <!-- 主要信息 -->
                  <p class="g-name">污水处理设备行业新闻（演示内容）</p>
                  <!-- <p class="g-desc">液体比重天平厂</p>
                  <p class="g-content">
                    <span>2013-9-9</span>
                    <span>查看详情 </span>
                  </p> -->
                </div>
              </div>
              <ul v-if="index != 0">
                <li>
                  <a class="image" href="javascript:;">
                    <img :src="item.image" alt="" />
                  </a>
                  <div class="info">
                    <p class="name ellipsis-2">
                      <RouterLink :to="'/news/detail?id=' + item.id">
                        {{ item.name }}</RouterLink
                      >
                    </p>
                    <p class="attr ellipsis-2">
                      {{ item.summary }}
                    </p>
                    <p class="attr-date ellipsis-2">2022-9-9</p>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="spec">
      <div class="news-list">
        <div
          class="news-item"
          v-for="(item, index) in newsListInfo?.items"
          :key="item.id"
        >
          <div class="body">
            <div class="column products">
              <div class="media" v-if="index == 0">
                <div class="ad-image">
                  <img :src="dataSource.imgUrl" />
                </div>
                <!-- 信息区 -->
                <div class="spec">
                  <!-- 主要信息 -->
                  <p class="g-name">污水处理设备行业新闻（演示内容）</p>
                  <!-- <p class="g-desc">液体比重天平厂</p>
                  <p class="g-content">
                    <span>2013-9-9</span>
                    <span>查看详情 </span>
                  </p> -->
                </div>
              </div>
              <ul v-if="index != 0">
                <li>
                  <a class="image" href="javascript:;">
                    <img :src="item.image" alt="" />
                  </a>
                  <div class="info">
                    <p class="name ellipsis-2">
                      <RouterLink :to="'/news/detail?id=' + item.id">
                        {{ item.name }}</RouterLink
                      >
                    </p>
                    <p class="attr ellipsis-2">
                      {{ item.summary }}
                    </p>
                    <p class="attr-date ellipsis-2">2022-9-9</p>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.ad-info {
  .head {
    height: 50px;
    line-height: 50px;
    background: #f5f5f5;
    padding: 0 20px;
    overflow: hidden;
    span {
      margin-right: 20px;
      &.down-time {
        margin-right: 0;
        float: right;
        i {
          vertical-align: middle;
          margin-right: 3px;
        }
        b {
          vertical-align: middle;
          font-weight: normal;
        }
      }
    }
    .del {
      margin-right: 0;
      float: right;
      color: #999;
    }
  }
  min-height: 600px;
  background: #fff;
  display: flex;
  .media {
    width: 580px;
    height: 500px;
    padding: 30px 50px;
    .ad-image {
      width: 480px;
      height: 400px;
    }
  }
}

// 信息区
.spec {
  .g-name {
    padding-top: 20px;
    font-size: 22px;
  }
  .g-desc {
    color: #999;
    line-height: 25px;
    margin-top: 10px;
  }
  .g-content {
    margin-top: 10px;
    display: flex;
    justify-content: space-between;
    span {
      &::before {
        font-size: 14px;
      }
      &:first-child {
        color: @redColor;
        margin-right: 10px;
        font-size: 16px;
      }
      &:last-child {
        text-align: right;
        color: #999;
        font-size: 16px;
      }
    }
  }
}
.news-list {
  background-color: #fff;
  padding-top: 30px;
}

.news-item {
  margin-bottom: 20px;
  border: 3px solid #f5f5f5;
  .body {
    display: flex;
    align-items: stretch;
    .column {
      border-left: 1px solid #f5f5f5;
      text-align: center;
      padding: 20px;
      > p {
        padding-top: 10px;
      }
      &:first-child {
        border-left: none;
      }
      &.products {
        flex: 1;
        padding: 0;
        align-self: center;
        ul {
          li {
            border-bottom: 1px solid #f5f5f5;
            padding: 10px;
            display: flex;
            &:last-child {
              border-bottom: none;
            }
            .image {
              width: 160px;
              height: 90px;
              border: 1px solid #f5f5f5;
            }
            .info {
              width: 500px;
              text-align: left;
              padding: 0 10px;
              line-height: 20px;
              p {
                margin-bottom: 5px;
                &.name {
                  height: 50px;
                  line-height: 25px;
                }
                &.attr {
                  color: #999;
                  font-size: 12px;
                  span {
                    margin-right: 5px;
                  }
                }
                &.attr-date {
                  color: #999;
                  font-size: 12px;
                  text-align: right;
                  span {
                    margin-right: 5px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
