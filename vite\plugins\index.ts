/*
 * @Description: vite plugin配置页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-02-16 14:11:33
 * @LastEditTime: 2024-12-02 15:06:37
 */
import type { Plugin } from "vite"
import vue from "@vitejs/plugin-vue"
import vueJsx from "@vitejs/plugin-vue-jsx"
import vueSetupExtend from "vite-plugin-vue-setup-extend"
import viteCompression from "vite-plugin-compression"
import autoImport from "unplugin-auto-import/vite"
import createAliOssUpload from "./ali-oss-upload"
import createSvgIcon from "./svg-icon"
import UnoCSS from "unocss/vite"

export default function createVitePlugins(viteEnv: any) {
  const vitePlugins: Plugin[] = [
    vue({
      // 响应性语法糖目前默认是关闭状态，需要你显式选择启用。此外，接下来的所有配置都需要 vue@^3.2.25
      reactivityTransform: true
    }) as Plugin
  ]
  vitePlugins.push(...UnoCSS())
  vitePlugins.push(
    autoImport({
      imports: ["vue", "vue-router", "pinia"],
      dts: "types/auto-imports.d.ts"
    })
  )
  vitePlugins.push(createSvgIcon(false))
  vitePlugins.push(vueJsx())
  vitePlugins.push(vueSetupExtend())
  vitePlugins.push(
    viteCompression({
      verbose: true, // 默认即可
      disable: false, //开启压缩(不禁用)，默认即可
      deleteOriginFile: false, //删除源文件
      threshold: 10240, //压缩前最小文件大小
      algorithm: "gzip", //压缩算法
      ext: ".gz" //文件类型
    })
  )
  viteEnv.VITE_APP_ENV === "production" && vitePlugins.push(createAliOssUpload())
  return vitePlugins
}
