<!--
 * @Description: 荣誉证书
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-07-13 11:18:41
 * @LastEditTime: 2024-12-24 09:40:08
-->
<template>
  <div>
    <el-tabs type="border-card">
      <el-tab-pane label="荣誉证书">
        <div class="card-con" v-if="templateData.length > 0">
          <el-card
            v-for="(item, index) in templateData"
            :key="item.certificateId"
            @click="handleDetail(item)"
          >
            <!-- :preview-src-list="srcList"
              :initial-index="index" -->
            <el-image style="width: 100%; height: 160px" :src="item.templateImg" fit="contain" />
            <div class="tem-desc">
              <p>{{ item.templateName }}</p>
              <p><span>获取时间：</span>{{ item.issueDate }}</p>
            </div>
          </el-card>
        </div>
        <el-empty v-else description="暂无数据" :image-size="200"></el-empty>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script lang="ts" setup>
  import { templateList } from "@/api/trainingTask/honor"

  const router = useRouter()
  const templateData: any = ref([])
  const srcList = ref()
  const loadData = async () => {
    const res = await templateList()
    if (res.code === 200) {
      templateData.value = res.rows
      srcList.value = res.rows.map(item => {
        return item.templateImg
      })
    }
  }
  const handleDetail = ({ certificateId, certType }) => {
    router.push({
      path: "/template",
      query: {
        certificateId,
        certType
      }
    })
  }

  onMounted(() => {
    loadData()
  })
</script>
<style scoped lang="less">
  .card-con {
    display: flex;

    flex-wrap: wrap;
    :deep(.el-card) {
      width: 230px;
      margin: 10px 8px;
    }
  }
  .el-image {
    cursor: pointer;
  }
  .tem-desc {
    line-height: 24px;
    font-size: 16px;
    text-indent: 2px;
    cursor: pointer;
    > p:nth-child(2) {
      font-size: 14px;
      color: #999;
    }
  }
  :deep(.el-card__body) {
    padding: 14px !important;
  }
</style>
