/*
 * @Description:
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-17 08:49:10
 * @LastEditTime: 2023-09-15 14:01:22
 */
import { defineStore } from "pinia"
import { courseRandomizer } from "@/api/course"
import { carousellist } from "@/api/develops/carouselImg"
import type { CourseListType } from "@/types"

// 定义 Store 时建议遵循命名规范 useXxxStore
const useHomeStore = defineStore({
  // 唯一标识
  id: "home",
  // 持久化插件 - 默认存所有模块数据
  // persist: true,
  // 持久化插件 - 进阶用法
  // persist: {
  //   // 修改存储时的键名称
  //   key: "company-home",
  //   // 按需存储分类数据
  //   paths: ["categoryList"],
  // },
  // 状态
  // 如果 TS 项目某些变量改名重构了
  // 需通过命令 yarn typecheck 主动调用TS检查，提前发现错误
  state: () => {
    return {
      // 轮播图数据
      bannerList: [],
      // 热门课程数据
      hotHandleList: [] as CourseListType,
      // 推荐课程数据
      recommendList: [] as CourseListType
    }
  },
  // 函数/方法
  actions: {
    // 获取轮播图数据
    async getBannerList() {
      let queryData = {
        pageNum: 1,
        pageSize: 10,
        carouselId: 2030
      }

      const { rows } = await carousellist(queryData)
      this.bannerList = rows
    },
    // 获取热门课程数据
    async getHotCourseList() {
      const { data } = await courseRandomizer()
      this.recommendList = data.slice(0, 4)
      this.hotHandleList = data.slice(-4)
    }
  }
})

// 默认导出
export default useHomeStore
