<!--
 * @Description: 证书详情
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-07-13 11:18:41
 * @LastEditTime: 2024-12-24 09:46:42
-->
<template>
  <div class="p-4">
    <!-- 面包屑导航 -->
    <el-breadcrumb :separator-icon="ArrowRight" class="mb-6">
      <el-breadcrumb-item to="/study/honor">我的荣誉</el-breadcrumb-item>
      <el-breadcrumb-item>证书详情</el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 证书展示区域 -->
    <div class="flex flex-col items-center">
      <!-- 操作按钮 -->
      <div class="w-full flex gap-3 mb-4">
        <el-button type="primary" @click="handleDownload">
          <el-icon class="mr-1"><Download /></el-icon> 下载
        </el-button>
        <el-button type="primary" @click="handleDetail">
          <el-icon class="mr-1"><Search /></el-icon> 查看证书详情
        </el-button>
      </div>

      <!-- 证书图片展示 -->
      <div v-if="certificateImage" class="relative w-[968px] bg-white rounded-lg shadow-md p-4">
        <img 
          :src="certificateImage" 
          alt="证书" 
          class="w-full h-auto object-contain"
          @error="handleImageError"
        />
        <!-- 加载状态 -->
        <div v-if="loading" class="absolute inset-0 flex items-center justify-center bg-gray-50/80">
          <el-icon class="text-3xl animate-spin text-primary"><Loading /></el-icon>
        </div>
        <!-- 错误状态 -->
        <div v-if="loadError" class="absolute inset-0 flex flex-col items-center justify-center bg-gray-50/80">
          <el-icon class="text-3xl text-red-500 mb-2"><Warning /></el-icon>
          <span class="text-gray-600">证书加载失败，请刷新重试</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ArrowRight, Download, Search, Loading, Warning } from "@element-plus/icons-vue"
  import { templateDetail, getCertPicture } from "@/api/trainingTask/honor"
  import useUserStore from "@/store/modules/user"

  const route = useRoute()
  const router = useRouter()
  const { certificateId, certType } = route.query
  const userStore = useUserStore()
  const { userInfo } = storeToRefs(userStore)

  // 状态变量
  const certificateImage = ref("")
  const loading = ref(true)
  const loadError = ref(false)
  const certParams = ref({
    courseId: "",
    examId: "",
    taskId: "",
    userId: ""
  })

  // 加载证书图片
  const loadCertificateImage = async () => {
    loading.value = true
    loadError.value = false
    try {
      // 1. 获取证书详情数据
      const { code, data } = await templateDetail({
        certTemplateId: certificateId,
        certType
      })
      
      if (code !== 200 || !data?.length) {
        throw new Error("获取证书详情失败")
      }

      const firstItem = data[0]
      certParams.value = {
        courseId: firstItem.courseId,
        examId: firstItem.examId,
        taskId: firstItem.taskId,
        userId: userInfo.value.userId.toString()
      }

      // 2. 准备获取证书图片的参数
      const requestData = {
        certDataList: data,
        studentUserId: userInfo.value.userId,
        taskId: firstItem.taskId
      }

      // 3. 获取完整证书图片
      const response = await getCertPicture(firstItem.templateImg, requestData)
      
      if (response.code === 200 && response.data) {
        // 确保base64数据包含正确的前缀
        certificateImage.value = response.data.startsWith("data:image/")
          ? response.data
          : `data:image/png;base64,${response.data}`
      } else {
        throw new Error("获取证书图片失败")
      }
    } catch (error) {
      console.error("加载证书失败:", error)
      loadError.value = true
    } finally {
      loading.value = false
    }
  }

  // 处理图片加载错误
  const handleImageError = () => {
    loadError.value = true
  }

  // 下载证书
  const handleDownload = () => {
    if (!certificateImage.value) return
    
    const link = document.createElement("a")
    link.download = "证书.png"
    link.href = certificateImage.value
    link.click()
  }

  // 查看证书详情
  const handleDetail = () => {
    router.push({
      path: "/certDetail",
      query: {
        ...certParams.value
      }
    })
  }

  // 页面加载时获取证书图片
  onMounted(() => {
    loadCertificateImage()
  })
</script>
