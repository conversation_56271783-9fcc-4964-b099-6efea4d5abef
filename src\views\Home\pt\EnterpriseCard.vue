<script setup lang="ts">
  defineProps({
    enterprise: {
      type: Object,
      required: true
    }
  })

  const emit = defineEmits(["view-details"])

  const viewDetails = () => {
    emit("view-details")
  }
</script>

<template>
  <div
    class="flex p-20px rounded-8px shadow-sm transition-all duration-300 cursor-pointer mb-16px hover:translate-y-[-5px] hover:shadow-md bg-gradient-to-br from-[#dceaff] to-[#fff]"
    @click="viewDetails"
  >
    <!-- <div class="enterprise-logo">
      <img :src="enterprise.logo || '/src/assets/images/default-enterprise.png'" alt="企业Logo" />
    </div> -->
    <div class="flex-1 flex flex-col">
      <h3 class="text-18px font-bold text-gray-800 m-0 mb-8px">{{ enterprise.name }}</h3>
      <div class="text-14px font-bold text-gray-800 m-0 mb-8px"
        >{{ enterprise.contactName }}&nbsp;&nbsp;&nbsp;&nbsp; {{ enterprise.phone }}</div
      >
      <p class="text-14px text-gray-600 m-0 mb-12px line-clamp-5 overflow-hidden">{{
        enterprise.description || "暂无描述"
      }}</p>
      <!-- <div class="enterprise-meta">
        <span class="enterprise-tag">{{ enterprise.category || '未分类' }}</span>
        <span class="enterprise-date">{{ enterprise.establishDate || '--' }}</span>
      </div> -->
    </div>
  </div>
</template>
