<!--
 * @Description: 电子签名弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-02-22 14:11:56
 * @LastEditTime: 2024-03-15 10:54:29
-->
<template>
  <el-dialog
    width="1200px"
    :show-close="false"
    v-model="visible"
    title="请手动签名"
    :append-to-body="true"
    :close-on-click-modal="false"
  >
    <BaseEsign @closeson="submitHandle" />
  </el-dialog>
</template>

<script setup lang="ts">
  import dayjs from "dayjs"
  import { fileUpload } from "@/api/system/upload"
  import { ElMessage } from "element-plus"
  const emit = defineEmits(["submitSign"])

  const visible = ref(false)

  const openDialog = () => {
    visible.value = true
  }

  const submitHandle = async pngUrl => {
    const fileBlob = btof(pngUrl, `sign-${dayjs().format("YYYY/MM/DD HH:mm:ss")}`)
    // 利用FormData传参
    const MultipartFile = new FormData()
    // file 是后端接受图片的字段
    MultipartFile.append("file", fileBlob)
    const res = await fileUpload(MultipartFile)
    if (res.code === 200) {
      emit("submitSign", res.data.url)
      visible.value = false
    } else {
      return ElMessage.error("上传失败")
    }
  }

  const btof = (data, fileName) => {
    const dataArr = data.split(",")
    const byteString = atob(dataArr[1])
    const options: any = {
      type: "image/jpeg",
      endings: "native"
    }
    const u8Arr = new Uint8Array(byteString.length)
    for (let i = 0; i < byteString.length; i++) {
      u8Arr[i] = byteString.charCodeAt(i)
    }
    return new File([u8Arr], fileName + ".jpg", options)
  }
  defineExpose({
    openDialog
  })
</script>

<style lang="less" scoped></style>
