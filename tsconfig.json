{"extends": "@vue/tsconfig/tsconfig.web.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue", "types/**/*.d.ts", "vite/**/*.ts", "src/types/global.d.ts"], "compilerOptions": {"module": "ES2022", "ignoreDeprecations": "5.0", "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "noImplicitAny": false, "allowJs": true, "lib": ["ES2015", "DOM"], "types": ["element-plus/global", "@unocss/runtime", "unocss/vite"], "typeRoots": ["./node_modules/@types", "./src/types"], "suppressImplicitAnyIndexErrors": true, "noPropertyAccessFromIndexSignature": false}, "references": [{"path": "./tsconfig.vite-config.json"}]}