<script setup lang="ts">
  import { ref, reactive, watch, onBeforeUnmount } from "vue"
  import { sendCode, handleSMSLogin } from "@/api/login"
  import useUserStore from "@/store/modules/user"
  import { ElMessage } from "element-plus"
  import { setToken } from "@/utils/auth"

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    }
  })

  const emit = defineEmits(["update:visible", "login-success"])

  const userStore = useUserStore()
  const form = reactive({
    mobile: "",
    code: "",
    readAndAgree: false
  })

  const showDialog = ref(props.visible)
  const countdown = ref(0)
  const timer = ref<any>(null)

  // 监听 visible 变化
  watch(
    () => props.visible,
    val => {
      showDialog.value = val
    }
  )

  // 监听 showDialog 变化
  watch(
    () => showDialog.value,
    val => {
      emit("update:visible", val)
    }
  )

  // 发送验证码
  const sendSmsCode = async () => {
    if (!form.mobile) {
      ElMessage.error("请输入手机号")
      return
    }
    // 验证手机号格式
    if (!/^1[3-9]\d{9}$/.test(form.mobile)) {
      ElMessage.error("请输入正确的手机号")
      return
    }

    try {
      await sendCode({ mobile: form.mobile, scene: 1 })
      ElMessage.success("验证码发送成功")
      countdown.value = 60
      timer.value = setInterval(() => {
        if (countdown.value > 0) {
          countdown.value--
        } else {
          if (timer.value) {
            clearInterval(timer.value)
            timer.value = null
          }
        }
      }, 1000)
    } catch (error) {
      console.error("发送验证码失败:", error)
    }
  }

  // 登录
  const handleLogion = async () => {
    if (!form.mobile) {
      ElMessage.error("请输入手机号")
      return
    }
    if (!form.code) {
      ElMessage.error("请输入验证码")
      return
    }
    if (!form.readAndAgree) {
      ElMessage.error("请阅读并同意《用户协议》和《隐私政策》")
      return
    }

    try {
      const res = await handleSMSLogin({
        mobile: form.mobile,
        code: form.code,
        scene: 1
      })

      userStore.token = res.data.access_token
      await setToken(res.data.access_token)
      await userStore.getInfo()

      ElMessage.success("登录成功")
      closeDialog()
      emit("login-success")
    } catch (error) {
      console.error("登录失败:", error)
    }
  }

  // 关闭弹窗
  const closeDialog = () => {
    showDialog.value = false
    form.mobile = ""
    form.code = ""
    form.readAndAgree = false
    if (timer.value) {
      clearInterval(timer.value)
      timer.value = null
    }
    countdown.value = 0
  }

  // 组件销毁时清除定时器
  onBeforeUnmount(() => {
    if (timer.value) {
      clearInterval(timer.value)
      timer.value = null
    }
  })
</script>

<template>
  <el-dialog
    v-model="showDialog"
    title="手机验证登录"
    width="400px"
    :show-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    @closed="closeDialog"
  >
    <div class="login-dialog">
      <el-form :model="form" label-position="top">
        <el-form-item>
          <el-input v-model="form.mobile" placeholder="手机号" clearable>
            <template #prepend>
              <div class="country-code">+86</div>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item>
          <el-input v-model="form.code" placeholder="短信验证码" clearable>
            <template #append>
              <el-button type="primary" :disabled="countdown > 0" @click="sendSmsCode" class="send-code-btn">
                {{ countdown > 0 ? `${countdown}s后重试` : "发送验证码" }}
              </el-button>
            </template>
          </el-input>
        </el-form-item>

        <el-form-item>
          <el-checkbox v-model="form.readAndAgree">
            我已阅读并同意
            <a href="#" class="link">《用户协议》</a>和
            <a href="#" class="link">《隐私政策》</a>
          </el-checkbox>
        </el-form-item>

        <el-button
          type="primary"
          class="login-btn"
          @click="handleLogion"
          :disabled="!form.readAndAgree"
        >
          登录/注册
        </el-button>
      </el-form>
    </div>
  </el-dialog>
</template>

<style lang="less" scoped>
:deep(.el-input-group__prepend){
  padding: 0 10px;
}
  .login-dialog {
    padding: 0 20px;

    .country-code {
      width: 40px;
      text-align: center;
    }

    .send-code-btn {
      width: 100px;
      border: 1px solid #dcdfe6;
      &:hover {
        border-color: #409eff;
        color: #409eff;
      }
      &:disabled {
        border-color: #ebeef5;
        color: #c0c4cc;
      }
    }

    .login-btn {
      width: 100%;
      margin-top: 10px;
      background-color: #409eff;
      border: 1px solid #409eff;
      border-radius: 4px;
      color: white;
      font-weight: 500;
      &:hover {
        background-color: #66b1ff;
        border-color: #66b1ff;
      }
      &:disabled {
        background-color: #a0cfff;
        border-color: #a0cfff;
      }
    }

    .link {
      color: #1890ff;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }

    :deep(.el-input__wrapper) {
      border-radius: 4px;
      box-shadow: 0 0 0 1px #dcdfe6 inset;

      &:hover {
        box-shadow: 0 0 0 1px #c0c4cc inset;
      }

      &.is-focus {
        box-shadow: 0 0 0 1px #409eff inset;
      }
    }

    :deep(.el-checkbox__inner) {
      border: 1px solid #dcdfe6;
    }
  }

  :deep(.el-dialog__header) {
    margin-right: 0;
    text-align: center;
    font-weight: bold;
    font-size: 18px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
  }

  :deep(.el-dialog__body) {
    padding-top: 25px;
  }
</style>
