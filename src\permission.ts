/*
 * @Description: permission
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-20 14:34:23
 * @LastEditTime: 2024-11-19 09:48:25
 */
import router from "./router"
import { getToken, setToken } from "@/utils/auth"
import useUserStore from "@/store/modules/user"
import { isRelogin } from "@/utils/request"
import { autoLogin } from "@/api/system/user"
import useTenantStore from "@/store/modules/tenant"
import { getSubdomain, pushToAdmin } from "@/utils/common"
import { productionEnvList } from "@/utils/constant"

const env = import.meta.env.VITE_APP_ENV
const whiteList = ["/pthtg-login"]
let isAlreadyAutoLogin = false

router.beforeEach(async (to, from, next) => {
  const tenantStore = useTenantStore()
  const userStore = useUserStore()
  const subDomain = getSubdomain(false)
  // 获取URL中带的query以及无query的URL
  let queryString, urlNoParams
  if (window.location.href.indexOf("?") > -1) {
    // 获取当前页面queryString以及无query的URL
    queryString = window.location.href.split("?")?.[1]
    queryString = queryString?.endsWith("#/")
      ? queryString?.substring(0, queryString.length - 2)
      : queryString
    urlNoParams = window.location.href.split("?")?.[0]
  }
  // 租户进入，调用获取租户信息接口
  if (subDomain && !tenantStore.tenantLogo) {
    await tenantStore.queryTenantInfo()
  }
  // 双控跳转至学习平台自动登录逻辑
  if (window.location.href.indexOf("appId") >= 0 && !isAlreadyAutoLogin) {
    // 获取query对象
    const parseQuery: any = Object.fromEntries(new URLSearchParams(queryString))
    // username=杨玉琪&tenantName=上海万巷制药有限公司&appId=6645cbae-27d5-11ec-805b-525400b8bd2d&domainName=xida
    // 获取参数值
    const res = await autoLogin(parseQuery)
    if (res.code === 200) {
      isAlreadyAutoLogin = true
      // 截取成功储存token，下面重定向时会在请求时再获取token添加到头部的
      localStorage.setItem("token", res.data.access_token)
      localStorage.setItem("domainName", res.data.domain_name)
      setToken(res.data.access_token)
      await userStore.getInfo()
      if (parseQuery?.courseId) {
        return next(`/course/detail?id=${parseQuery.courseId}`)
      }
      if (parseQuery?.baseId && parseQuery?.arrangeId) {
        return next(`/prepare?baseId=${parseQuery.baseId}&arrangeId=${parseQuery.arrangeId}`)
      }
    }

    return next()
  }
  // 管理端携带token跳转至用户端逻辑
  else if (window.location.href.indexOf("token") >= 0) {
    const urlParams = new URLSearchParams(queryString)
    // 获取参数值
    const token = urlParams.get("token")!
    localStorage.setItem("token", token)
    setToken(token)
    if (!productionEnvList.includes(env)) {
      const testTenant = urlParams.get("testTenant")!
      if (testTenant) {
        localStorage.setItem("testTenant", testTenant)
      }
    }
    window.location.href = urlNoParams

    // if (tenantStore.domainName === "hb" && to.path === "/prepare") {
    //   const baseId = urlParams.get("baseId")
    //   const arrangeId = urlParams.get("arrangeId")
    //   setTimeout(() => {
    //     window.location.href = urlNoParams + `?baseId=${baseId}&arrangeId=${arrangeId}`
    //   })
    // } else {
    //  window.location.href = urlNoParams
    // }
  }
  // 页面跳转/刷新 判断是否有当前登录人信息逻辑；没有则调用getInfo()
  else if (getToken()) {
    /* has token*/
    if (userStore.roles.length === 0) {
      isRelogin.show = true
      // 判断当前用户是否已拉取完user_info信息
      await userStore.getInfo()
      isRelogin.show = false
    }
    return next()
  }
  // 无token返回登录页逻辑
  else {
    if (whiteList.indexOf(to.path) !== -1) {
      console.log(1111111111111)
      next()
    } else {
      console.log(2222222222222)
      pushToAdmin("/login", false)
      return next()
    }
  }
})
