<template>
  <div class="box">
    <div class="base-oneimage">
      {{ dataSource.text }}
    </div>
  </div>
</template>

<script setup lang="ts">
import type { SingleRichText } from "@/types";
import type { PropType } from "vue";
defineProps({
  dataSource: {
    type: Object as PropType<SingleRichText>,
    default: () => ({}),
  },
});
</script>

<style scoped lang="less">
.base-oneimage {
  width: 100%;
  height: 100%;
}
</style>
