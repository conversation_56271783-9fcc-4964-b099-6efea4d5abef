<!--
 * @Description: 
 * @Author: <PERSON><PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-09-08 15:40:09
 * @LastEditTime: 2023-10-19 10:40:52
-->
<template>
  <div class="target-view">
    <div>
      <span class="title">任务状态统计</span>

      <el-date-picker
        style="width: 260px; float: right; margin-right: 10px; margin-top: 2px"
        v-model="dateRange"
        value-format="YYYY-MM-DD"
        type="daterange"
        range-separator="-"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="selectDate"
      ></el-date-picker>
    </div>
    <div class="view-con">
      <BaseEcharts :option="option" />
    </div>
  </div>
</template>

<script setup>
  import { dashboard } from "@/api/trainingTask"
  import useStore from "@/store"

  const option = ref(null)
  const { user } = useStore()
  const { roles } = storeToRefs(user)

  const isAdmin = () => {
    let hasAdminRole = false
    if (roles.value.length > 0) {
      roles.value.forEach(item => {
        if (item.includes("admin")) {
          hasAdminRole = true
        }
      })
    }
    return hasAdminRole
  }

  let date = new Date()
  let year = date.getFullYear()
  let month = date.getMonth() + 1 < 10 ? "0" + (date.getMonth() + 1) : date.getMonth() + 1
  let day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate()
  let startDate = year + "-" + month + "-" + "01"
  let endDate = year + "-" + month + "-" + day
  let dateRange = ref([startDate, endDate])

  let queryParams = {
    queryTimeFrom: dateRange.value[0],
    queryTimeTo: dateRange.value[1]
  }
  if (!isAdmin()) {
    queryParams.userId = user.userInfo.userId
  }
  const getTarget = () => {
    dashboard(queryParams).then(res => {
      setOption(res.data)
    })
  }

  const selectDate = () => {
    getTarget()
  }

  getTarget()
  const setOption = data => {
    let text = String(data.totalTask)
    let tx = "28.5%"

    if (text.length === 2) {
      tx = "27%"
    } else if (text.length === 3) {
      tx = "26%"
    }
    let color = ["#8388fa", "#ed9d32", "#50d6c0", "#d01f25"]
    option.value = {
      tooltip: {
        trigger: "item"
      },
      color,
      legend: {
        orient: "vertical",
        left: "55%",
        top: "20%",
        itemGap: 25,
        itemWidth: 10,
        icon: "circle",
        itemHeight: 10,
        textStyle: {
          fontSize: 14,
          fontWeight: 600
        },

        formatter: function (name) {
          if (data.totalTask === 0) {
            return name
          }
          if (name === "全部任务") {
            return (
              name +
              "   " +
              ((data.totalTask / data.totalTask) * 100).toFixed(0) +
              "%" +
              "    " +
              data.totalTask
            )
          } else if (name === "未开始") {
            return (
              name +
              "       " +
              ((data.notStartedTask / data.totalTask) * 100).toFixed(0) +
              "%" +
              "      " +
              data.notStartedTask
            )
          } else if (name === "进行中") {
            return (
              name +
              "       " +
              ((data.inProgressTask / data.totalTask) * 100).toFixed(0) +
              "%" +
              "      " +
              data.inProgressTask
            )
          } else if (name === "已超期") {
            return (
              name +
              "       " +
              ((data.expiredTask / data.totalTask) * 100).toFixed(0) +
              "%" +
              "      " +
              data.expiredTask
            )
          }
        },
        data: [
          {
            name: "全部任务",
            icon: "circle",
            textStyle: {
              color: color[0]
            }
          },
          {
            name: "未开始",
            icon: "circle",
            textStyle: {
              color: color[1]
            }
          },
          {
            name: "进行中",
            icon: "circle",
            textStyle: {
              color: color[2]
            }
          },
          {
            name: "已超期",
            icon: "circle",
            textStyle: {
              color: color[3]
            }
          }
        ]
      },
      title: {
        text,
        textStyle: {
          fontSize: 36,
          color: "#7f8af5",
          fontWeight: 400
        },

        x: tx,
        y: "40%"
      },

      series: [
        {
          name: "任务分布",
          type: "pie",
          radius: ["50%", "65%"],
          center: ["30%", "50%"],

          label: {
            show: false
          },
          itemStyle: {
            shadowColor: "rgba(0, 0, 0, 0.6)",
            shadowBlur: 12,
            shadowOffsetX: 2,
            shadowOffsetY: 2
          },
          data: [
            { value: data.totalTask, name: "全部任务" },
            { value: data.notStartedTask || undefined, name: "未开始" },
            { value: data.inProgressTask || undefined, name: "进行中" },
            { value: data.expiredTask || undefined, name: "已超期" }
          ],
          label: {
            show: true,
            formatter: "{b|{b}} {d|{d}%} {c|{c}}",
            color: "inherit",
            rich: {
              d: {
                fontSize: 14,
                lineHeight: 24,
                height: 24
              },
              b: {
                fontSize: 12,
                lineHeight: 20,
                align: "left"
              }
            },
            textStyle: {
              align: "center",
              fontSize: 14
            }
          }
        }
      ]
    }
  }
</script>

<style scoped lang="less">
  .title {
    line-height: 40px;
    font-size: 16px;
    font-weight: bold;
    text-indent: 10px;
    color: #ee9d32;
  }
  .view-con {
    background: #fff;
    padding: 14px 20px;
    border-radius: 10px;
    height: 298px;
  }
</style>
