/*
 * @Description:
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON><PERSON>
 * @Date: 2023-05-30 15:34:27
 * @LastEditTime: 2023-06-01 09:08:32
 */
export type questionType = {
  questionnaireId: number
  questionnaireCover: string
  surveyTitle: string
  surveyDescription: string
  createTime: string
  queryStatus: string | number
  questionnaireIssuedId: number
  queryStartTime: string
  queryEndTime: string
}

export type questionnaireType = {
  questionType: string
  userAnswer: string | Array
  traningQuestionId: number
  questionName: string
}
