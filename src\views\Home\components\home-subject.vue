<script lang="ts">
  export default {
    name: "HomeSubject"
  }
</script>
<script setup lang="ts">
  import HomePanel from "./home-panel.vue"
  import { ref } from "vue"
  const productadvListTwoInfo = ref([
    {
      id: 1,
      img: "https://meedu-cos.meedu.xyz/images/ffaNXVYx5mEsTGzACNcQGEd43hfvH8YptIdaf8tU.png",
      title: "1",
      subTitle: "1"
    },
    {
      id: 2,
      img: "https://meedu-cos.meedu.xyz/images/ffaNXVYx5mEsTGzACNcQGEd43hfvH8YptIdaf8tU.png",
      title: "2",
      subTitle: "2"
    },
    {
      id: 3,
      img: "https://meedu-cos.meedu.xyz/images/ffaNXVYx5mEsTGzACNcQGEd43hfvH8YptIdaf8tU.png",
      title: "3",
      subTitle: "3"
    },
    {
      id: 4,
      img: "https://meedu-cos.meedu.xyz/images/ffaNXVYx5mEsTGzACNcQGEd43hfvH8YptIdaf8tU.png",
      title: "4",
      subTitle: "4"
    }
  ])

  // 对象结构确定，可以用 reactive 定义响应式对象
  // const query = reactive({
  //   orderState: 0,
  //   page: 1,
  //   pageSize: 5,
  // });
  // const loadData = async () => {
  // };
  // watch(
  //   query,
  //   () => {
  //     loadData();
  //   },
  //   // 立刻执行
  //   { immediate: true }
  // );
</script>

<template>
  <HomePanel title="推荐专题" ref="target">
    <div class="enterprise-item">
      <div class="body">
        <div class="column products" v-for="item in productadvListTwoInfo" :key="item.id">
          <ul>
            <li>
              <a class="image" href="javascript:;">
                <img :src="item.img" alt="" />
              </a>
              <div class="info">
                <p class="name ellipsis-2">
                  <RouterLink :to="'/news/detail?id=' + item.id"> {{ item.title }}</RouterLink>
                </p>
                <p class="attr ellipsis">
                  {{ item.subTitle }}
                </p>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </HomePanel>
</template>

<style scoped lang="less">
  .enterprise-item {
    margin: 0 auto;
    position: relative;
    width: 1240px;
    padding-bottom: 20px;
    .head {
      height: 50px;
      line-height: 50px;
      background: #f5f5f5;
      padding: 0 20px;
      overflow: hidden;
      span {
        margin-right: 20px;
        &.down-time {
          margin-right: 0;
          float: right;
          i {
            vertical-align: middle;
            margin-right: 3px;
          }
          b {
            vertical-align: middle;
            font-weight: normal;
          }
        }
      }
      .del {
        margin-right: 0;
        float: right;
        color: #999;
      }
    }
    .body {
      display: flex;
      background: #fff;
      flex-wrap: wrap;
      align-items: stretch;
      .column {
        border-left: 1px solid #f5f5f5;
        text-align: center;
        padding: 20px;
        > p {
          padding-top: 10px;
        }
        &:first-child {
          border-left: none;
        }
        &.products {
          flex: 1;
          padding: 0;
          align-self: center;
          ul {
            li {
              border-bottom: 1px solid #f5f5f5;
              padding: 10px;
              display: flex;
              &:last-child {
                border-bottom: none;
              }
              .image {
                width: 320px;
                height: 180px;
                border: 1px solid #f5f5f5;
              }
              .info {
                width: 220px;
                text-align: left;
                padding: 0 10px;
                p {
                  margin-bottom: 5px;
                  &.name {
                    height: 68px;
                    font-weight: bold;
                    font-size: 18px;
                  }
                  &.attr {
                    color: #999;
                    font-size: 14px;
                    span {
                      margin-right: 5px;
                    }
                  }
                }
              }
              .enterprise {
                width: 100px;
              }
              .count {
                width: 80px;
              }
            }
          }
        }
        &.state {
          width: 120px;
          .green {
            color: @baseColor;
          }
        }
        &.amount {
          width: 200px;
          .red {
            color: @redColor;
          }
        }
        &.action {
          width: 140px;
          a {
            display: block;
            &:hover {
              color: @baseColor;
            }
          }
        }
      }
    }
  }
</style>
