<!--
 * @Description: detail-body
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-09-05 14:26:36
 * @LastEditTime: 2024-11-29 14:24:28
-->

<script lang="ts" setup>
  import { fetchCourseList } from "@/api/course"
  import { listDiscuss, addDiscuss } from "@/api/discuss"
  import useUserStore from "@/store/modules/user"
  import { ElMessage } from "element-plus"
  import type { classType, discussType } from "@/types"
  import { changeNumToHan, countSubstr } from "@/utils/common"

  const { proxy } = getCurrentInstance()!
  const { exam_question_type } = proxy!.useDict("exam_question_type")

  const props = defineProps({
    courseItem: {
      type: Object as PropType<classType>,
      default: () => ({})
    }
  })

  let route = useRoute()
  let discussList = ref<discussType[]>([])
  let pageNum = ref(1)
  let pageSize = ref(8)
  let dataTotal = ref<number | undefined>(0)
  let activeIndex = ref("0")
  let commentValue = ref("")
  let recommendCourseList = ref<classType[]>([])
  let userStore = useUserStore()

  let inputValue = ref()
  let fillingQS = ref([])
  let questionIndex = ref(0)
  let showAnswer = ref(false)
  let skipIndex = ref(1)
  const getAssetURL = url => {
    if (url) return url
    return new URL(`@/assets/images/avatar.png`, import.meta.url).href
  }
  // 获取评论
  async function fetchDiscussData() {
    let queryData = {
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      courseId: route.query.id
    }
    const { rows, total } = await listDiscuss(queryData)
    discussList.value = rows
    dataTotal.value = total
  }

  watch(
    () => skipIndex.value,
    () => {
      if (skipIndex.value < 1) skipIndex.value = 1
      else if (skipIndex.value > props.courseItem.courseQuestionLinkList.length - 1)
        skipIndex.value = props.courseItem.courseQuestionLinkList.length - 1
    }
  )

  // 发表评论
  const loading = ref(false)
  async function sendComment() {
    loading.value = true
    if (!commentValue.value.trim()) {
      ElMessage({
        message: "评论内容不能为空!",
        type: "warning",
        grouping: true
      })
      setTimeout(() => {
        loading.value = false
      }, 500)
      return
    }
    let queryData = {
      courseId: route.query.id,
      discussContent: commentValue.value,
      discussPortrait: userStore.avatar,
      courseName: props.courseItem?.courseName
    }
    let res = await addDiscuss(queryData)
    if (res.code == 200) {
      commentValue.value = ""
      await fetchDiscussData()
      ElMessage({
        message: "发表评论成功！",
        type: "success",
        grouping: true
      })
    }
    loading.value = false
  }

  // 获取推荐课程
  const getRecommendCourse = async () => {
    let queryData = {
      pageNum: 1,
      pageSize: 4,
      sortField: "a.update_time",
      sortOrder: "desc",
      courseStatus: "2"
    }
    const { rows } = await fetchCourseList(queryData)
    recommendCourseList.value = rows || []
  }

  onMounted(async () => {
    await fetchDiscussData()
    getRecommendCourse()
  })

  const changeQuestionIndex = type => {
    showAnswer.value = false
    inputValue.value = undefined
    fillingQS.value = []
    if (type === "previous") questionIndex.value--
    else if (type === "next") questionIndex.value++
    else questionIndex.value = type * 1
  }

  const showOrHide = () => {
    if (showAnswer.value) showAnswer.value = false
    else showAnswer.value = true
  }

  // 正确的选项
  function isCorrect(item, choice) {
    if (item.questionType === "S" || item.questionType === "J") {
      return item.answer === choice
    } else if (item.questionType === "M") {
      if (item.answer.includes(",")) {
        return item.answer.split(",").indexOf(choice) > -1
      }
      return item.answer === choice
    }
    return false
  }

  // 错误的选项
  function isWrong(item, choice) {
    if (!inputValue.value) return false
    if (item.questionType === "S" || item.questionType === "J") {
      return inputValue.value === choice && inputValue.value !== item.answer
    } else if (item.questionType === "M") {
      return inputValue.value.includes(choice) && !item.answer.includes(choice)
    }
    return false
  }
</script>
<template>
  <div class="detail-body">
    <div class="detail-body-left">
      <el-tabs v-model="activeIndex" class="demo-tabs detail-body-left-header">
        <el-tab-pane label="课程介绍" name="0">
          {{ courseItem?.courseIntroduction || "暂无" }}
        </el-tab-pane>
        <el-tab-pane label="课程大纲" name="1">{{ courseItem?.curriculum || "暂无" }}</el-tab-pane>
        <el-tab-pane label="课程目标" name="2">
          {{ courseItem?.courseObjectives || "暂无" }}
        </el-tab-pane>
        <el-tab-pane
          v-if="courseItem.courseQuestionLinkList && courseItem.courseQuestionLinkList.length > 0"
          :label="`试题库(${courseItem.courseQuestionLinkList?.length})`"
          name="3"
          class="question-pane"
        >
          <div class="question-type">
            {{ changeNumToHan(questionIndex + 1) }}.<dict-tag
              :options="exam_question_type"
              :value="courseItem.courseQuestionLinkList[questionIndex].questionType"
            />题
          </div>
          <div class="question-number">
            <div
              >第 {{ questionIndex + 1 }} 题 / 共
              {{ courseItem.courseQuestionLinkList?.length }} 题</div
            >
            <el-button @click="showOrHide">{{ showAnswer ? "隐藏答案" : "显示答案" }}</el-button>
          </div>
          <div class="question-name">{{
            courseItem.courseQuestionLinkList[questionIndex].questionName
          }}</div>
          <div class="question-option" v-if="!showAnswer">
            <div
              class="option-item"
              v-for="(item, index) in countSubstr(
                courseItem.courseQuestionLinkList[questionIndex].questionName,
                '（____）'
              )"
              v-if="courseItem.courseQuestionLinkList[questionIndex].questionType === 'F'"
            >
              <div>填空{{ item }}：</div>
              <el-input v-model="fillingQS[item - 1]" class="input"></el-input>
            </div>

            <el-radio-group
              v-model="inputValue"
              v-if="
                courseItem.courseQuestionLinkList[questionIndex].questionType === 'S' ||
                courseItem.courseQuestionLinkList[questionIndex].questionType === 'J'
              "
              class="group"
            >
              <template v-for="choice in 11">
                <el-radio
                  class="group-item"
                  v-if="courseItem.courseQuestionLinkList[questionIndex][`item${choice}`]"
                  :name="String.fromCharCode(64 + choice)"
                  :key="choice"
                  :label="`${String.fromCharCode(64 + choice)}`"
                  >{{
                    `${String.fromCharCode(64 + choice)}. &nbsp;${
                      courseItem.courseQuestionLinkList[questionIndex][`item${choice}`]
                    }`
                  }}</el-radio
                >
              </template>
            </el-radio-group>

            <el-checkbox-group
              v-model="inputValue"
              class="group"
              v-if="courseItem.courseQuestionLinkList[questionIndex].questionType === 'M'"
            >
              <template v-for="multiChoice in 11">
                <el-checkbox
                  class="group-item"
                  v-if="courseItem.courseQuestionLinkList[questionIndex][`item${multiChoice}`]"
                  :key="multiChoice"
                  :label="`${String.fromCharCode(64 + multiChoice)}`"
                  >{{
                    `${String.fromCharCode(64 + multiChoice)}. &nbsp;${
                      courseItem.courseQuestionLinkList[questionIndex][`item${multiChoice}`]
                    }`
                  }}</el-checkbox
                >
              </template>
            </el-checkbox-group>
          </div>

          <div class="question-choice" v-else>
            <div
              class="option-item"
              v-for="item in countSubstr(
                courseItem.courseQuestionLinkList[questionIndex].questionName,
                '（____）'
              )"
              v-if="courseItem.courseQuestionLinkList[questionIndex].questionType === 'F'"
            >
              <div>填空{{ item }}：</div>
              <el-input
                :value="
                  courseItem.courseQuestionLinkList[questionIndex].answer.split('::')[item - 1]
                "
                class="input"
                disabled
              ></el-input>
            </div>

            <template v-for="choice in 11">
              <div
                class="choice-item"
                v-if="courseItem.courseQuestionLinkList[questionIndex][`item${choice}`]"
              >
                <div class="index">
                  <span
                    v-if="
                      isCorrect(
                        courseItem.courseQuestionLinkList[questionIndex],
                        String.fromCharCode(64 + choice)
                      )
                    "
                  >
                    <img src="@/assets/images/correct.png" alt="" />
                  </span>
                  <span
                    v-else-if="
                      isWrong(
                        courseItem.courseQuestionLinkList[questionIndex],
                        String.fromCharCode(64 + choice)
                      )
                    "
                  >
                    <img src="@/assets/images/error.png" alt="" />
                  </span>
                  <span class="letterOnly" v-else>{{ String.fromCharCode(64 + choice) }}</span>
                </div>
                <div class="content">
                  {{ courseItem.courseQuestionLinkList[questionIndex][`item${choice}`] }}
                </div>
              </div>
            </template>
          </div>

          <div class="question-switch">
            <div>
              <el-button v-if="questionIndex !== 0" @click="changeQuestionIndex('previous')"
                >上一题</el-button
              >
            </div>

            <div class="skip">
              <div class="left">前往</div>
              <div class="right"
                ><span>第</span>
                <el-input
                  v-model="skipIndex"
                  class="right-input"
                  @keyup.enter="changeQuestionIndex(skipIndex - 1)"
                  @blur="changeQuestionIndex(skipIndex - 1)"
                ></el-input>
                <span>题</span></div
              >
            </div>

            <div>
              <el-button
                v-if="questionIndex !== courseItem.courseQuestionLinkList.length - 1"
                @click="changeQuestionIndex('next')"
                >下一题</el-button
              >
            </div>
          </div>
          <div class="question-answer">
            <div>答案：</div>
            <div>
              <div
                class="answer-item"
                v-if="showAnswer === true"
                v-for="(item, index) in courseItem.courseQuestionLinkList[
                  questionIndex
                ].answer.split('::')"
              >
                <div v-if="courseItem.courseQuestionLinkList[questionIndex].questionType === 'F'"
                  >填空{{ index + 1 }}
                </div>
                <div>{{ item }}</div>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane :label="`评论(${dataTotal})`" name="4">
          <div
            class="detail-body-left-header-comment"
            v-for="item in discussList"
            :key="item.traningCourseDiscussId"
            v-if="dataTotal !== 0"
          >
            <div class="detail-body-left-header-comment-img">
              <img :src="getAssetURL(item.discussPortrait)" alt="" />
            </div>
            <div class="detail-body-left-header-comment-info">
              <div>
                {{ item.discussBy }}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{ item.createTime }}
              </div>
              <div>{{ item.discussContent }}</div>
            </div>
          </div>
          <el-empty v-else description="暂无数据" :image-size="300"></el-empty>
          <BasePagination
            class="pagination"
            v-if="dataTotal !== 0"
            :total="dataTotal!"
            v-model:pageNum="pageNum"
            v-model:pageSize="pageSize"
            @pagination="fetchDiscussData"
          />
          <el-input
            v-model="commentValue"
            type="textarea"
            :rows="6"
            placeholder="评论时请遵纪守法并注意语言文明"
          ></el-input>
          <el-button class="buttonStyle" type="primary" @click="sendComment" :loading="loading"
            >提交</el-button
          >
        </el-tab-pane>
      </el-tabs>
    </div>
    <div class="detail-body-right">
      <div class="detail-body-right-title">推荐课程</div>
      <RouterLink
        class="detail-body-right-content"
        v-for="item in recommendCourseList"
        :key="item.courseId"
        :to="'/course/detail?id=' + item.courseId"
        target="_blank"
      >
        <img :src="item.courseImage" alt="" />
        <div class="detail-body-right-content-title">
          {{ item.courseName }}
        </div>
      </RouterLink>
    </div>
  </div>
</template>

<style lang="less" scoped>
  .detail-body {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    padding-bottom: 30px;

    .detail-body-left {
      background-color: #fff;
      width: 75%;
      min-height: 500px;

      .detail-body-left-header {
        padding: 10px 20px;

        .question-type {
          display: flex;
          font-size: 18px;
          font-weight: bold;
          margin-bottom: 30px;
        }

        .question-number {
          font-size: 14px;
          font-weight: bold;
          margin-bottom: 30px;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }

        .question-name {
          font-size: 14px;
          margin-bottom: 30px;
        }

        .question-option {
          margin-bottom: 30px;
          .option-item {
            display: flex;
            align-items: center;

            > div {
              margin-right: 10px;
              margin-bottom: 30px;
            }
            .input {
              width: 300px;
            }
          }

          .group {
            display: flex;
            flex-direction: column;
            align-items: start;
            .group-item {
              margin-bottom: 20px;
            }
          }
        }

        .question-choice {
          margin-bottom: 30px;
          .option-item {
            display: flex;
            align-items: center;

            > div {
              margin-right: 10px;
              margin-bottom: 30px;
            }
            .input {
              width: 300px;
            }
          }
          .choice-item {
            display: flex;
            align-items: center;
            margin-bottom: 30px;

            .index {
              width: 24px;
              height: 24px;

              .letterOnly {
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 50% 50%;
                width: 22px;
                height: 22px;
                border: 1px solid #ccc;
              }
            }

            .content {
              padding-left: 10px;
              // font-size: 16px;
            }
          }
        }

        .question-switch {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 30px;
          > div {
            text-align: center;
            width: 25%;
          }
          .skip {
            display: flex;
            align-items: center;
            justify-content: center;
            .left {
              margin-right: 10px;
            }
            .right {
              display: flex;
              align-items: center;
              .right-input {
                width: 70px;
                margin: 0 10px;
              }
            }
          }
        }

        .question-answer {
          display: flex;
          font-size: 18px;

          > :first-child {
            margin-right: 20px;
          }
          .answer-item {
            color: #000;
            margin-bottom: 30px;
            display: flex;
            > div {
              margin-right: 20px;
              display: flex;
              color: #2d79ae;
            }
          }
        }
      }

      .question-pane {
        padding: 20px 40px;
      }
      .detail-body-left-header-comment {
        display: flex;
        border-bottom: 1px dashed #dcdfe6;
        .detail-body-left-header-comment-img {
          margin-top: 25px;
          margin-bottom: 20px;
          width: 5%;
          > img {
            width: 60px;
            border-radius: 50%;
          }
        }
        .detail-body-left-header-comment-info {
          margin: 20px;
          width: 95%;
          > div {
            margin-bottom: 30rpx;
            overflow: hidden;
            word-wrap: break-word;
            word-break: break-all;
            white-space: pre-wrap;
            margin-bottom: 10px;
            line-height: 30px;
          }
        }
      }

      .buttonStyle {
        margin-top: 30px;
        float: right;
        margin-bottom: 30px;
      }
    }

    .detail-body-right {
      background-color: #fff;
      width: 23%;
      display: flex;
      flex-direction: column;
      padding: 20px;
      // align-items: center;
      height: 850px;
      .detail-body-right-title {
        margin-bottom: 20px;
        font-size: 18px;
        font-weight: bold;
        border-left: 5px solid @warmOrange;
        color: @warmOrange;
        padding-left: 10px;
      }

      .detail-body-right-content {
        border: 1px solid #d7d7d7;
        margin-bottom: 30px;

        > img {
          height: 127px;
          width: 100%;
        }

        .detail-body-right-content-title {
          margin: 10px;
          text-overflow: -o-ellipsis-lastline;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          line-clamp: 1;
          -webkit-box-orient: vertical;
          word-wrap: break-word;
        }

        .detail-body-right-content-views {
          display: flex;
          align-items: center;
          justify-content: end;
          margin-right: 15px;
        }
        .icon-caiyouduo_liulanliang {
          margin-right: 5px;
        }
      }
    }
  }
</style>
