<!--
 * @Description: 排名弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-09-18 09:04:49
 * @LastEditTime: 2023-10-11 17:22:00
-->

<script setup lang="ts" name="disasterRankingDialog">
  import useUserStore from "@/store/modules/user"
  import { getDisasterRanking } from "@/api/home"

  const dialogFormVisible = ref(false)
  const { userInfo } = storeToRefs(useUserStore())

  const openDialog = () => {
    getRankingList()
    getSelfRanking()
    dialogFormVisible.value = true
    stopSrcoll()
  }

  const rankingList = ref([])
  // 获取排行榜列表
  const getRankingList = async () => {
    let requestData = {
      pageSize: 20
    }
    const res = await getDisasterRanking(requestData)
    rankingList.value = res.rows || []
  }

  const stopSrcoll = () => {
    const mo = function (e) {
      e.preventDefault()
    }
    document.body.style.overflow = "hidden"
    document.addEventListener("touchmove", mo, false) //禁止页面滑动
  }

  const openSrcoll = () => {
    dialogFormVisible.value = false
    const mo = function (e) {
      e.preventDefault()
    }
    document.body.style.overflow = "" //出现滚动条
    document.removeEventListener("touchmove", mo, false)
  }

  // 个人排名所在行样式特殊处理
  const selfRowStyle = ({ row, rowIndex }) => {
    if (row.userId === userInfo.value.userId) {
      return {
        color: "#d9001b",
        fontWeight: "bold",
        fontSize: "16px"
      }
    }
  }

  const selfRankingInfo = ref()
  const getSelfRanking = async () => {
    let requestData = {
      userId: userInfo.value.userId
    }
    const res = await getDisasterRanking(requestData)
    selfRankingInfo.value = res.rows?.[0] || []
  }

  const getAssetURL = () => {
    return new URL(`@/assets/images/disaster-bg.jpg`, import.meta.url).href
  }

  defineExpose({
    openDialog
  })
</script>

<template>
  <el-dialog
    top="5vh"
    v-model="dialogFormVisible"
    :close-on-click-modal="false"
    :show-close="false"
    width="80%"
    :key="new Date().getTime()"
    :style="`
      overflow: hidden;
      background-image: url(${getAssetURL()}) !important;
      background-size: 100% 100% !important;
      background-repeat: no-repeat !important;
      background-size: cover !important;
    `"
    @open="stopSrcoll"
    @close="openSrcoll"
  >
    <!-- <Teleport to="body">
      <div class="notice-banner">
        <div class="notice-content">
          <span class="bold-text">
            恭喜您！防灾减灾知识达人评比活动排名第
            {{ selfRankingInfo?.ranking }} 名，获得线上防灾减灾知识达人的称号。
          </span>
          请于2023年10月13日14:00前，在古北黄金城道活动现场的综合服务区领取奖品。
        </div>
      </div>
    </Teleport> -->
    <div class="dialog-container">
      <div class="dialog-header"> <img src="@/assets/images/disaster.png" alt="" /></div>
      <!-- <div class="tips">
        <span class="rule-title">活动说明：</span
        >活动期间综合分数满分100分，前20名评为“线上防灾减灾知识达人”，20-100名为“线上防灾减灾学习平台”优秀学员。每看完一个视频，可得1积分，最多可积50分。考试题目共计25题，总分50分，满分100分。分数一样者，现获得分数的成员优先。
        <div class="deadline">活动截止时间：2023年10月11日 12:00</div>
      </div> -->
      <div class="deadline">活动截止时间：2023年10月11日 12:00</div>
      <div class="ranking">
        <div class="ranking-title">
          <span class="line"></span>
          <span class="text">2023年防灾减灾知识达人排名</span>
          <span class="line"></span>
        </div>
        <div class="ranking-self"> 您的成绩：第 {{ selfRankingInfo?.ranking }} 名 </div>
        <div class="ranking-table">
          <el-table
            :row-style="selfRowStyle"
            size="large"
            stripe
            border
            :data="rankingList"
            style="width: 100%; height: 550px"
            :header-cell-style="{
              backgroundColor: '#f58335',
              color: 'white',
              fontSize: '18px'
            }"
          >
            <el-table-column label="排名" width="150" align="center">
              <template #default="scope">
                <img
                  class="rank-img-1"
                  v-if="scope.row.ranking === 1"
                  src="@/assets/images/one.png"
                />
                <img
                  class="rank-img-2"
                  v-else-if="scope.row.ranking === 2"
                  src="@/assets/images/two.png"
                />
                <img
                  class="rank-img-3"
                  v-else-if="scope.row.ranking === 3"
                  src="@/assets/images/three.png"
                />
                <span v-else>{{ scope.row.ranking }}</span>
              </template>
            </el-table-column>
            <el-table-column label="用户名" prop="mobile" align="center" />
            <el-table-column label="分数" prop="score" align="center" />
            <el-table-column label="最后登录时间" prop="loginTime" align="center" />
          </el-table>
        </div>
      </div>

      <div class="bottom-text">
        <span>感谢您的关注</span>
        <span>本次活动已结束，敬请期待下次活动。</span>
      </div>
    </div>
  </el-dialog>
</template>

<style scoped lang="less">
  .notice-banner {
    position: absolute;
    top: 0;
    z-index: 9999;
    height: 70px;
    width: 100%;
    background-color: #d9001b;
    color: #ffff00;
    display: flex;
    justify-content: center;
    align-items: center;

    .bold-text {
      font-size: 20px;
      font-weight: bold;
    }
  }
  .rank-img-1 {
    width: 35px;
    height: 35px;
  }

  .rank-img-2 {
    width: 30px;
    height: 30px;
  }

  .rank-img-3 {
    width: 25px;
    height: 25px;
  }
  .dialog-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    width: 100%;
  }

  .circle {
    position: absolute;
    width: 200px;
    height: 200px;
    background-color: #fcddb4;
  }

  .circle-1 {
    top: -30px;
    left: -30px;
    border-radius: 30% 70% 61% 39% / 0% 35% 65% 100%;
  }

  .circle-2 {
    top: 40%;
    right: -100px;
    border-radius: 50%;
  }

  .circle-3 {
    top: 70%;
    left: -100px;
    border-radius: 50%;
  }
  .dialog-header {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .deadline {
    color: #333333;
    font-weight: bold;
    font-size: 18px;
    margin-top: 20px;
  }
  .tips {
    margin: 10px 0;
    background-color: #fff;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
    padding: 20px 65px;
    width: 70%;
    font-size: 15px;
    line-height: 25px;
    .rule-title {
      font-size: 18px;
      color: #d9001b;
      font-weight: bold;
    }
  }

  .ranking {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 70%;
    overflow: hidden;
    margin-top: 20px;
    .ranking-title {
      display: flex;
      align-items: center;
      color: @warmOrange;
      width: 100%;
      .line {
        margin: 0 60px;
        width: 500px;
        flex: 1;
        display: inline-block;
        border-top: 1px dashed @warmOrange;
        vertical-align: 5px;
      }
      .text {
        font-size: 20px;
        font-weight: bold;
      }
    }

    .ranking-self {
      margin: 10px 0 20px 0;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #fff;
      width: 35%;
      font-size: 20px;
      font-weight: bold;
      border-radius: 10px;
      height: 50px;
      background: linear-gradient(to bottom, #03a7f0, #bf80ff);
    }

    .ranking-table {
      width: 90%;
    }
  }

  .bottom-text {
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    font-size: 24px;
    font-weight: bold;
    align-items: center;
    color: @warmOrange;
    span {
      height: 50px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
</style>
