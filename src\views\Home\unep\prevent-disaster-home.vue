<!--
 * @Description: 防灾减灾知识达人评比活动首页
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-09-15 15:36:21
 * @LastEditTime: 2024-11-06 14:42:13
-->

<script lang="ts" setup>
  import { getXdExamInfo } from "@/api/onlineExam"
  import { isInTimeRange } from "@/utils/common"
  import disasterRankingDialog from "./disaster-ranking-dialog.vue"
  import useTenantStore from "@/store/modules/tenant"
  import dayjs from "dayjs"

  const baseId = ref(2057)
  const arrangeId = ref(2177)
  const { domainName, startTime, endTime } = storeToRefs(useTenantStore())
  const router = useRouter()
  const startLearn = () => {
    router.push("/disasterCourse")
  }
  const startExam = async () => {
    if (domainName.value === "eduxd") {
      await eduxdGetArrangeId()
    }
    if (domainName.value === "jinwaitan" || domainName.value === "nf") {
      router.push("/jinwaitan-exam")
    } else {
      router.push({
        path: "/prepare",
        query: {
          baseId: baseId.value,
          arrangeId: arrangeId.value
        }
      })
    }
  }
  const isShowDialog = computed(() => {
    if (!endTime.value) return false
    return isInTimeRange(endTime.value, "2023.10.13 12:00")
  })

  const disasterRankingDialogRef = ref()

  const isShowExam = computed(() => {
    if (!endTime.value) return true
    return new Date() <= new Date(endTime.value)
  })

  const getAssetURL = () => {
    if (domainName.value === "unep") {
      return "https://training-voc.obs.cn-north-4.myhuaweicloud.com/unep/header-logo.png"
    } else {
      return new URL(`@/assets/images/yingji_home.png`, import.meta.url).href
    }
  }

  // 西电每日考试获取arrangeId
  const eduxdGetArrangeId = async () => {
    const { data } = await getXdExamInfo({ examId: baseId.value })
    arrangeId.value = data?.[0]?.arrangeId || "2177"
  }

  onMounted(() => {
    // if (isShowDialog.value) {
    //   disasterRankingDialogRef.value?.openDialog()
    // }
  })
</script>
<template>
  <div class="prevent-disaster-home">
    <h2 class="title">
      <span class="eduxd-title" v-if="domainName === 'eduxd'"
        >中国西电集团、中国西电<br />重大事故隐患判定标准学习竞赛</span
      >
      <span class="eduxd-title" v-else-if="domainName === 'nf'">安全生产月知识竞赛</span>
      <img v-else :src="getAssetURL()" alt="" />
    </h2>
    <div class="disaster-content">
      <div class="activity-rule">
        <span class="rule-title">{{ domainName === "nf" ? "活动内容" : "活动介绍" }}：</span>
        {{
          domainName === "unep"
            ? "2024年11月9日是第33个全国消防日，本月是“全国消防宣传月”为大力提升全民消防安全素质，普及消防安全知识，五角场街道开展2024年“全国消防安全宣传月”系列活动，营造浓厚的消防安全宣传氛围，特别推出“五角场街道线上消防知识达人竞赛”活动。"
            : domainName === "yingji"
            ? "今年 5 月 11 日至 17 日是我国第 16 个防灾减灾宣传周，主题是“人人讲安全、个个会应急---着力提升基层防灾避险能力”。为深入贯彻落实习近平总书记关于应急管理、防灾减灾救灾工作重要论述和党的二十大精神，进一步提升各级领导干部统筹发展和安全能力，增强全民灾害风险防范意识和能力，提高防灾减灾救灾处置保障能力和水平，应急管理部推出“防灾减灾知识学习竞赛”活动。"
            : domainName === "eduxd"
            ? "为深入贯彻落实习近平总书记关于安全生产系列重要指示精神，	严格落实国务院安委办《关于学好用好重大事故隐患判定标准的通知》要求，切实提高广大员工学习应用重大事故隐患判定标准的主动性、自觉性，健全企业重大事故隐患排查治理长效机制，坚决防范遏制各类事故，公司安委办组织开展重大事故隐患判定标准学习竞赛。"
            : domainName === "nf"
            ? "今年6月是第23个全国“安全生产月”，主题是“人人讲安全、个个会应急——畅通生命通道”，6月16日为全国“安全宣传咨询日”。为深入学习贯彻习近平关于应急管理的重要论述为重点，开展专题研讨、集中宣讲、辅导报告，全面领会习近平总书记关于安全生产重要论述的精髓要义，把理论学习成果转化为谋划推动工作的创新思路、务实举措、有效方法。聚焦“畅通生命通道”这一主要内容，组织开展宣传和演练。推出“安全生产月知识竞赛”活动。"
            : "今年 5 月 11 日至 17 日是我国第 16 个防灾减灾宣传周，主题是“人人讲安全、个个会应急---着力提升基层防灾避险能力”。为深入贯彻落实习近平总书记关于应急管理、防灾减灾救灾工作重要论述和党的二十大精神，进一步提升各级领导干部统筹发展和安全能力，增强全民灾害风险防范意识和能力，提高防灾减灾救灾处置保障能力和水平，推出“防灾减灾知识学习竞赛”活动。"
        }}
      </div>
      <div class="point-rule" v-if="domainName !== 'jinwaitan' && domainName !== 'nf'">
        <span class="rule-title">活动规则：</span>
        {{
          domainName === "eduxd"
            ? "活动由视频学习和知识测试两部分组成，综合总分100分。视频学习：每学习观看完1个视频，可积1分，最多可积25分，未完成一部视频学习扣1分；知识测试：每日完成10道测试题目，最终按照每日平均分×0.75。"
            : "活动由视频学习和知识测试两部分组成，综合总分100分。视频学习:每学习观看完1个视频，可积3分，最多可积45分;知识测试:随机抽取30道题目，总分55分，限时30分钟，活动期间可随时参与测试,测试机会为3次，以最优成绩作为最终记录，测试分数相同时，测试用时较少者优先。两部分分数相加，前100名获得本次活动“消防知识达人”称号,并获得“达人礼包”1份。"
        }}
        <template v-if="domainName === 'eduxd'">
          <br />最终成绩=视频学习得分+知识测试得分
        </template>
      </div>
      <div class="dead-line">
        活动时间：{{ dayjs(startTime).format("YYYY年MM月DD日 HH:mm") }} -
        {{ dayjs(endTime).format("YYYY年MM月DD日 HH:mm") }}
      </div>
      <!-- <div class="tips">
        <span style="color: #d9001b">2023年10月13日08:00</span>
        平台公布最后排名成绩，凭借注册信息凭证，于2023年10月13日14:00前，在古北黄金城道活动现场的综合服务区确认相关信息。信息确认无误后，签字合影后领取礼包（超时无法领取）
      </div> -->
    </div>

    <div class="bottom-btn" v-if="isShowExam">
      <div class="start-learn" @click="startLearn">开始学习</div>
      <div class="start-exam" @click="startExam">开始测试</div>
    </div>
    <div class="bottom-text" v-else>
      <span>活动已结束</span>
      <span>敬请期待下次活动</span>
    </div>
    <disaster-ranking-dialog ref="disasterRankingDialogRef" />
  </div>
</template>

<style scoped lang="less">
  .prevent-disaster-home {
    padding: 30px 0 60px 0;
    width: 1240px;
    margin: 0 auto;
  }
  .title {
    color: #f59a23;
    font-size: 50px;
    font-weight: bolder;
    line-height: 1;
    margin-bottom: 32px;
    text-align: center;
    text-shadow: 4px 4px 4px #333333;
  }

  .eduxd-title {
    line-height: 60px;
    color: #0f5f84;
    text-shadow: none;
    font-size: 46px;
  }

  .disaster-content {
    width: 1240px;
    background-color: #fff;
    padding: 50px 80px;
    line-height: 30px;
    font-size: 15px;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);

    .rule-title {
      font-size: 18px;
      color: #d9001b;
      font-weight: bold;
    }

    .dead-line {
      margin-top: 30px;
      font-weight: bold;
      font-size: 15px;
    }

    .tips {
      font-size: 15px;
      font-weight: bold;
    }
  }

  .bottom-btn {
    margin-top: 46px;
    display: flex;
    font-size: 24px;
    font-weight: bold;
    justify-content: space-around;
    align-items: center;
    div {
      height: 90px;
      border-radius: 20px;
      width: 320px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      color: #fff;
      box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
    }

    .start-learn {
      background-color: rgb(217, 39, 0);
    }

    .start-exam {
      background-color: rgb(0, 137, 147);
    }
  }

  .bottom-text {
    margin: 100px 0 50px 0;
    display: flex;
    flex-direction: column;
    font-size: 24px;
    font-weight: bold;
    align-items: center;
    color: @warmOrange;
    span {
      height: 50px;
      width: 350px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
</style>
