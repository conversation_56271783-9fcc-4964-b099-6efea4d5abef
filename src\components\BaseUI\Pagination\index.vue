<template>
  <div :class="{ hidden: hidden }" class="pagination-container">
    <el-pagination
      class="centerPagination"
      :background="background"
      v-model:current-page="currentPage"
      v-model:page-size="pageSize2"
      :layout="layout"
      :page-sizes="pageSizes"
      :pager-count="pagerCount"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup lang="ts" name="Pagination">
  import { computed } from "vue"

  const props = defineProps({
    total: {
      required: true,
      type: Number
    },
    pageNum: {
      type: Number,
      default: 1
    },
    pageSize: {
      type: Number,
      default: 20
    },
    pageSizes: {
      type: Array,
      default() {
        return [10, 20, 30, 50]
      }
    },
    // 移动端页码按钮的数量端默认值5
    pagerCount: {
      type: Number,
      default: document.body.clientWidth < 992 ? 5 : 7
    },
    layout: {
      type: String,
      default: "prev, pager, next"
    },
    background: {
      type: <PERSON>olean,
      default: true
    },
    hidden: {
      type: Boolean,
      default: false
    }
  })

  interface Emit {
    (e: "update:pageNum", val: number): void
    (e: "update:pageSize", val: number): void
    (e: "pagination", val: any): void
  }
  const emit = defineEmits<Emit>()
  const currentPage = computed({
    get() {
      return props.pageNum
    },
    set(val) {
      emit("update:pageNum", val)
    }
  })
  const pageSize2 = computed({
    get() {
      return props.pageSize
    },
    set(val) {
      emit("update:pageSize", val)
    }
  })
  function handleSizeChange(val) {
    if (currentPage.value * val > props.total) {
      currentPage.value = 1
    }
    emit("pagination", { pageNum: currentPage.value, pageSize: val })
  }
  function handleCurrentChange(val) {
    emit("pagination", { pageNum: val, pageSize: pageSize2.value })
  }
</script>

<style lang="less" scoped>
  .pagination-container {
    padding: 32px 16px;
  }
  .centerPagination {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
  }
  .pagination-container.hidden {
    display: none;
  }

  :deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
    color: white;
  }
</style>
