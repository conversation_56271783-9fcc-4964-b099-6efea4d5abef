<!--
 * @Description: 安全生产课程
 * @Author: <PERSON>
 * @LastEditors: <PERSON><PERSON>
 * @Date: 2023-09-19 15:15:59
 * @LastEditTime: 2023-11-20 13:18:17
-->

<script lang="ts" setup>
  import type { classType } from "@/types"
  import { fetchCourseList } from "@/api/course"
  let route = useRoute()

  let videoList = ref<classType[]>([])

  let queryParams = ref({
    pageNum: 1,
    pageSize: 20,
    courseName: ""
  })
  let dataTotal = ref<number>(0)

  async function fetchData() {
    let queryData = {
      catalogueId: route.query.type,
      fromType: "learning",
      courseStatus: "2",
      ...queryParams.value
    }
    const { rows, total } = await fetchCourseList(queryData)
    dataTotal.value = total || 0
    videoList.value = rows
  }

  fetchData()
</script>

<template>
  <div class="course-container">
    <div class="container">
      <div class="home-panel">
        <div class="container">
          <div class="head">
            <div class="title"> <i class="iconfont icon-jiaoxuefansi"></i>全部课程</div>
          </div>
          <div class="course-list" v-if="videoList.length > 0">
            <div class="course-item" v-for="item in videoList" :key="item.courseId">
              <RouterLink :to="'/course/detail?id=' + item.courseId" target="_blank">
                <div class="course-img">
                  <img :src="item.courseImage" alt="" />
                </div>
                <div class="foot">
                  <div class="meta ellipsis">{{ item.courseName }} </div>
                  <div class="info">
                    <div class="info-left">
                      <div class="lecturer">
                        <i class="iconfont icon-yonghu"></i>
                        {{ item.lecturer }}
                      </div>
                    </div>
                    <div class="info-right">
                      <el-button
                        type="primary"
                        class="study_immediate"
                        v-if="item.learnStatus !== '2'"
                      >
                        立即学习
                      </el-button>
                      <el-button type="primary" class="already_completion" v-else>
                        已学习
                      </el-button>
                    </div>
                  </div>
                </div>
              </RouterLink>
            </div>
          </div>
          <el-empty
            class="empty-container"
            v-else
            description="暂无数据"
            :image-size="200"
          ></el-empty>
        </div>
      </div>
    </div>
  </div>
  <BasePagination
    class="pagination"
    v-if="videoList.length > 0"
    :total="dataTotal"
    v-model:pageNum="queryParams.pageNum"
    v-model:pageSize="queryParams.pageSize"
    @pagination="fetchData"
  />
</template>

<style scoped lang="less">
  .home-panel {
    /*  margin-top: 30px; */
    padding: 30px 0 60px 0;

    .head {
      padding-bottom: 20px;
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      .title {
        font-size: 20px;
        font-weight: 550;

        line-height: 34px;

        .iconfont {
          color: @warmOrange;
          font-size: 24px;
          margin-right: 12px;
          vertical-align: middle;
        }
      }
    }
  }
  .empty-container {
    padding-bottom: 200px;
  }
  .course-container {
    .right-head {
      background-color: #fff;
      width: 100%;
      margin: 0 auto;
      position: relative;
      border-bottom: 1px solid #dddddd;
      .filter {
        width: 1240px;
      }
    }
  }

  :deep(.el-divider--horizontal) {
    margin: 0;
  }

  :deep(.el-input__wrapper) {
    border-radius: 30px;
  }
  .active {
    background-color: @warmOrange;
    color: white;
    font-weight: bold;
    border-radius: 5px;
  }

  .pagination {
    background-color: #f4fafe;
    padding-bottom: 100px;
  }

  .course-sort {
    margin-bottom: 20px;
  }

  .disaster-tips {
    margin: 0 0 20px 30px;
    font-size: 15px;
  }

  .course-list {
    padding: 0;
    display: grid;
    gap: 20px;
    grid-template-columns: repeat(4, 1fr);
    .course-item {
      box-shadow: 0 2px 10px 0 rgba(103, 111, 144, 0.15);
      box-sizing: border-box;
      border-radius: 5px;
      width: 290px;
      background: #fff;
      margin-bottom: 10px;
      cursor: pointer;
      .hoverShadow();
      a {
        display: block;
        width: 100%;
        position: relative;
        img {
          border-radius: 5px;
          width: 100%;
          height: 160px;
        }
      }
      .foot {
        padding: 13px;
        font-size: 16px;
        .meta {
          margin-bottom: 10px;
          font-size: 16px;
          font-weight: 500;
        }
        // 一行省略
        .ellipsis {
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }

        .info {
          display: flex;
          align-items: center;
          justify-content: space-between;
          &-left {
            display: flex;
            font-size: 13px;

            .iconfont {
              color: @warmOrange;
              font-weight: bold;
            }

            .lecturer {
              margin-right: 15px;
            }
          }
          &-right {
            .study_immediate {
              background-color: #eff4fe;
              border-radius: 5px;
              color: #666;
              font-family: Microsoft YaHei;
              font-size: 14px;
              font-weight: 400;
              height: 28px;
              line-height: 28px;
              text-align: center;
              width: 88px;
              border: none;
            }
            .already_completion {
              background: #f7f7f7;
              border-radius: 5px;
              color: #333;
              font-family: Microsoft YaHei;
              font-size: 14px;
              font-weight: 400;
              height: 28px;
              line-height: 28px;
              text-align: center;
              width: 88px;
              border: none;
            }
          }
        }
      }
    }

    .course-item:hover .info-right .study_immediate {
      background: @warmOrange;
      color: #fff !important;
    }
    .course-item:hover .info-right .already_completion {
      color: #333;
    }
    :deep(.el-button:focus) {
      color: #666 !important;
    }
  }
</style>
