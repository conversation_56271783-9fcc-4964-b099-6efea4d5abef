/*
 * @Description: userStore
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-05 16:36:56
 * @LastEditTime: 2025-02-25 14:54:33
 */
import { defineStore } from "pinia"
import { getInfo, logout, getUserExtraInfo } from "@/api/system/user"
import { getToken, removeToken, setToken } from "@/utils/auth"
import type { userType } from "@/types"
import defAva from "@/assets/images/avatar.png"

const useUserStore = defineStore("user", {
  state: () => ({
    token: getToken(),
    name: "",
    nickName: "",
    avatar: "",
    faceImg: "",
    roles: [] as Array<string>,
    permissions: [],
    userInfo: {} as any,
    isFromDoubleControl: false,
    extraInfo: null as any
  }),
  actions: {
    // 更新本地token状态
    updateToken() {
      this.token = getToken()
      return this.token
    },
    // 获取用户信息
    async getInfo() {
      try {
        // 先更新token状态
        this.updateToken()
        
        const res: any = await getInfo()
        if (res.code === 200) {
          this.userInfo = res.user
          this.avatar = res.user.avatar || defAva
          this.name = res.user.userName
          this.nickName = res.user.nickName
          this.faceImg = res.user.faceImg
  
          if (res.roles && res.roles.length > 0) {
            // 验证返回的roles是否是一个非空数组
            this.roles = res.roles
            this.permissions = res.permissions
          } else {
            this.roles = ["ROLE_DEFAULT"]
          }
          return res
        } else {
          this.logOutAction()
          return Promise.reject(res.msg || '获取用户信息失败')
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        return Promise.reject(error)
      }
    },
    // 添加获取extraInfo的action
    async fetchExtraInfo(force = false) {
      if (this.extraInfo && !force) return
      const data = await getUserExtraInfo()
      this.extraInfo = data
    },
    // 退出系统
    logOutAction() {
      return new Promise((resolve, reject) => {
        logout()
          .then(() => {
            this.token = ""
            this.name = ""
            this.nickName = ""
            this.avatar = ""
            this.faceImg = ""
            this.roles = []
            this.permissions = []
            this.userInfo = {}
            this.extraInfo = null // 清空extraInfo
            removeToken()
            resolve(1)
          })
          .catch(error => {
            reject(error)
          })
      })
    }
  }
})

export default useUserStore
