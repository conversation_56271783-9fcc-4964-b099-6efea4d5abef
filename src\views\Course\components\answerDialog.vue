<!--
 * @Description: 视频答题弹窗
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-07-05 17:22:55
 * @LastEditTime: 2024-12-05 16:36:10
-->

<template>
  <el-dialog
    title="课堂问答"
    width="1000px"
    :show-close="false"
    v-model="visible"
    :append-to-body="true"
    :close-on-click-modal="false"
    center
  >
    <div class="question_container">
      <div class="countdown" v-if="showCountdown">
        剩余时间：{{ Math.floor(countdown / 60) }}:{{
          (countdown % 60).toString().padStart(2, "0")
        }}
      </div>
      <div class="name">
        <div class="question_type">
          【<dict-tag
            style="display: inline"
            :options="exam_question_type"
            :value="qsInfo.questionType"
          />题】
        </div>
        <div class="question_name">
          {{ qsInfo.questionName }}
        </div>
      </div>
      <div class="options">
        <el-radio-group
          v-model="answerValue"
          v-if="qsInfo.questionType === 'S' || qsInfo.questionType === 'J'"
          class="group"
        >
          <template v-for="choice in 11">
            <el-radio
              class="group-item"
              v-if="qsInfo[`item${choice}`]"
              :name="String.fromCharCode(64 + choice)"
              :key="choice"
              :label="`${String.fromCharCode(64 + choice)}`"
              :disabled="isShowAnalysis"
              >{{
                `${String.fromCharCode(64 + choice)}. &nbsp;${qsInfo[`item${choice}`]}`
              }}</el-radio
            >
          </template>
        </el-radio-group>

        <el-checkbox-group v-model="answerValue" class="group" v-if="qsInfo.questionType === 'M'">
          <template v-for="multiChoice in 11">
            <el-checkbox
              class="group-item"
              v-if="qsInfo[`item${multiChoice}`]"
              :key="multiChoice"
              :label="`${String.fromCharCode(64 + multiChoice)}`"
              :disabled="isShowAnalysis"
              >{{
                `${String.fromCharCode(64 + multiChoice)}. &nbsp;${qsInfo[`item${multiChoice}`]}`
              }}
            </el-checkbox>
          </template>
        </el-checkbox-group>
      </div>
      <div class="analysis" v-show="isShowAnalysis">
        <div class="question_answer">
          <span class="title">【答案】：</span>
          {{ qsInfo.answer }}
        </div>
        <div class="question_analysis" v-if="qsInfo.analysis">
          <span class="title">【分析】：</span>
          {{ qsInfo.analysis }}
        </div>
      </div>
    </div>

    <template #footer>
      <el-button v-if="!isShowAnalysis" type="primary" @click="handleSubmit"> 提 交 </el-button>
      <el-button v-else type="primary" @click="handleClose(false)"> 继续观看 </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ElMessage } from "element-plus"
  import { getQuestion } from "@/api/onlineExam"
  import { addAnswer } from "@/api/questionnaire"
  import useStore from "@/store"

  const { proxy } = getCurrentInstance()!
  const { exam_question_type } = proxy!.useDict("exam_question_type")
  const emit = defineEmits(["done"])
  const visible = ref(false)
  const answerValue = ref<any>()

  const countdown = ref(0)
  const countdownTimer = ref<any>(null)
  const showCountdown = ref(false)

  const { user } = useStore()
  const qsInfo = ref<any>({})
  const openDialog = async qsItem => {
    visible.value = true
    const res = await getQuestion(qsItem.questionId)
    qsInfo.value = res.data
    if (qsInfo.value.questionType === "S" || qsInfo.value.questionType === "J") {
      answerValue.value = ""
    } else if (qsInfo.value.questionType === "M") {
      answerValue.value = []
    }
    
    if (qsItem.countdownDuration) {
      countdown.value = qsItem.countdownDuration
      showCountdown.value = true
      startCountdown()
    } else {
      showCountdown.value = false
    }
  }

  const startCountdown = () => {
    clearInterval(countdownTimer.value)
    countdownTimer.value = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(countdownTimer.value)
        handleTimeUp()
      }
    }, 1000)
  }

  const handleTimeUp = async () => {
    ElMessage({
      message: "答题时间已到",
      type: "warning"
    })
    
    const submitData = {
      userId: user.userInfo.userId,
      questionId: qsInfo.value.questionId,
      userAnswer: "",
      remark: qsInfo.value.passageId
    }
    await addAnswer(submitData)
    
    handleClose(false, true)
  }

  const isShowAnalysis = ref(false)
  const handleSubmit = async () => {
    if (!answerValue.value) {
      ElMessage({
        message: "请选择你的答案",
        type: "warning"
      })
      return
    }
    let answerData
    if (qsInfo.value.questionType === "M") {
      answerData = Array.isArray(answerValue.value)
        ? answerValue.value.sort().join(",")
        : answerValue.value
    } else {
      answerData = answerValue.value
    }
    // 接口调用
    const submitData = {
      userId: user.userInfo.userId,
      questionId: qsInfo.value.questionId,
      userAnswer: answerData,
      remark: qsInfo.value.passageId
    }
    await addAnswer(submitData)
    // 判断答题是否正确
    if (answerData === qsInfo.value.answer) {
      ElMessage({
        message: "恭喜您，回答正确！",
        type: "success"
      })
      handleClose(true)
    } else {
      ElMessage({
        message: "对不起，回答错误",
        type: "error"
      })
      isShowAnalysis.value = true
      handleClose(false, true)
    }
  }

  const handleClose = (flag, needReset = false) => {
    if (countdownTimer.value) {
      clearInterval(countdownTimer.value)
    }
    emit("done", flag, needReset)
    answerValue.value = ""
    visible.value = false
    isShowAnalysis.value = false
    showCountdown.value = false
  }

  onBeforeUnmount(() => {
    if (countdownTimer.value) {
      clearInterval(countdownTimer.value)
    }
  })

  defineExpose({
    openDialog,
    visible
  })
</script>

<style lang="less" scoped>
  .question_container {
    padding: 0 50px 50px;
    display: flex;
    flex-direction: column;
    font-size: 16px;

    .countdown {
      position: absolute;
      top: 20px;
      right: 30px;
      font-size: 18px;
      color: #f56c6c;
      font-weight: bold;
    }

    .name {
      margin-bottom: 30px;
      font-size: 18px;
      display: flex;
      .question_type {
        display: flex;
        font-weight: 600;
        color: #12aef4;
        width: 90px;
      }
      .question_name {
        flex: 1;
        line-height: 28px;
      }
    }

    .options {
      display: flex;
      flex-direction: column;
      padding-left: 90px;
      margin-bottom: 20px;
      .group {
        display: flex;
        flex-direction: column;
        align-items: start;
        justify-content: flex-start;
        :deep(.el-radio) {
          height: 46px;
          .el-radio__label {
            font-size: 17px !important;
          }
        }
        :deep(.el-checkbox) {
          max-width: 100%;
          height: 46px;
          max-height: 60px;
          .el-checkbox__label {
            line-height: 22px;
            font-size: 17px !important;
            white-space: pre-line;
          }
        }
      }
    }

    .analysis {
      font-weight: 600;
      font-size: 18px;
      .question_answer {
        width: 100%;
        margin-right: 10px;
        display: flex;
        margin-bottom: 8px;
      }
    }
  }

  .title {
    color: #12aef4;
  }
</style>
