<!--
 * @Description: 上海市普陀区企业合同信用促进会-首页
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2025-02-25 11:01:57
 * @LastEditTime: 2025-06-03 17:26:40
-->

<script lang="ts" setup>
  import HomeRecommend from "../components/home-recommend.vue"
  import { ref, onMounted, computed } from "vue"
  import { getEnterpriseList } from "@/api/system/enterprise"
  import { ElMessage, ElLoading } from "element-plus"
  import EnterpriseCard from "./EnterpriseCard.vue"
  import ProductDialog from "./ProductDialog.vue"
  import RegisterEnterpriseDialog from "./RegisterEnterpriseDialog.vue"
  import { Search, Plus } from "@element-plus/icons-vue"
  import useUserStore from "@/store/modules/user"

  // 定义企业类型接口
  interface Enterprise {
    id: number
    enterpriseName: string
    contactName: string
    position: string
    phone: string
    address: string
    enterpriseIntro: string
    createBy: string | null
    createTime: string | null
    updateBy: string | null
    updateTime: string | null
    remark: string | null
  }

  const userStore = useUserStore()
  // 计算属性：用户是否已登录
  const isUserLoggedIn = computed(() => {
    return userStore.token
  })

  const loading = ref(false)
  const enterpriseList = ref<Enterprise[]>([])
  const showProductDialog = ref(false)
  const currentEnterprise = ref<Enterprise | null>(null)
  const searchKeyword = ref("")

  // 获取企业列表
  const fetchEnterpriseList = async (searchParams?: { enterpriseName?: string }) => {
    loading.value = true
    const loadingInstance = ElLoading.service({ fullscreen: true, text: "加载企业数据中..." })
    try {
      const res = await getEnterpriseList(searchParams || {})
      if (res.code === 200) {
        enterpriseList.value = res.rows || []
      } else {
        ElMessage.error(res.msg || "获取企业列表失败")
      }
    } catch (error) {
      console.error("获取企业列表失败:", error)
      ElMessage.error("获取企业列表失败")
    } finally {
      loadingInstance.close()
      loading.value = false
    }
  }

  // 查看企业详情
  const viewEnterpriseDetails = (enterprise: Enterprise) => {
    currentEnterprise.value = enterprise
    showProductDialog.value = true
  }

  // 搜索企业
  const handleSearch = () => {
    if (searchKeyword.value.trim()) {
      fetchEnterpriseList({ enterpriseName: searchKeyword.value.trim() })
    } else {
      fetchEnterpriseList()
    }
  }

  // 清空输入框（不调用接口）
  const handleClear = () => {
    searchKeyword.value = ""
  }

  // 重置搜索（清空并调用接口）
  const handleReset = () => {
    searchKeyword.value = ""
    fetchEnterpriseList()
  }

  const registerEnterpriseDialogRef = ref()
  const handleRegisterEnterprise = () => {
    registerEnterpriseDialogRef.value.open()
  }

  // 刷新企业列表
  const refreshEnterpriseList = () => {
    fetchEnterpriseList()
  }

  onMounted(() => {
    fetchEnterpriseList()
  })
</script>
<template>
  <div>
    <!-- 诚信产品服务平台 首批产品清单 -->
    <div class="mx-auto my-20px rounded-15px p-20px pt-20px pb-10px w-95% bg-white">
      <div class="flex items-center justify-between mb-20px">
        <div class="border-l-5px border-l-solid border-[#6b7dfa] pl-10px">
          <div class="text-22px">诚信产品服务平台 首批产品清单</div>
          <div class="text-8px pb-3px border-b border-b-solid border-[#6b7dfa]">
            INTEGRITY PRODUCT SERVICE PLATFORM
          </div>
        </div>

        <div class="flex items-center">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索公司名称、联系人或产品名称"
            class="w-260px"
            clearable
            @clear="handleClear"
            :prefix-icon="Search"
            @keyup.enter="handleSearch"
          />
          <el-button type="primary" class="ml-10px" @click="handleSearch"> 搜索 </el-button>
          <el-button class="ml-10px" @click="handleReset"> 重置 </el-button>
          <div class="ml-10px text-gray-500 text-13px">
            找到 {{ enterpriseList.length }} 个结果
          </div>
          <el-button
            v-if="isUserLoggedIn"
            :icon="Plus"
            type="primary"
            class="ml-10px"
            @click="handleRegisterEnterprise"
          >
            注册企业
          </el-button>
        </div>
      </div>

      <el-skeleton :rows="3" animated v-if="loading" />

      <div
        v-else-if="enterpriseList.length > 0"
        class="grid grid-cols-[repeat(auto-fill,minmax(320px,1fr))] gap-16px mt-20px"
      >
        <EnterpriseCard
          v-for="enterprise in enterpriseList"
          :key="enterprise.id"
          :enterprise="{
            id: enterprise.id,
            name: enterprise.enterpriseName,
            description: enterprise.enterpriseIntro,
            category: enterprise.position,
            establishDate: enterprise.createTime,
            contactName: enterprise.contactName,
            phone: enterprise.phone
          }"
          @view-details="viewEnterpriseDetails(enterprise)"
        />
      </div>

      <el-empty v-else description="暂无符合条件的企业数据" />
    </div>

    <!-- 推荐课程 -->
    <HomeRecommend />

    <!-- 产品详情弹窗 -->
    <ProductDialog
      v-model:visible="showProductDialog"
      :enterprise-id="currentEnterprise?.id"
      :enterprise-name="currentEnterprise?.enterpriseName"
    />
    <!-- 注册企业弹窗 -->
    <RegisterEnterpriseDialog
      ref="registerEnterpriseDialogRef"
      @refreshDataList="refreshEnterpriseList"
    />
  </div>
</template>
