/*
 * @Description:
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-16 15:35:39
 * @LastEditTime: 2025-02-25 10:33:46
 */
import request from "@/utils/request"

// 获取key
export function getPublicKey() {
  return request({
    url: "/auth/publicKey",
    method: "get"
  })
}

// 发送验证码
export function sendCode(data) {
  return request({
    url: "/auth/send-sms-code",
    method: "post",
    data
  })
}

// 手机验证码登录
export function handleSMSLogin(data) {
  return request({
    url: "/auth/sms-login",
    headers: {
      isToken: false
    },
    method: "post",
    data
  })
}

export function loginRequest(data) {
  return request({
    url: "/auth/login",
    headers: {
      isToken: false
    },
    method: "post",
    data
  })
}
