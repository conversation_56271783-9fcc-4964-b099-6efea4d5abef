<!--
 * @Description: VideoPlayer
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-14 09:43:19
 * @LastEditTime: 2023-03-15 08:51:46
-->
<template>
  <video ref="rVideoPlayer" class="video-js vjs-default-skin"></video>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import videoJs from "video.js";
const props = defineProps({
  options: {
    type: Object,
    default: () => ({}),
  },
});

const rVideoPlayer = ref<Element>();
let player = ref();
onMounted(() => {
  player.value = videoJs(rVideoPlayer.value as Element, props.options);
});
onUnmounted(() => {
  if (player.value) {
    player.value.dispose();
  }
});
</script>
