<!--
 * @Description: 
 * @Author: <PERSON><PERSON>
 * @LastEditors: sun<PERSON><PERSON>
 * @Date: 2023-04-17 08:49:10
 * @LastEditTime: 2023-09-06 15:55:25
-->
<script setup lang="ts">
  import useStore from "@/store"

  const { home } = useStore()
  home.getBannerList()
</script>

<template>
  <div class="home-banner">
    <div> <BaseSlider :sliders="home.bannerList" auto-play /></div>
  </div>
</template>

<style scoped lang="less">
  .home-banner {
    width: 65%;
  }
  // .home-banner {
  //   padding: 42px 0;
  //   background: #fff;
  //   position: sticky;
  //   z-index:10;
  // /*   box-shadow: 0 2px 15px 0 hsla(0, 0%, 45%, 0.2);
  //   */ > div {
  //     width: 1240px;
  //     margin: 0 auto;
  //   }
  // }
</style>
