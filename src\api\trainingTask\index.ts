/*
 * @Description: 培训任务相关API
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-11-25 10:46:46
 * @LastEditTime: 2024-11-29 15:09:08
 */
import request from "@/utils/request"

// 我的项目列表
export function fetchTaskMyList(query: Object) {
  return request({
    url: "/course/task/myList",
    method: "get",
    params: query
  })
}

// 通过 ID 获取项目信息
export function getTaskById(taskId, params?) {
  return request({
    url: "/course/task/taskDetail/" + taskId,
    method: "get",
    params
  })
}

// 通过 ID 获取任务详情
export function getTaskInfoById(taskId, params?) {
  return request({
    url: "/course/task/" + taskId,
    method: "get",
    params
  })
}

// 数据一览 任务分布
// queryTimeFrom  queryTimeTo
export function dashboard(params) {
  return request({
    url: "/course/task/dashboard",
    method: "get",
    params
  })
}

// 查询培训任务跟踪详细
export function getTrainingTrack(taskId) {
  return request({
    url: "/course/record/" + taskId,
    method: "get"
  })
}

// 培训课程统计
export function getTrainingCourseList(params) {
  return request({
    url: "/reportforms/report/course-list",
    method: "get",
    params
  })
}
// 培训考试统计
export function getTrainingExamList(params) {
  return request({
    url: "/reportforms/report/exam-list",
    method: "get",
    params
  })
}
// 培训问卷统计
export function getTrainingQuestionnaireList(params) {
  return request({
    url: "/reportforms/report/questionnaire-list",
    method: "get",
    params
  })
}
