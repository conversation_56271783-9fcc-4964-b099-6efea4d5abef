<script lang="ts">
  export default {
    name: "homeRecommend"
  }
</script>
<script lang="ts" setup>
  import { hotCourseList } from "@/api/home"
  import { fetchCourseList } from "@/api/course"
  import type { classType } from "@/types"
  import { DEFAULT_COVER } from "@/utils/constant"
  import useUserStore from "@/store/modules/user"
  import useTenantStore from "@/store/modules/tenant"
  import LoginDialog from "@/views/Home/pt/LoginDialog.vue"
  import { useRouter } from "vue-router"
  import { ref, onMounted, onUnmounted } from "vue"
  import { getToken } from "@/utils/auth"
  const userStore = useUserStore()
  const tenantStore = useTenantStore()
  const router = useRouter()
  const showLoginDialog = ref(false)
  const lastClickedCourseId = ref<string | null>(null)

  const recommendCourseList = ref<classType[]>()
  // 获取首页推荐课程
  const getRecommendCourse = async () => {
    // let queryData = {
    //   pageNum: 1,
    //   pageSize: 10,
    //   sortField: "a.update_time",
    //   sortOrder: "desc",
    //   courseStatus: "2"
    // }
    // const { rows } = await fetchCourseList(queryData)
    const { data } = await hotCourseList()
    recommendCourseList.value = data.slice(0, 10) || []
  }

  // 课程点击处理
  const handleCourseClick = (courseItem: any, event: Event) => {
    const { courseId, hasTask } = courseItem

    // 如果课程有任务，跳转到待办页面
    if (hasTask) {
      event.preventDefault()
      router.push({
        path: "/study/todo"
      })
      return
    }

    if (tenantStore.domainName === "pt") {
      const token = userStore.token || getToken()
      if (!token) {
        event.preventDefault()
        // 保存最后点击的课程ID，登录成功后跳转
        lastClickedCourseId.value = courseId.toString()

        // 使用统一的全局登录弹窗事件
        const customEvent = new CustomEvent("global-login-dialog", {
          detail: { source: "course-click", courseId: courseId.toString() }
        })
        window.dispatchEvent(customEvent)
      } else {
        router.push(`/course/detail?id=${courseId}&taskId=1`)
      }
    } else {
      router.push(`/course/detail?id=${courseId}`)
    }
  }

  // 登录成功后的回调
  const handleLoginSuccess = () => {
    if (lastClickedCourseId.value) {
      router.push(`/course/detail?id=${lastClickedCourseId.value}`)
      lastClickedCourseId.value = null
    }
  }

  // 监听全局登录弹窗事件
  const handleGlobalLoginDialog = event => {
    // 如果事件来源是课程点击，则记录该课程ID
    if (event.detail && event.detail.source === "course-click") {
      lastClickedCourseId.value = event.detail.courseId
    }
    // 由顶部导航栏统一管理登录弹窗的显示，这里不需要设置showLoginDialog
  }

  // 监听全局登录成功事件
  const handleGlobalLoginSuccess = () => {
    handleLoginSuccess()
  }

  // 添加全局事件监听
  onMounted(() => {
    window.addEventListener("global-login-dialog", handleGlobalLoginDialog)
    window.addEventListener("global-login-success", handleGlobalLoginSuccess)
  })

  // 移除全局事件监听
  onUnmounted(() => {
    window.removeEventListener("global-login-dialog", handleGlobalLoginDialog)
    window.removeEventListener("global-login-success", handleGlobalLoginSuccess)
  })

  getRecommendCourse()
</script>
<template>
  <div class="recommend">
    <div class="recommend-title">
      <div class="recommend-title-left">
        <div>推荐课程</div>
        <div>RECOMMENDED COURSES</div>
      </div>
      <RouterLink class="recommend-title-right" to="/course">
        <div style="margin-right: 5px">更多课程</div>
        <el-icon><ArrowRight /></el-icon>
      </RouterLink>
    </div>
    <div class="recommend-list">
      <div
        @click="e => handleCourseClick(item, e)"
        class="recommend-item"
        v-for="item in recommendCourseList"
        :key="item.courseId"
      >
        <div class="recommend-img">
          <img :src="item.courseImage || DEFAULT_COVER" alt="" />
        </div>
        <div class="foot">
          <div class="meta ellipsis">{{ item.courseName }} </div>
          <div class="info">
            <div class="info-left">
              <div class="hot">
                <i class="iconfont icon-remen"></i>
                {{ item.hotValue || 0 }}</div
              >
            </div>
            <div class="info-right">
              <el-button type="primary" class="study_immediate"> 立即学习 </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 登录弹窗 -->
    <LoginDialog v-model:visible="showLoginDialog" @login-success="handleLoginSuccess" />
  </div>
</template>
<style scoped lang="less">
  .recommend {
    margin: 20px auto;
    border-radius: 15px;
    padding: 20px 20px 10px 20px;
    width: 95%;
    background-color: white;
    .recommend-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
      .recommend-title-left {
        border-left: 5px solid #6b7dfa;
        padding-left: 10px;
        :first-child {
          font-size: 22px;
        }
        :last-child {
          font-size: 8px;
          padding-bottom: 3px;
          border-bottom: 1px solid #6b7dfa;
        }
      }
      .recommend-title-right {
        display: flex;
        align-items: center;
      }
    }
    .recommend-list {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      grid-column-gap: 20px;
      .recommend-item {
        box-shadow: 0 2px 10px 0 rgba(103, 111, 144, 0.15);
        box-sizing: border-box;
        border-radius: 5px;
        // width: 290px;
        background: #fff;
        margin-bottom: 30px;
        overflow: hidden;
        cursor: pointer;
        .hoverShadow();
        a {
          display: block;
          width: 100%;
          position: relative;
          img {
            /*  border-radius: 5px; */
            width: 100%;
            height: 160px;
          }
        }
        .foot {
          padding: 13px;
          font-size: 14px;
          color: #545c63;
          .meta {
            margin-bottom: 10px;
            font-size: 14px;
            font-weight: 500;
          }
          // 一行省略
          .ellipsis {
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }

          .info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            &-left {
              display: flex;
              font-size: 13px;

              .iconfont {
                color: @warmOrange;
                font-weight: bold;
              }

              .lecturer {
                margin-right: 15px;
              }
            }
            &-right {
              .study_immediate {
                background: #3a86f2;
                border-radius: 5px;
                color: white;
                font-family: Microsoft YaHei;
                font-size: 14px;
                font-weight: 400;
                height: 28px;
                line-height: 28px;
                text-align: center;
                width: 88px;
                border: none;
              }
            }
          }
        }
        .recommend-img {
          width: 100%;
          height: 160px;
          overflow: hidden;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 5px 5px 0 0;
          }
        }
      }

      .recommend-item:hover .study_immediate {
        background: #3a86f2 !important;
        color: #fff !important;
      }
    }
  }
  // .recommend-list {
  //   display: grid;
  //   grid-template-columns: repeat(5, 1fr);
  //   grid-column-gap: 20px;
  //   .recommend-item {
  //     box-shadow: 0 2px 10px 0 rgba(103, 111, 144, 0.15);
  //     box-sizing: border-box;
  //     border-radius: 5px;
  //     width: 290px;
  //     background: #fff;
  //     margin-bottom: 30px;
  //     overflow: hidden;
  //     cursor: pointer;
  //     .hoverShadow();
  //     a {
  //       display: block;
  //       width: 100%;
  //       position: relative;
  //       img {
  //         /*  border-radius: 5px; */
  //         width: 100%;
  //         height: 160px;
  //       }
  //     }
  //     .foot {
  //       padding: 13px;
  //       font-size: 14px;
  //       color: #545c63;
  //       .meta {
  //         margin-bottom: 10px;
  //         font-size: 14px;
  //         font-weight: 500;
  //       }
  //       // 一行省略
  //       .ellipsis {
  //         white-space: nowrap;
  //         text-overflow: ellipsis;
  //         overflow: hidden;
  //       }

  //       .info {
  //         display: flex;
  //         align-items: center;
  //         justify-content: space-between;
  //         &-left {
  //           display: flex;
  //           font-size: 13px;

  //           .iconfont {
  //             color: @warmOrange;
  //             font-weight: bold;
  //           }

  //           .lecturer {
  //             margin-right: 15px;
  //           }
  //         }
  //         &-right {
  //           .study_immediate {
  //             background: #eff4fe;
  //             border-radius: 5px;
  //             color: #666;
  //             font-family: Microsoft YaHei;
  //             font-size: 14px;
  //             font-weight: 400;
  //             height: 28px;
  //             line-height: 28px;
  //             text-align: center;
  //             width: 88px;
  //             border: none;
  //           }
  //         }
  //       }
  //     }
  //   }

  //   .recommend-item:hover .study_immediate {
  //     background: @warmOrange !important;
  //     color: #fff !important;
  //   }
  // }

  // .home-skeleton {
  //   width: 1240px;
  //   height: 365px;
  //   display: flex;
  //   justify-content: space-between;
  //   .item {
  //     width: 306px;
  //     .base-skeleton ~ .base-skeleton {
  //       display: block;
  //       margin: 5px 0 0 0;
  //     }
  //   }
  // }
</style>
