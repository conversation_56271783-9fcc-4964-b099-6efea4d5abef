<!--
 * @Description: Ecahrts全局组件
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-27 14:05:14
 * @LastEditTime: 2024-11-14 16:36:57
-->
<template>
  <div ref="chartDom" class="chartDom"></div>
</template>
<script setup name="Echarts">
  import * as echarts from "echarts"
  import { debounce } from "lodash-es"

  const unWarp = obj => obj && (obj.__v_raw || obj.valueOf() || obj)
  const props = defineProps({
    // 图表配置项
    option: {
      type: Object,
      default: {}
    }
  })
  const chartDom = ref(null)
  const chartInstance = ref(null)

  // 自适应不同屏幕时改变图表尺寸
  const chartResize = debounce(() => {
    nextTick(() => {
      chartInstance.value && chartInstance.value.resize()
    })
  }, 100)

  // 监听图表数据时候变化,重新渲染图表
  watch(
    () => props.option,
    v => {
      chartInstance.value && v && unWarp(chartInstance.value).setOption(v, true) // 渲染图表
      chartResize()
    },
    { immediate: true, deep: true }
  )
  // 页面成功渲染,开始绘制图表
  onMounted(() => {
    // 配置为svg形式,预防页面缩放而出现模糊问题;图表过于复杂时建议使用 Canvas
    chartInstance.value = echarts.init(chartDom.value, null, {
      renderer: "svg"
    })
    chartResize()
    window.addEventListener("resize", chartResize)
  })
  // 页面销毁前,销毁事件和实例
  onBeforeUnmount(() => {
    window.removeEventListener("resize", chartResize)
    chartInstance.value.dispose()
  })
</script>

<style scoped lang="less">
  .chartDom {
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
</style>
