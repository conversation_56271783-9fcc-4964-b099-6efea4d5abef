<!--
 * @Description: 考场准备页面
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-17 13:55:12
 * @LastEditTime: 2024-12-19 14:55:37
-->
<script setup lang="ts">
  import {
    specialTenantList,
    noNeedLayOutDomainList,
    noNeedExamScoreDomainList
  } from "@/utils/constant"
  import { ArrowRight } from "@element-plus/icons-vue"
  import type { ExamDetailInfoType, examRecordType, examPaperAnswerType } from "@/types"
  import { getExam, getArrange } from "@/api/onlineExam"
  import useTenantStore from "@/store/modules/tenant"
  import { isInTimeRange } from "@/utils/common"

  const { domainName } = storeToRefs(useTenantStore())
  const route = useRoute()
  const router = useRouter()
  const examText = computed(() => {
    return domainName.value === "unep" ? "测试" : "考试"
  })

  const { baseId, arrangeId, taskId } = route.query

  const examDetailInfo = ref<ExamDetailInfoType>()
  const loadData = async () => {
    const { data } = await getExam(baseId)
    examDetailInfo.value = data
  }

  const examRecordInfo = ref<examRecordType>()
  // 已完成考试列表
  const completedExamRecordList = ref<examPaperAnswerType[]>()
  const loadRecordData = async () => {
    const { data } = await getArrange(arrangeId)
    examRecordInfo.value = data
    // 已完成的考试才显示在考试记录中
    if (!data.examPaperAnswers || data.examPaperAnswers.length === 0) return
    completedExamRecordList.value = data.examPaperAnswers
      .map(item => {
        if (item.examStatus === "2") {
          return item
        }
      })
      .filter(ite => typeof ite !== "undefined")
  }

  const activeName = ref("first")

  const startExam = async () => {
    router.push({
      path: "/examing",
      query: {
        baseId,
        arrangeId,
        taskId,
        paperId: examDetailInfo.value?.paperId,
        baseName: examDetailInfo.value?.baseName,
        examDurationLimit: examDetailInfo.value?.examConfig.examDurationLimit,
        lowestScore: examDetailInfo.value?.examConfig.lowestScore,
        minimumScoreRate: examDetailInfo.value?.examConfig.minimumScoreRate,
        switchingTimes: examDetailInfo.value?.examConfig.switchingTimes,
        submintMinimumNumber: examDetailInfo.value?.examConfig.submintMinimumNumber,
        faceCapture: examDetailInfo.value?.examConfig.faceCapture,
        recordCredit: examDetailInfo.value?.examConfig.recordCredit
      }
    })
  }

  // 查看试卷解析
  const pushToExamed = item => {
    router.push({
      path: "/examed",
      query: {
        baseId: item.baseId,
        arrangeId: item.arrangeId,
        paperAnswerId: item.paperAnswerId
      }
    })
  }

  const displayContent = (startTime, endTime) => {
    var now = new Date() // 获取当前时间
    var start = new Date(startTime) // 将开始时间转换为Date对象
    var end = new Date(endTime) // 将结束时间转换为Date对象

    if (now < start) {
      return "还未开考"
    } else if (now > end) {
      return "考试已结束"
    }
  }

  onMounted(() => {
    loadData()
    loadRecordData()
  })
</script>

<template>
  <div class="p10">
    <el-breadcrumb
      class="mb-20px"
      :separator-icon="ArrowRight"
      v-if="!specialTenantList.includes(domainName) && !noNeedLayOutDomainList.includes(domainName)"
    >
      <el-breadcrumb-item to="/study/todo">学习中心</el-breadcrumb-item>
      <el-breadcrumb-item to="/study/exam">我的考试</el-breadcrumb-item>
      <el-breadcrumb-item>{{ examDetailInfo?.baseName }}</el-breadcrumb-item>
    </el-breadcrumb>
    <div
      class="examInfo"
      :style="{ marginTop: noNeedLayOutDomainList.includes(domainName) ? '300px' : '0' }"
    >
      <img
        style="position: absolute; top: 0; left: 0; height: 100%"
        src="@/assets/images/prepare_left.png"
        alt=""
      />
      <img
        style="position: absolute; top: 0; right: 0; height: 100%"
        src="@/assets/images/prepare_right.png"
        alt=""
      />
      <div class="examInfo-title">{{ examDetailInfo?.baseName }}</div>
      <div class="examInfo-info">
        <div>
          {{ examText }}时长：
          {{
            examDetailInfo?.examConfig.examDurationLimit === 0 ||
            !examDetailInfo?.examConfig.examDurationLimit
              ? "不限时"
              : `${examDetailInfo?.examConfig.examDurationLimit}分钟`
          }}
        </div>
        <el-divider direction="vertical"></el-divider>
        <div
          >{{ examText }}次数：
          <span v-if="examRecordInfo?.remainExamCount == -1"> 无限次 </span>
          <span v-else>
            还有
            <span class="dark-blue-text">{{ examRecordInfo?.remainExamCount }} </span>
            次机会
          </span>
        </div>
      </div>
      <div class="examInfo-time" v-if="examRecordInfo?.startTime">
        {{ examText }}时间： {{ examRecordInfo?.startTime }}~{{ examRecordInfo?.endTime }}
      </div>
      <div
        v-if="!isInTimeRange(examRecordInfo?.startTime, examRecordInfo?.endTime)"
        class="examInfo-end"
      >
        {{ displayContent(examRecordInfo?.startTime, examRecordInfo?.endTime) }}
      </div>
      <div
        class="examInfo-button"
        v-else-if="examRecordInfo?.remainExamCount != 0"
        @click="startExam"
      >
        开始{{ examText }}
      </div>
      <div v-else class="examInfo-end">已无{{ examText }}次数</div>
    </div>
    <div class="config" v-if="!noNeedLayOutDomainList.includes(domainName)">
      <el-card class="box-card">
        <el-tabs v-model="activeName" class="demo-tabs">
          <el-tab-pane :label="`${examText}记录`" name="first">
            <div class="configDetail">
              <div class="rules" v-if="!specialTenantList.includes(domainName)">
                <div class="subTitle">考试规则</div>
                <span class="configText">
                  切屏次数：
                  {{
                    !examDetailInfo?.examConfig.switchingTimes
                      ? "无限制"
                      : examDetailInfo?.examConfig.switchingTimes === 0
                      ? "不允许切屏"
                      : `${examDetailInfo?.examConfig.switchingTimes}次`
                  }}
                </span>
                <span class="configText" v-if="examDetailInfo?.examConfig.submintMinimumNumber">
                  交卷控制： 答卷时间少于
                  {{ examDetailInfo?.examConfig.submintMinimumNumber }}
                  分钟禁止交卷
                </span>
                <span class="configText" v-if="examDetailInfo?.examConfig.beFiveLimitMinute">
                  迟到控制： 迟到
                  {{ examDetailInfo?.examConfig.beFiveLimitMinute }}
                  分钟后禁止参加考试
                </span>
                <span
                  class="configText"
                  v-if="
                    examDetailInfo?.examConfig.lowestScore &&
                    !noNeedExamScoreDomainList.includes(domainName)
                  "
                >
                  及格条件：
                  <span v-if="examDetailInfo?.examConfig.lowestScore">
                    成绩达到{{ examDetailInfo?.examConfig.lowestScore }}分
                  </span>
                  <span v-else> 得分率达到{{ examDetailInfo?.examConfig.minimumScoreRate }}% </span>
                </span>
                <span class="configText">
                  查看状态：
                  {{ examDetailInfo?.examConfig.isViewScore ? "允许" : "不允许" }}
                </span>
                <span class="configText">
                  查看答卷：
                  {{ examDetailInfo?.examConfig.isViewAnswer ? "允许" : "不允许" }}</span
                >
                <span class="configText">
                  评卷规则： 多选题{{
                    examDetailInfo?.examConfig.ruleMcq === "A"
                      ? "全部答对给分"
                      : examDetailInfo?.examConfig.ruleMcq === "B"
                      ? "按正确选项个数给分"
                      : `全部答对给分，不完全答对给${
                          examDetailInfo?.examConfig.ruleMcqScore || 0
                        }分`
                  }}； 填空题{{
                    examDetailInfo?.examConfig.ruleCloze === "A"
                      ? "全部答对给分"
                      : examDetailInfo?.examConfig.ruleCloze === "B"
                      ? "按正确选项个数给分"
                      : `人工评定`
                  }}
                </span>
                <span class="configText" v-if="examDetailInfo?.examConfig.ruleUserName">
                  评卷人： {{ examDetailInfo?.examConfig.ruleUserName }}
                </span>
              </div>
              <div
                class="record"
                v-if="completedExamRecordList && completedExamRecordList?.length > 0"
              >
                <div class="subTitle">
                  <div class="subtitle-text">考试记录</div>
                  <div class="count-text">共{{ completedExamRecordList?.length }}次考试</div>
                </div>

                <div class="recordList" v-for="item in completedExamRecordList">
                  <div class="recordItem">
                    <span class="configText">
                      开考时间：
                      {{ item.startTime }}</span
                    >
                    <span class="configText">
                      交卷时间：
                      {{ item.submitTime }}</span
                    >
                    <template v-if="!noNeedExamScoreDomainList.includes(domainName)">
                      <span class="configText">
                        {{ examText }}成绩：
                        <span :class="item.isPass === '0' ? 'redText' : ''">
                          {{ item.userPaperScore }}
                        </span>
                      </span>
                      <span class="configText" v-if="!specialTenantList.includes(domainName)">
                        {{ examText }}状态：
                        <span :class="item.isPass === '0' ? 'redText' : ''">
                          {{ item.isPass === "0" ? "未通过" : "通过" }}
                          <el-icon v-if="item.isPass === '0'"><CircleCloseFilled /></el-icon>
                          <el-icon v-else><SuccessFilled /></el-icon>
                        </span>
                      </span>
                    </template>

                    <el-button type="primary" @click="pushToExamed(item)"> 查看详情 </el-button>
                  </div>
                </div>
              </div>
              <div class="examRecordNull" v-else>
                <img src="@/assets/images/no_exam_detail.png" alt="" />
                <div>暂无{{ examText }}详情</div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>
  </div>
</template>

<style scoped lang="less">
  .examInfo:hover {
    box-shadow: 0 0 10px 2px rgba(0, 0, 255, 0.5); /* 添加阴影效果 */
  }

  .examInfo {
    border: 1px solid #e6e7e9;
    position: relative;
    color: black;
    margin: 20px 0 30px 0;
    height: 210px;
    width: 100%;
    border-radius: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    transition: box-shadow 0.3s ease-in-out;

    .examInfo-title {
      font-size: 28px;
      font-weight: bold;
      margin-bottom: 20px;
      color: #1d4b88;
    }

    .examInfo-info {
      display: flex;
      align-items: center;
      width: 340px;
      justify-content: space-evenly;
      margin-bottom: 20px;
      .dark-blue-text {
        color: #1d4b88;
        font-weight: bold;
        font-size: 14px;
      }
    }

    .examInfo-time {
      margin-bottom: 20px;
    }

    .examInfo-button {
      background-color: #fae8c6;
      color: #d01f25;
      padding: 15px 100px;
      cursor: pointer;
    }

    .examInfo-end {
      margin-bottom: 20px;
      font-size: 22px;
    }
  }

  .config {
    .box-card {
      min-height: 600px;
      border-radius: 20px;

      .demo-tabs {
        :deep(.el-tabs__item) {
          font-size: 16px;
        }
        .configDetail {
          padding: 20px;
          display: flex;

          .subTitle {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            font-size: 20px;
            color: #000;
            font-weight: bold;

            .count-text {
              font-size: 16px;
              margin-left: 50px;
            }
          }

          .configText {
            line-height: 35px;
            font-size: 17px;
            font-weight: 550;
          }
          .redText {
            color: red;
          }
        }

        .examRecordNull {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 450px;
          font-size: 18px;
          color: #d7d7d7;
          font-weight: bold;
          flex-direction: column;
          > img {
            width: 250px;
            margin-bottom: 30px;
          }
        }
        .rules {
          display: flex;
          flex-direction: column;
          margin-left: 30px;
          width: 55%;
        }
        .record {
          flex: 1;

          .recordList {
            display: flex;
            flex-wrap: wrap;
            .recordItem {
              margin: 0 30px 30px 0;
              display: flex;
              justify-content: center;
              flex-direction: column;
              padding-left: 30px;
              width: 400px;
              height: 200px;
              border: 1px solid #eee;

              .el-button {
                margin: 5px 30px 5px 0;
                font-size: 16px;
              }
            }
          }
        }
      }
    }
  }
</style>
