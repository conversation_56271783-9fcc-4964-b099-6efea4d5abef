// 资料
export type MeansItemType = {
  catalogueId: number
  catalogueName: string
  manageId: number
  manageName: string
  manageAddress: string
  cover: string
  createTime: string
  createBy: string
  downloadNumber: number | null
  viewNumber: number | null
  dataUserIds: string
  manageType: string
  profile: string
  likeNumber: number | null
  likeFlag: boolean
  isDownload: string
  newUploadFlag: boolean
  newUploadCatalogueList: Array
}

export type meansCategoryItemType = {
  icon: string
  label: string
  value: number
  count: number
}

export type howSearchItemType = {
  manageName: string
}
