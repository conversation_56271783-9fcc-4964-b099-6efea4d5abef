<template>
  <div class="ad-info">
    <div class="spec">
      <div v-html="dataSource.content"></div>
    </div>
    <div class="media">
      <div class="ad-image">
        <img :src="dataSource.imgUrl" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { RightImgLeftText } from "@/types";
import type { PropType } from "vue";
defineProps({
  dataSource: {
    type: Object as PropType<RightImgLeftText>,
    default: () => ({}),
  },
});
</script>

<style scoped lang="less">
.ad-info {
  // min-height: 600px;
  background: #fff;
  display: flex;
  .media {
    width: 580px;
    height: 400px;
    padding: 30px 50px;
    .ad-image {
      width: 480px;
      height: 400px;
    }
  }
  .spec {
    flex: 1;
    padding: 30px 30px 30px 30px;
  }
}
</style>
