<!--
 * @Description: 我的问卷页面
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-22 13:27:51
 * @LastEditTime: 2024-11-29 11:33:36
-->
<script lang="ts" setup>
  import useUserStore from "@/store/modules/user"
  import { myList } from "@/api/questionnaire"
  import type { questionType } from "@/types"
  import { Search, ArrowRight } from "@element-plus/icons-vue"

  const router = useRouter()
  const userStore = useUserStore()
  const { userInfo } = storeToRefs(userStore)
  const questionList = ref<questionType[]>([])
  const pageNum = ref(1)
  const pageSize = ref(6)
  const dataTotal = ref<number | undefined>(0)
  const searchValue = ref("")
  const activeStatus = ref("")

  async function fetchMyListData() {
    const queryData = {
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      queryUserId: userInfo.value.userId,
      surveyTitle: searchValue.value,
      ...baseTypeFilterData.value,
      ...extraQueryData.value
    }

    const { rows, total } = await myList(queryData)
    questionList.value = rows
    dataTotal.value = total || 0
  }

  const extraQueryData = ref({})
  const arrowSortChange = value => {
    pageNum.value = 1
    extraQueryData.value = { ...value }
    fetchMyListData()
  }

  const baseTypeFilterData = ref({})
  const baseTypeFilterChange = value => {
    baseTypeFilterData.value = { ...value }
    fetchMyListData()
  }

  const jumpTo = (questionnaireId, questionnaireIssuedId) => {
    router.push({
      path: "/study/questionnaire/doquestionnaire",
      query: {
        questionnaireId,
        questionnaireIssuedId
      }
    })
  }

  const handleFilterChange = () => {
    pageNum.value = 1
    baseTypeFilterChange({ queryStatus: activeStatus.value })
  }

  onMounted(() => {
    fetchMyListData()
  })
</script>

<template>
  <div class="bg-white">
    <div class="p-4 border-b border-gray-200">
      <div class="flex items-center">
        <span class="text-gray-600 text-16px mr-5 text-skyblueColor font-bold">问卷状态：</span>
        <el-radio-group v-model="activeStatus" @change="handleFilterChange">
          <el-radio label="">全部</el-radio>
          <el-radio label="1">已参加</el-radio>
          <el-radio label="0">未参加</el-radio>
        </el-radio-group>
      </div>
    </div>

    <div class="flex justify-between items-center p-4 border-b-2px border-gray-200 border-b-dashed">
      <BaseArrowSort
        :data-list="[
          { name: 'create_time', label: '按时间' },
          { name: 'survey_title', label: '按名称' }
        ]"
        @sort="arrowSortChange"
      ></BaseArrowSort>
      <el-input
        v-model="searchValue"
        class="w-300px"
        :prefix-icon="Search"
        @keyup.enter="handleFilterChange"
        @clear="handleFilterChange"
        clearable
      ></el-input>
    </div>

    <div v-if="questionList.length > 0" class="p-5">
      <div
        v-for="item in questionList"
        :key="item.questionnaireIssuedId"
        class="flex items-center pb-6 mb-6 border-b border-gray-200"
      >
        <div class="w-320px h-180px overflow-hidden rounded-lg">
          <img :src="item.questionnaireCover" alt="" class="w-full h-full object-cover" />
        </div>
        <div class="flex-1 ml-6 flex flex-col justify-between h-180px">
          <div class="text-24px font-bold">{{ item.surveyTitle }}</div>
          <div class="flex justify-between items-center">
            <div class="text-16px text-gray-600 w-70%">{{ item.surveyDescription }}</div>
            <el-button
              v-if="item.queryStatus !== '1'"
              type="primary"
              @click="jumpTo(item.questionnaireId, item.questionnaireIssuedId)"
            >
              参与问卷
              <el-icon class="ml-2"><ArrowRight /></el-icon>
            </el-button>
            <el-button :disabled="true" v-else>
              已参加
              <el-icon class="ml-2"><ArrowRight /></el-icon>
            </el-button>
          </div>
          <div class="flex text-16px text-gray-600">
            <div class="mr-10">开始时间：{{ item.queryStartTime }}</div>
            <div>结束时间：{{ item.queryEndTime }}</div>
          </div>
        </div>
      </div>
    </div>
    <el-empty v-else description="暂无数据" :image-size="200"></el-empty>
    <BasePagination
      class="pagination"
      v-if="questionList.length > 0"
      :total="dataTotal!"
      v-model:pageNum="pageNum"
      v-model:pageSize="pageSize"
      @pagination="fetchMyListData"
    />
  </div>
</template>

<style lang="less" scoped>
  :deep(.el-button) {
    min-width: 120px;
  }
</style>
