import { type } from "os"

//接口返回数据的统一类型,为了后期拓展，这里将其定义为接口
export interface ApiRes<T> {
  code: number
  msg: string
  data: T
}

//单富文本显示
export type SingleRichText = {
  id: string
  title: string
  type: string
  summary: string
  createTime: string
  soruce: string
  updateTime: string
  collectNum: number
  viewNum: number
  content: string
}
//左图右文字广告
export type LeftImgRightText = {
  id: string
  title: string
  url: string
  type: string
  summary: string
  soruce: string
  createTime: string
  updateTime: string
  collectNum: number
  viewNum: number
  content: string
  imgUrl: string
}
//左文字右图广告
export type RightImgLeftText = {
  id: string
  title: string
  url: string
  type: string
  summary: string
  soruce: string
  createTime: string
  updateTime: string
  collectNum: number
  viewNum: number
  imgUrl: string
  content: string
}
