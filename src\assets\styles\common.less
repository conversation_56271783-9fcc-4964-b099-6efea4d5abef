* {
  box-sizing: border-box;
}

html {
  height: 100%;
  font-size: 14px;
  /* 隐藏滚动条 */
  ::-webkit-scrollbar {
    display: none;
  }
}

body {
  height: 100%;
  color: #333;
  min-width: 1240px;
  font: 1em/1.4 "Microsoft Yahei", "PingFang SC", "Avenir", "Segoe UI", "Hiragino Sans GB",
    "STHeiti", "Microsoft Sans Serif", "WenQuanYi Micro Hei", sans-serif;
}

ul,
h1,
h3,
h4,
p,
dl,
dd {
  padding: 0;
  margin: 0;
}

a {
  text-decoration: none;
  color: #000;
  outline: none;
}

i {
  font-style: normal;
}

input[type="text"],
input[type="search"],
input[type="password"],
input[type="checkbox"] {
  padding: 0;
  outline: none;
  border: none;
  -webkit-appearance: none;
  &::placeholder {
    color: #ccc;
  }
}

img {
  max-width: 100%;
  max-height: 100%;
  vertical-align: middle;
  //  background: #ebebeb;
}

ul {
  list-style: none;
}

#app {
  background: #fff;
  // 不能选中文字
  user-select: none;
}

.container {
  width: 1240px !important;
  margin: 0 auto;
  position: relative !important;
}

// 一行省略
.ellipsis {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
// 二行省略
.ellipsis-2 {
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.fl {
  float: left;
}

.fr {
  float: right;
}

.clearfix:after {
  content: ".";
  display: block;
  visibility: hidden;
  height: 0;
  line-height: 0;
  clear: both;
}

// 闪动画
.shan {
  &::after {
    content: "";
    position: absolute;
    animation: shan 1.5s ease 0s infinite;
    top: 0;
    width: 30%;
    height: 100%;
    background: linear-gradient(
      to left,
      rgba(255, 255, 255, 0) 0,
      rgba(255, 255, 255, 0.3) 50%,
      rgba(255, 255, 255, 0) 100%
    );
    transform: skewX(-45deg);
  }
}
@keyframes shan {
  0% {
    left: -100%;
  }
  100% {
    left: 120%;
  }
}

// 离开淡出动画
.fade {
  &-leave {
    &-active {
      position: absolute;
      width: 100%;
      transition: opacity 0.5s 0.2s;
      z-index: 1;
    }
    &-to {
      opacity: 0;
    }
  }
}

// 1. 离开，透明度  1---->0    位移 0---->30
// 2. 进入，透明度  0---->1    位移 30---->0
// 执行顺序，先离开再进入
.pop {
  &-leave {
    &-from {
      opacity: 1;
      transform: none;
    }
    &-active {
      transition: all 0.5s;
    }
    &-to {
      opacity: 0;
      transform: translateX(20px);
    }
  }
  &-enter {
    &-from {
      opacity: 0;
      transform: translateX(20px);
    }
    &-active {
      transition: all 0.5s;
    }
    &-to {
      opacity: 1;
      transform: none;
    }
  }
}

// 表单
.base-form {
  padding: 50px 0;
  &-item {
    display: flex;
    align-items: center;
    width: 700px;
    margin: 0 auto;
    padding-bottom: 25px;
    .label {
      width: 180px;
      padding-right: 10px;
      text-align: right;
      color: #999;
      ~ .field {
        margin-left: 0;
      }
    }
    .field {
      width: 320px;
      height: 50px;
      position: relative;
      margin-left: 190px;
      .icon {
        position: absolute;
        left: 0;
        top: 0;
        width: 40px;
        height: 50px;
        text-align: center;
        line-height: 50px;
        color: #999;
        ~ .input {
          padding-left: 40px;
        }
      }
      .input {
        border: 1px solid #e4e4e4;
        width: 320px;
        height: 50px;
        line-height: 50px;
        padding: 0 10px;
        &.err {
          border-color: @redColor;
        }
        &:focus,
        &:active {
          border-color: @baseColor;
        }
      }
    }
    .error {
      width: 180px;
      padding-left: 10px;
      color: @redColor;
    }
  }
  .submit {
    width: 320px;
    height: 50px;
    border-radius: 4px;
    background: @baseColor;
    height: 50px;
    line-height: 50px;
    text-align: center;
    font-size: 16px;
    color: #fff;
    display: block;
    margin: 0 auto;
  }
}

.Hzqh1 {
  font-size: 20px;
  color: #333;
  font-weight: bold;
  line-height: 43px;
  position: relative;
}

.HzqLi {
  max-width: 120px;
  margin-right: 25px;
  padding: 3px 8px;
  margin-bottom: 20px;
  cursor: pointer;
  word-break: keep-all; /* 不换行 */
  white-space: nowrap; /* 不换行 */
  overflow: hidden; /* 内容超出宽度时隐藏超出部分的内容 */
  text-overflow: ellipsis; /*溢出时显示省略标记...；需与overflow:hidden一起使用*/
}

// cover some element-plus styles
.logout-confirm-btn:active {
  scale: 1.1;
}
