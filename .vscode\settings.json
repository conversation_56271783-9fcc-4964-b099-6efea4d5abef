{
  // 格式化 typescript 和 vue 使用 prettier-vscode
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  // 在保存时运行的代码自动修复操作
  "editor.codeActionsOnSave": {
    "source.fixAll": "explicit",
  },
  // 操作时作为单词分隔符的字符
  "editor.wordSeparators": "`~!@#%^&*()=+[{]}\\|;:'\",.<>/?",
  // 一个制表符等于的空格数
  "editor.tabSize": 2,
  // 默认行尾字符 LF
  "files.eol": "\n",
  // 不已紧凑形式呈现文件夹
  "explorer.compactFolders": false,
  "prettier.semi": false,
  "editor.quickSuggestions": {
    "strings": true
  },
  "files.associations": {
    "*.vue": "vue"
  },
  "editor.inlayHints.enabled": true,
  "unocss.root": "src",
  "css.validate": false,
  "unocss.remToPxPreview": true
}
