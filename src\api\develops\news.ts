/*
 * @Description:
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON><PERSON>
 * @Date: 2023-05-05 09:43:21
 * @LastEditTime: 2023-05-22 09:22:29
 */
import request from "@/utils/request"

export function list(query: Object) {
  return request({
    url: "/devops/news/list",
    method: "get",
    params: query
  })
}

//通过ID获取课程信息
export function getListById(newsId) {
  return request({
    url: "/devops/news/" + newsId,
    method: "get"
  })
}
