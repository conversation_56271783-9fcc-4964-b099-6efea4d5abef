<script lang="ts" setup>
  import { ref } from "vue"
  import { Search } from "@element-plus/icons-vue"
  import { storeToRefs } from "pinia"
  import type { howSearchItemType } from "@/types"
  import useStore from "@/store"
  import meansCatalogue from "./components/meansCatalogue.vue"
  import meansContent from "./components/meansContent.vue"
  import meansSituation from "./components/meansSituation.vue"
  import { getPopularSearchWords, editRelateNumber } from "@/api/trainingTask/means"

  const { means } = useStore()
  const { manageName } = storeToRefs(means)

  const loadData = async () => {
    let queryParams = {
      manageName: manageName.value
    }
    means.getMeansInfo(queryParams)
  }

  const clearContent = () => {
    manageName.value = ""
    loadData()
  }

  const howSearchList = ref<howSearchItemType[]>([])
  const fetchHotSearchData = async () => {
    const { data } = await getPopularSearchWords()
    howSearchList.value = data
  }

  const filePreviewRef = ref<any>(null)
  const filePreview = async item => {
    await editRelateNumber({ manageId: item.manageId, viewNumber: 0 })
    filePreviewRef.value.fileLoad(item.manageAddress, item.manageId)
  }

  const downloadCountRecord = async manageId => {
    await editRelateNumber({ manageId, downloadNumber: 0 })
  }

  loadData()
  fetchHotSearchData()
</script>

<template>
  <div class="container">
    <div class="means-header">
      <img
        src="https://training.ehsconsulting.com.cn/resources/skins/basic/images/kng-ban.jpg"
        alt=""
      />
      <div class="hot-search">
        <span>热门搜索：</span>
        <div class="hot-search-list">
          <template v-for="item in howSearchList">
            <div class="hot-search-item" v-if="item?.manageName" @click="filePreview(item)">
              {{ item.manageName }}
            </div>
          </template>
        </div>
      </div>
      <div class="means-search">
        <el-icon class="search-icon" :size="23"><Search /></el-icon>
        <el-input
          size="large"
          v-model="manageName"
          placeholder="输入您想要查找的资料..."
          class="means-input"
          @keyup.enter="loadData"
        >
        </el-input>
        <div class="right-suffix">
          <el-icon v-if="manageName" class="clear-icon" :size="23" @click="clearContent"
            ><CircleClose
          /></el-icon>

          <el-divider direction="vertical" />

          <el-button link class="search-button" @click="loadData">搜索</el-button>
        </div>
      </div>
    </div>
    <div class="means-body">
      <el-row :gutter="10">
        <el-col :span="6">
          <meansCatalogue />
          <meansSituation />
        </el-col>
        <el-col :span="16">
          <meansContent />
        </el-col>
      </el-row>
    </div>

    <BaseFilePreview ref="filePreviewRef" @clickDownload="downloadCountRecord" />
  </div>
</template>

<style scoped lang="less">
  :deep(.el-button.is-link:focus) {
    color: #444 !important;
  }
  :deep(.el-button) {
    border: none;
  }
  :deep(.el-button:hover) {
    border: none;
    > span {
      color: @warmOrange !important;
    }
  }
  .container {
    padding: 20px 0;

    .means-header {
      position: relative;

      .hot-search {
        position: absolute;
        left: 18%;
        bottom: 50px;
        color: #fff;
        font-size: 15px;
        display: flex;
        > span {
          text-align: center;
          line-height: 26px;
        }

        .hot-search-list {
          display: flex;
          .hot-search-item {
            text-align: center;
            height: 30px;
            margin-left: 8px;
            padding: 5px 6px;
            border-radius: 4px;
            background-color: #2e68e0;
            font-size: 13px;
            color: #fff;
            text-overflow: ellipsis;
            white-space: nowrap;
            word-break: break-all;
            max-width: 200px;
            overflow: hidden;
            vertical-align: middle;
            cursor: pointer;
          }
          .hot-search-item:hover {
            background-color: #1674d9;
          }
        }
      }
      .means-search {
        display: flex;
        position: absolute;
        justify-content: center;
        align-items: center;
        left: 17%;
        bottom: -20px;
        width: 800px;
        height: 64px;
        background-color: #fff;
        box-shadow: 0 2px 10px 2px rgba(0, 0, 0, 0.2);

        border-radius: 5px;
        :deep(.el-input__wrapper) {
          box-shadow: none;
        }

        .search-icon {
          color: #c4c4c4;
          margin: 0 10px 0 20px;
        }
        .means-input {
          height: 100%;
          font-size: 16px;
        }

        .right-suffix {
          display: flex;
          justify-content: center;
          align-items: center;

          .clear-icon {
            color: #c4c4c4;
            margin: 0 10px 0 20px;
            cursor: pointer;
          }

          .search-button {
            color: #444;
            font-weight: bold;
            font-size: 16px;
            padding: 0 45px 0 30px;
          }
        }
      }
    }

    .means-body {
      margin-top: 40px;
      display: flex;
      margin-bottom: 50px;
    }
  }
</style>
