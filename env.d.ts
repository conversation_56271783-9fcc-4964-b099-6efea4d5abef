/// <reference types="vite/client" />
import { ComponentCustomProperties } from "vue"
import axios, { Axios, AxiosResponse, AxiosRequestConfig } from "axios"

declare module "axios" {
  interface AxiosResponse<T = any> {
    rows?: T
    code: number
    msg?: string
    total?: number
  }
}

declare module "@vue/runtime-core" {
  interface ComponentCustomProperties {
    useDict: any
    download: any
  }
}

// QC 类型声明 - QQ 登录模块
declare namespace QC {
  // QC.api("get_user_info").success((res: unknown) => {
  //   console.log("获取QQ用户资料", res);
  // });
  function api(s: string): {
    success: (res: unknown) => void
  }
  const Login: {
    // QC.Login.check()
    check: () => boolean
    // QC.Login.getMe((openId) => {
    //   console.log("获取QQ用户openId", openId);
    // });
    getMe: (callback: (openId: string) => void) => void
  }
}

declare module "*.vue" {
  import type { DefineComponent } from "vue"
  const component: DefineComponent<{}, {}, any>
  export default component
}

/// <reference types="@unocss/runtime" />
