<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    :close-on-click-modal="false"
    center
    width="1000px"
  >
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      :rules="dataFormRules"
      label-width="130px"
      @keyup.enter="submitHandle()"
    >
      <el-form-item label="企业名称" prop="enterpriseName">
        <el-input placeholder="请输入企业名称" v-model="dataForm.enterpriseName" />
      </el-form-item>
      <el-form-item label="联系人" prop="contactName">
        <el-input placeholder="请输入联系人" v-model="dataForm.contactName" />
      </el-form-item>
      <el-form-item label="职务" prop="position">
        <el-input placeholder="请输入职务" v-model="dataForm.position" />
      </el-form-item>
      <el-form-item label="电话" prop="phone">
        <el-input placeholder="请输入电话" v-model="dataForm.phone" />
      </el-form-item>
      <el-form-item label="地址" prop="address">
        <el-input placeholder="请输入地址" v-model="dataForm.address" />
      </el-form-item>
      <el-form-item label="企业简介" prop="enterpriseIntro">
        <el-input
          type="textarea"
          :rows="4"
          placeholder="请输入企业简介"
          v-model="dataForm.enterpriseIntro"
        />
      </el-form-item>

      <el-divider content-position="center">产品信息</el-divider>

      <!-- 产品列表 -->
      <div class="product-list">
        <div
          v-for="(product, index) in dataForm.productList"
          :key="index"
          class="product-item mb-16px p-16px border border-solid border-gray-200 rounded-8px"
        >
          <div class="flex justify-between mb-10px">
            <div class="text-16px font-bold">产品 {{ index + 1 }}</div>
            <el-button type="danger" link @click="removeProduct(index)" :icon="Delete"
              >删除</el-button
            >
          </div>
          <el-form-item
            :label="'产品名称'"
            :prop="`productList.${index}.productName`"
            :rules="[{ required: true, message: '产品名称不能为空', trigger: 'blur' }]"
          >
            <el-input v-model="product.productName" placeholder="请输入产品/服务名称" />
          </el-form-item>
          <el-form-item
            :label="'产品简介及价格'"
            :prop="`productList.${index}.productIntro`"
            :rules="[{ required: true, message: '产品简介不能为空', trigger: 'blur' }]"
          >
            <el-input
              type="textarea"
              :rows="3"
              v-model="product.productIntro"
              placeholder="请输入产品/服务简介及价格"
            />
          </el-form-item>
          <el-form-item :label="'会员优惠说明'" :prop="`productList.${index}.discountInfo`">
            <el-input
              v-model="product.discountInfo"
              placeholder="请输入对促进会会员优惠/免费说明"
            />
          </el-form-item>
        </div>

        <div class="flex justify-center mt-16px">
          <el-button type="primary" plain @click="addProduct" :icon="Plus">添加产品</el-button>
        </div>
      </div>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitHandle()" :loading="submitLoading">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
  import { ref, getCurrentInstance } from "vue"
  import { addEnterprise } from "@/api/system/enterprise"
  import { Delete, Plus } from "@element-plus/icons-vue"
  import { ElMessage } from "element-plus"

  const emit = defineEmits(["refreshDataList"])
  const { proxy } = getCurrentInstance()

  const visible = ref(false)
  const dataFormRef = ref()
  const submitLoading = ref(false)
  const dataForm = ref({
    enterpriseName: "",
    contactName: "",
    position: "",
    phone: "",
    address: "",
    enterpriseIntro: "",
    productList: []
  })
  const dialogTitle = ref("注册企业")

  // 表单验证规则
  const dataFormRules = ref({
    enterpriseName: [{ required: true, message: "企业名称不能为空", trigger: "blur" }],
    contactName: [{ required: true, message: "联系人不能为空", trigger: "blur" }],
    phone: [{ required: true, message: "电话不能为空", trigger: "blur" }],
    address: [{ required: true, message: "地址不能为空", trigger: "blur" }],
    enterpriseIntro: [{ required: true, message: "企业简介不能为空", trigger: "blur" }]
  })

  // 打开弹窗
  const open = () => {
    // 重置表单数据
    dataForm.value = {
      enterpriseName: "",
      contactName: "",
      position: "",
      phone: "",
      address: "",
      enterpriseIntro: "",
      productList: []
    }

    // 默认添加一个空产品
    if (dataForm.value.productList.length === 0) {
      addProduct()
    }

    visible.value = true
  }

  // 添加产品
  const addProduct = () => {
    dataForm.value.productList.push({
      productName: "",
      productIntro: "",
      discountInfo: ""
    })
  }

  // 移除产品
  const removeProduct = index => {
    dataForm.value.productList.splice(index, 1)
  }

  // 表单提交
  const submitHandle = () => {
    dataFormRef.value.validate(async valid => {
      if (!valid) {
        return false
      }

      if (dataForm.value.productList.length === 0) {
        ElMessage.warning("请至少添加一个产品")
        return
      }

      submitLoading.value = true
      try {
        const res = await addEnterprise(dataForm.value)
        if (res.code === 200) {
          ElMessage.success("您的注册信息已经提交审核！审核通过后会显示在首页。")
          visible.value = false
          emit("refreshDataList")
        } else {
          ElMessage.error(res.msg || "企业注册失败")
        }
      } catch (error) {
        console.error("企业注册失败:", error)
        ElMessage.error("企业注册失败")
      } finally {
        submitLoading.value = false
      }
    })
  }

  defineExpose({
    open
  })
</script>

<style scoped>
  .product-item {
    background-color: #f8f9fa;
  }
</style>
