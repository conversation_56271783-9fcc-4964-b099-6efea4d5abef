/*
 * @Description:
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-17 08:49:10
 * @LastEditTime: 2025-06-06 14:33:51
 */
import { createRouter, createWebHashHistory, type RouteRecordRaw } from "vue-router"
import useTenantStore from "@/store/modules/tenant"

// 指定 RouteRecordRaw[] 类型后，书写的时候就有 TS 的类型提示和检查了
const routes: RouteRecordRaw[] = [
  {
    path: "/",
    redirect: "/home",
    component: () => import("@/views/Layout/index.vue"),
    children: [
      { path: "/home", component: () => import("@/views/Home/index.vue") },
      {
        path: "/study",
        redirect: () => {
          const tenantStore = useTenantStore()
          const domainName = tenantStore.domainName
          if (domainName === "yygf") {
            return "/study/onGoingTask"
          }
          return "/study/todo"
        },
        component: () => import("@/views/Study/index.vue"),
        children: [
          {
            path: "myInfo",
            component: () => import("@/views/Study/components/myInfo/index.vue")
          },
          {
            path: "todo",
            component: () => import("@/views/Study/components/todo/index.vue")
          },
          {
            path: "course",
            component: () => import("@/views/Study/components/course/index.vue")
          },
          {
            path: "myCourse",
            component: () => import("@/views/Study/components/course/index.vue")
          },
          {
            path: "exam",
            component: () => import("@/views/Study/components/exam/index.vue")
          },
          {
            path: "favor",
            component: () => import("@/views/Study/components/favor/index.vue")
          },
          {
            path: "resource",
            component: () => import("@/views/Study/components/resource/index.vue")
          },
          {
            path: "questionnaire",
            component: () => import("@/views/Study/components/questionnaire/index.vue")
          },
          {
            path: "locus",
            component: () => import("@/views/Study/components/locus/index.vue")
          },
          {
            path: "honor",
            component: () => import("@/views/Study/components/honor/index.vue")
          },
          {
            path: "needTask",
            component: () => import("@/views/Study/components/trainingTask/needTask/index.vue")
          },
          {
            path: "onGoingTask",
            component: () => import("@/views/Study/components/trainingTask/index.vue"),
            props: route => ({ status: "ongoing" })
          },
          {
            path: "noStartTask",
            component: () => import("@/views/Study/components/trainingTask/index.vue"),
            props: route => ({ status: "nostart" })
          },
          {
            path: "myTask",
            component: () => import("@/views/Study/components/trainingTask/index.vue"),
            props: route => ({ status: "all" })
          },
          {
            path: "completed",
            component: () => import("@/views/Study/components/trainingTask/completed/index.vue")
          },
          {
            path: "expiredTask",
            component: () => import("@/views/Study/components/trainingTask/expired/index.vue")
          },
          {
            path: "expired",
            component: () => import("@/views/Study/components/trainingTask/expired/index.vue")
          },
          {
            path: "trailer",
            component: () => import("@/views/Study/components/trailer/index.vue")
          }
        ]
      },
      {
        path: "prepare",
        component: () => import("@/views/Study/components/exam/components/prepare.vue")
      },
      {
        path: "townReview",
        component: () => import("@/views/Study/components/exam/components/town-review.vue")
      },
      {
        path: "examing",
        component: () => import("@/views/Study/components/exam/components/examing.vue")
      },
      {
        path: "template",
        component: () => import("@/views/Study/components/honor/components/template.vue")
      },
      {
        path: "certDetail",
        component: () => import("@/views/Study/components/honor/certDetail.vue")
      },
      {
        path: "/examed",
        component: () => import("@/views/Study/components/exam/components/examed.vue")
      },
      {
        path: "/means",
        component: () => import("@/views/Means/index.vue")
      },
      {
        path: "/jinwaitan-exam",
        component: () => import("@/views/Home/jinwaitan/jinwaitan-exam.vue")
      },
      {
        path: "/study/questionnaire/doquestionnaire",
        component: () => import("@/views/Study/components/questionnaire/doQuestionnaire/index.vue")
      },
      {
        path: "/course",
        component: () => import("@/views/Course/index.vue")
      },
      {
        path: "/game",
        component: () => import("@/views/Game/index.vue")
      },
      {
        path: "/game/detail",
        component: () => import("@/views/Game/components/game-detail.vue")
      },
      {
        path: "/disasterCourse",
        component: () => import("@/views/Course/disasterCourse.vue")
      },
      {
        path: "/safetyCourse",
        component: () => import("@/views/Course/safetyCourse.vue")
      },
      {
        path: "/jdCourse",
        component: () => import("@/views/Course/jdCourse.vue")
      },
      {
        path: "/course/detail",
        component: () => import("@/views/Course/components/course-detail.vue")
      },
      {
        path: "/study/project/detail",
        component: () => import("@/views/Study/components/trainingTask/project-detail/index.vue")
      },
      {
        path: "/news",
        component: () => import("@/views/News/index.vue")
      },
      {
        path: "/news/detail",
        component: () => import("@/views/News/components/news-detail.vue")
      },
      {
        path: "/link",
        component: () => import("@/views/Link/index.vue"),
        children: [
          {
            path: "",
            component: () => import("@/views/Link/components/link-about.vue")
          },
          {
            path: "entCulture",
            component: () => import("@/views/Link/components/link-ent-culture.vue")
          },
          {
            path: "honor",
            component: () => import("@/views/Link/components/link-honor.vue")
          },
          {
            path: "leaveMsg",
            component: () => import("@/views/Link/components/link-leave-msg.vue")
          },
          {
            path: "recruit",
            component: () => import("@/views/Link/components/link-recruit.vue")
          },
          {
            path: "way",
            component: () => import("@/views/Link/components/link-way.vue")
          }
        ]
      }
    ]
  },
  {
    path: "/pthtg-login",
    component: () => import("@/views/Home/pthtg/pthtg-login.vue")
  }
]

// 创建路由实例
const router = createRouter({
  // 创建 hash 路由模式
  history: createWebHashHistory(),
  // 路由规则
  routes,
  scrollBehavior: () => {
    // 始终滚动到顶部
    return { top: 0 }
  }
})

export default router
