<!--
 * @Description: 考试
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-22 09:44:28
 * @LastEditTime: 2025-06-10 09:46:22
-->
<script setup lang="ts">
  import signDialog from "./signDialog.vue"
  import { completeEachQuestion } from "@/api/onlineExam"
  import { useRoute, useRouter } from "vue-router"
  import type {
    paperQuestionType,
    paperTacticsType,
    ExamQuestionsType,
    ExamQuestionItem
  } from "@/types"
  import { startTheExam, submitAnswerPaper } from "@/api/onlineExam"
  import { ElMessage, ElMessageBox } from "element-plus"
  import type { Action } from "element-plus"
  import useTenantStore from "@/store/modules/tenant"

  const { domainName } = storeToRefs(useTenantStore())
  const { proxy } = getCurrentInstance()!
  const { exam_question_type } = proxy?.useDict("exam_question_type")!
  const route = useRoute()
  const router = useRouter()
  const {
    baseId,
    arrangeId,
    taskId,
    examDurationLimit,
    baseName,
    switchingTimes,
    submintMinimumNumber,
    faceCapture,
    recordCredit
  } = route.query
  const paperQuestionInfo = ref<paperQuestionType>()
  const paperTacticsList = ref<paperTacticsType>()
  const examQuestionList = ref<ExamQuestionsType>([])
  const timeLeft = ref()
  const showFaceVerify = ref(false)
  const canStartExam = ref(false)
  const taskMonitorId = ref<number>()

  const loadData = async () => {
    const requestData = {
      baseId,
      arrangeId,
      taskId,
      taskMonitorId: taskMonitorId.value
    }
    const res = await startTheExam(requestData)
    if (res.code === 200) {
      paperQuestionInfo.value = res.data
      timeLeft.value = Number(res.data.remainingTime)
      paperTacticsList.value = res.data.paperTactics
      paperTacticsList.value?.forEach(item => {
        item?.examQuestions.forEach((question: ExamQuestionItem) => {
          examQuestionList.value.push(question)
        })
      })
      examQuestionList.value.forEach(item => {
        // 第一次进入考试
        if (!item.userAnswer) {
          if (item.questionType === "S") {
            item.userAnswer = ""
          } else if (item.questionType === "M" || item.questionType === "F") {
            item.userAnswer = []
          } else if (item.questionType === "Q") {
            item.userAnswer = ""
          }
          // 已经做过题 再次进入考试
        } else {
          if (item.questionType === "F") {
            item.userAnswer = item.userAnswer?.split("::")
            for (let i = 0; i < item.userAnswer.length; i++) {
              item[`blank${i + 1}`] = item.userAnswer[i]
            }
          } else if (item.questionType === "M") {
            item.userAnswer = item.userAnswer?.split(",")
          }
          // 简答题已经是字符串格式，不需要额外处理
        }
      })

      // 倒计时
      if (examDurationLimit != "0") {
        timer.value = setInterval(countDown, 1000)
      }
    } else {
      router.go(-1)
    }
  }

  // 选择题选中
  const choiceSelected = async (question: ExamQuestionItem, index: number, choice: string) => {
    if (question.questionType === "S" || question.questionType === "J") {
      examQuestionList.value[index].userAnswer = ""
      examQuestionList.value[index].userAnswer = choice
    } else if (question.questionType === "M") {
      if (examQuestionList.value[index].userAnswer.indexOf(choice) !== -1) {
        examQuestionList.value[index].userAnswer.splice(
          examQuestionList.value[index].userAnswer.indexOf(choice),
          1
        )
      } else {
        examQuestionList.value[index].userAnswer.push(choice)
      }
    }
    let requestData = {
      questionId: question.questionId,
      arrangeId,
      userAnswer: Array.isArray(examQuestionList.value[index].userAnswer)
        ? examQuestionList.value[index].userAnswer.sort().join(",")
        : examQuestionList.value[index].userAnswer,
      paperAnswerId: paperQuestionInfo.value?.paperAnswerId
    }
    await completeEachQuestion(requestData)
  }

  // 倒计时
  let timer = ref()
  const countDown = () => {
    if (timeLeft.value > 0) {
      timeLeft.value--
    } else {
      // 倒计时结束后的处理
      clearInterval(timer.value)
    }
  }

  const examText = computed(() => {
    return domainName.value === "unep" ? "测试" : "考试"
  })
  watch(timeLeft, (newValue, oldValue) => {
    // 倒计时结束后的处理
    if (examDurationLimit != "0" && newValue <= 0) {
      clearInterval(timer.value)
      ElMessageBox.alert(`${examText.value}时间耗尽，已自动交卷`, "考试结束", {
        type: "warning",
        center: true,
        confirmButtonText: "OK",
        callback: async () => {
          realSubmitOperation()
        }
      })
    }
  })

  // 倒计时格式化
  const countDownTime = () => {
    if (!timeLeft.value) return "00:00:00"
    const hours = Math.floor(timeLeft.value / 3600)
    const minutes = Math.floor((timeLeft.value % 3600) / 60)
    const seconds = timeLeft.value % 60
    return `${hours.toString().padStart(2, "0")}:
  ${minutes.toString().padStart(2, "0")}:
  ${seconds.toString().padStart(2, "0")}`
  }

  // 修改滚动方法,调整偏移量
  const scrollToQuestion = (index: number) => {
    const questionEl = proxy?.$refs[`questionBox${index}`]![0]
    if (questionEl) {
      const headerHeight = 70 // 顶部固定导航的高度
      const offset = 20 // 额外的偏移量,可以根据需要调整
      const top = questionEl.offsetTop - headerHeight - offset
      window.scrollTo({ top, behavior: "smooth" })
    }
  }

  const signDialogRef = ref()
  // 手动点击提交试卷按钮触发
  const handleSubmit = async () => {
    // 所有试题答案非空判断
    // try {
    //   examQuestionList.value.forEach(item => {
    //     if (!item.userAnswer) {
    //       throw new Error()
    //     }
    //   })
    // } catch (error) {
    //   ElMessage({
    //     message: "请确保所有题目已完成作答后再进行交卷",
    //     type: "warning"
    //   })
    //   return
    // }

    // 是否有交卷控制
    if (submintMinimumNumber) {
      if (Number(examDurationLimit) * 60 - timeLeft.value < Number(submintMinimumNumber) * 60) {
        ElMessage({
          type: "warning",
          message: `开考时间少于${submintMinimumNumber}分钟禁止交卷!`
        })
        return
      }
    }

    // 确定提交弹窗
    ElMessageBox.alert("是否确定提交试卷?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      confirmButtonClass: "logout-confirm-btn",
      type: "warning"
    })
      .then(() => {
        if (paperQuestionInfo.value?.signFlag === "1") {
          signDialogRef.value.openDialog()
        } else {
          realSubmitOperation()
        }
      })
      .catch(() => {})
  }

  const realSubmitOperation = async (signUrl?) => {
    let requestData = {
      paperAnswerId: paperQuestionInfo.value?.paperAnswerId,
      frequency: screenCount.value,
      signature: signUrl
    }
    const { code, data } = await submitAnswerPaper(requestData)
    if (code !== 200) {
      return ElMessage({
        type: "error",
        message: `${examText.value}提交失败，请稍后再试`
      })
    }
    if (domainName.value === "hb") {
      ElMessageBox.alert(`您的考试分数为：${data}分`, "提交成功", {
        showConfirmButton: false,
        showCancelButton: false,
        showClose: false,
        type: "success",
        center: true,
        customStyle: {
          fontSize: "28px"
        }
      })
    } else {
      router.push({
        path: "/prepare",
        query: {
          baseId,
          arrangeId
        }
      })
    }
    ElMessage({
      type: "success",
      message: `试卷已提交，${examText.value}结束`
    })
  }

  // 切屏次数逻辑
  const screenCount = ref(0)
  const isLimitReached = ref(false)

  // 用户切屏监听事件
  const switchTimeStatistics = () => {
    if (screenCount.value < Number(switchingTimes)) {
      screenCount.value++
      ElMessageBox.alert(
        `当前已切屏${screenCount.value}次，切屏次数超过${switchingTimes}将自动提交试卷`,
        "警告",
        {
          confirmButtonText: "已知晓",
          type: "warning",
          center: true,
          showCancelButton: false
        }
      )
    } else {
      screenCount.value++
      isLimitReached.value = true
      ElMessageBox.alert("当前已超过切屏次数上限，系统已为您自动提交试卷", "通知", {
        confirmButtonText: "确定",
        center: true,
        callback: (action: Action) => {
          realSubmitOperation()
        }
      })
      window.removeEventListener("blur", switchTimeStatistics)
    }
  }

  // 填空题填写完成后
  const writeBlank = async (item, choice, blank) => {
    if (item.userAnswer === "") {
      item.userAnswer = []
    }
    item.userAnswer[choice - 1] = blank
    let isEmpty = item.userAnswer.every(empty => {
      return empty === ""
    })
    if (isEmpty) {
      item.userAnswer = ""
    }
    let requestData = {
      questionId: item.questionId,
      arrangeId,
      userAnswer: Array.isArray(item.userAnswer) ? item.userAnswer.join("::") : item.userAnswer,
      paperAnswerId: paperQuestionInfo.value?.paperAnswerId
    }
    await completeEachQuestion(requestData)
  }

  // 简答题填写完成后
  const writeEssay = async (item, answer) => {
    item.userAnswer = answer
    let requestData = {
      questionId: item.questionId,
      arrangeId,
      userAnswer: item.userAnswer,
      paperAnswerId: paperQuestionInfo.value?.paperAnswerId
    }
    await completeEachQuestion(requestData)
  }

  const handleFaceVerify = () => {
    if (faceCapture === "1") {
      showFaceVerify.value = true
    } else {
      canStartExam.value = true
      loadData()
    }
  }

  const handleVerifySuccess = (monitorId: number) => {
    taskMonitorId.value = monitorId
    canStartExam.value = true
    loadData()
  }

  const handleVerifyFail = () => {
    canStartExam.value = true
    loadData()
  }

  onMounted(() => {
    handleFaceVerify()
    // 如果有切屏限制
    if (switchingTimes) {
      if (Number(switchingTimes) === 0) {
        ElMessageBox.alert(`当前考试不允许切屏`, "警告", {
          confirmButtonText: "已知晓",
          type: "warning",
          center: true,
          showCancelButton: false
        })
      }
      watchEffect(() => {
        // 监听用户的行为，每当用户进行一次切屏行为时，将切屏次数加 1
        window.addEventListener("blur", switchTimeStatistics)
      })
    }
  })

  onBeforeUnmount(() => {
    window.removeEventListener("blur", switchTimeStatistics)
  })
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 固定顶部导航 -->
    <div
      class="fixed top-0 left-0 right-0 h-70px bg-[#f4fafe] flex justify-between items-center px-[20%] z-50"
    >
      <div class="text-18px">
        <span class="text-20px font-bold">{{ baseName }}</span>
      </div>
      <div class="flex items-center justify-end gap-8 text-16px text-gray-600 w-3/5">
        <span v-if="switchingTimes">已切屏 {{ screenCount }} 次</span>
        <span v-if="examDurationLimit !== '0'">倒计时：{{ countDownTime() }}</span>
        <span v-else>不限时</span>
        <el-button size="large" type="primary" @click="handleSubmit">立即交卷</el-button>
      </div>
    </div>

    <!-- 只在通过人脸识别后显示考试内容 -->
    <template v-if="canStartExam">
      <div class="flex pt-70px">
        <!-- 左侧题号导航 -->
        <div class="fixed top-90px left-80px w-300px bg-white rounded-10px shadow-md">
          <div class="grid grid-cols-5 gap-5 p-30px">
            <div
              v-for="(item, index) in examQuestionList"
              :key="index"
              @click="scrollToQuestion(index)"
              :class="[
                'w-30px h-30px rounded-full flex items-center justify-center cursor-pointer transition-all',
                (item.userAnswer && (Array.isArray(item.userAnswer) ? item.userAnswer.length !== 0 : item.userAnswer.trim() !== ''))
                  ? 'bg-[#f58335] text-white'
                  : 'bg-gray-400 text-white hover:bg-gray-500'
              ]"
            >
              {{ index + 1 }}
            </div>
          </div>
        </div>

        <!-- 右侧试题内容 -->
        <div class="ml-400px mr-100px flex-1 mt-20px">
          <div
            v-for="(item, index) in examQuestionList"
            :key="index"
            :ref="`questionBox${index}`"
            class="bg-white rounded-10px p-30px mb-20px shadow-sm"
          >
            <div class="flex items-center text-20px mb-8px">
              {{ index + 1 }}.
              <dict-tag :options="exam_question_type" :value="item.questionType" class="ml-2" />
            </div>

            <div class="text-16px mb-20px">{{ item.questionName }}</div>

            <!-- 选择题选项 -->
            <div v-if="item.questionType === 'S' || item.questionType === 'M' || item.questionType === 'J'" class="space-y-4">
              <template v-for="choice in 11" :key="choice">
                <div
                  v-if="item[`item${choice}`]"
                  @click="choiceSelected(item, index, String.fromCharCode(64 + choice))"
                  class="flex items-center cursor-pointer group"
                >
                  <div
                    :class="[
                      'w-24px h-24px rounded-full border flex items-center justify-center mr-10px transition-colors',
                      (item.questionType === 'S' || item.questionType === 'J') &&
                      String.fromCharCode(64 + choice) === item.userAnswer
                        ? 'bg-[#f58335] text-white border-[#f58335]'
                        : item.userAnswer?.includes(String.fromCharCode(64 + choice))
                        ? 'bg-[#f58335] text-white border-[#f58335]'
                        : 'border-gray-300 group-hover:border-[#f58335]'
                    ]"
                  >
                    {{ String.fromCharCode(64 + choice) }}
                  </div>
                  <div class="text-16px group-hover:text-[#f58335]">{{
                    item[`item${choice}`]
                  }}</div>
                </div>
              </template>
            </div>

            <!-- 填空题 -->
            <div v-else-if="item.questionType === 'F'" class="space-y-6">
              <div
                v-for="blankChoice in item.blankCount"
                :key="blankChoice"
                class="flex items-center"
              >
                <div class="w-90px text-16px">填空{{ blankChoice }}：</div>
                <el-input
                  v-model.trim="item[`blank${blankChoice}`]"
                  @change="writeBlank(item, blankChoice, item[`blank${blankChoice}`])"
                  class="w-300px"
                />
              </div>
            </div>

            <!-- 简答题 -->
            <div v-else-if="item.questionType === 'Q'" class="space-y-4">
              <div class="text-16px text-gray-600 mb-2">请在下方文本框中输入您的答案：</div>
              <el-input
                v-model="item.userAnswer"
                @blur="writeEssay(item, item.userAnswer)"
                type="textarea"
                :rows="6"
                placeholder="请输入您的答案..."
                class="w-full"
                resize="vertical"
              />
            </div>
          </div>
        </div>
      </div>
    </template>

    <signDialog ref="signDialogRef" @submitSign="realSubmitOperation" />

    <!-- 添加人脸识别组件 -->
    <BaseFaceVerification
      v-model:visible="showFaceVerify"
      :record-credit="recordCredit"
      @verify-success="handleVerifySuccess"
      @verify-fail="handleVerifyFail"
      task-type="exam"
      :task-id="taskId"
      :sub-task-id="baseId"
      :sub-task-name="baseName"
      :arrange-id="arrangeId"
    />
  </div>
</template>
