/*
 * @Description:
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-07-13 11:18:41
 * @LastEditTime: 2024-11-29 15:28:08
 */
import request from "@/utils/request"

// 查询证书列表
export function templateList(params?) {
  return request({
    url: "/devops/template/myList",
    method: "get",
    params
  })
}

// 查询证书详情
export function templateDetail(params) {
  if (!params.certType) {
    // 无证书类型时
    return request({
      url: `/devops/template/getTemplateData/${params.certTemplateId}`,
      method: "get"
    })
  } else {
    // 有证书类型时
    return request({
      url: `/devops/template/getTemplateData/${params.certTemplateId}/${params.certType}`,
      method: "get"
    })
  }
}

// 证书图片
// body对象里面需要：
// certDataList(/getTemplateData/2023/c返回的数组)
// studentUserId
// taskId
export function getCertPicture(templateImgUrl, data) {
  return request({
    url: `/course/record/getCertPicture?templateImgUrl=${templateImgUrl}`,
    method: "post",
    data
  })
}
