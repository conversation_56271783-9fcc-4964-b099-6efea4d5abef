<script setup lang="ts">
  import { ElMessageBox } from "element-plus"
  import useStore from "@/store"
  import QrcodeVue from "qrcode.vue"
  import { pushToAdmin } from "@/utils/common"
  import router from "@/router"
  //解构获取大仓库函数返回值里的各个小仓库实例对象
  const { user } = useStore()
  const { name, avatar, token, roles } = storeToRefs(user)

  // 判断是否是管理员角色
  let isAdmin = computed(() => {
    let hasAdminRole = false
    if (roles.value.length > 0) {
      roles.value.forEach(item => {
        if (item.includes("admin")) {
          hasAdminRole = true
        }
      })
    }
    return hasAdminRole
  })

  // 判断是否有退出登录按钮
  let canExit = computed(() => {
    return !localStorage.getItem("domainName")
  })

  const toHome = () => {
    router.push({
      path: "/"
    })
  }

  const handleCommand = command => {
    switch (command) {
      case "pushAdmin":
        pushToAdmin("/index")
        break
      case "logout":
        logout()
        break
      default:
        break
    }
  }

  const mobileEntryUrl = computed(() => {
    // 获取当前域名并替换
    let host = window.location.hostname.replace(/edu(.*?)user/, "edu$1app")
    // 拼接协议、host、路径和参数
    return `${window.location.protocol}//${host}${window.location.pathname}${window.location.search}`
  })

  const logout = () => {
    ElMessageBox.alert("确定退出系统吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      confirmButtonClass: "logout-confirm-btn",
      type: "warning"
    })
      .then(() => {
        user.logOutAction().then(() => {
          location.href = "/index"
        })
      })
      .catch(() => {})
  }

  let rotated = ref(false)
  const handleVisibleChange = val => {
    rotated.value = val
  }
</script>

<template>
  <nav class="app-topnav">
    <div class="container">
      <!-- 左侧Logo区域 -->
      <div class="logo-section">
        <h1 class="logo" @click="toHome">
          <div class="home-title">
            <span>安全发展&防灾减灾学习平台</span>
          </div>
        </h1>
      </div>

      <!-- 右侧用户区域 -->
      <div class="right">
        <el-popover placement="bottom" trigger="hover" width="180">
          <template #reference>
            <div class="mobile-entry flex items-center cursor-pointer">
              <el-icon :size="20" color="#67C23A"><Iphone /></el-icon>
              <span class="text-sm font-medium text-gray-700">手机端入口</span>
            </div>
          </template>
          <div class="flex flex-col items-center p-4 rounded-md shadow-md bg-white">
            <div class="qr-code-wrapper">
              <qrcode-vue :value="mobileEntryUrl" :size="120" />
            </div>
            <p class="text-xs text-gray-500 mt-2">扫码访问手机端</p>
          </div>
        </el-popover>
        <div class="avatar-container">
          <el-dropdown
            @visible-change="handleVisibleChange"
            @command="handleCommand"
            trigger="hover"
            :disabled="!isAdmin && !canExit"
          >
            <div class="avatar-wrapper">
              <img :src="avatar" class="user-avatar" />
              <div class="nickname">{{ name }}</div>
              <i
                v-if="isAdmin || canExit"
                class="iconfont icon-xiangxia"
                :class="{ rotate: rotated, rotateBack: !rotated }"
              ></i>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="pushAdmin" v-if="isAdmin">
                  <span>管理员专区</span>
                </el-dropdown-item>
                <el-dropdown-item command="logout" v-if="canExit">
                  <span>退出登录</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>
  </nav>
</template>

<style scoped lang="less">
  .mobile-entry {
    padding: 8px 12px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
  }

  .mobile-entry:hover {
    background-color: rgba(0, 0, 0, 0.05); /* 轻微的背景色变化 */
  }

  .qr-code-wrapper {
    border: 1px solid #eee;
    padding: 4px;
    border-radius: 4px;
  }
  .app-topnav {
    background: #fff;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 100;
    box-shadow: 0 2px 15px 0 hsla(0, 0%, 45%, 0.2);
    height: 70px;
    display: flex;
    justify-content: center;
    align-items: center;
    .container {
      width: 100%;
      max-width: 1240px;
      padding: 0 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .logo-section {
        min-width: 280px;
        flex-shrink: 0;
        display: flex;
        align-items: center;
      }

      .logo {
        cursor: pointer;
        display: flex;
        align-items: center;
        a {
          height: 55px;
          width: 35%;
          text-indent: -9999px;
          // background: url("https://beckwelldb.obs.cn-east-3.myhuaweicloud.com/logo2.png") no-repeat
          //   center center / contain;
          background-repeat: no-repeat;
          background-position: center center;
          background-size: contain;
        }

        .title {
          font-size: 20px;
          font-weight: bold;
          color: @deepColor;
          font-family: fangsong;
        }

        .home-title {
          margin-left: 8px;
          font-size: 18px;
          font-weight: bold;
          color: @deepColor;
          font-family: fangsong;
          white-space: nowrap;
        }
      }

      .nav-center {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .search-bar {
        width: 320px;
        margin: 0 60px 0 70px;
      }

      .right {
        display: flex;
        align-items: center;
        text-align: center;
        color: #6b7280;
        font-size: 16px;
        cursor: pointer;
        flex-shrink: 0;
        gap: 10px;
        .avatar-container {
          display: flex;
          height: 40px;
          justify-content: center;
          align-items: center;

          .avatar-wrapper {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;

            .user-avatar {
              cursor: pointer;
              width: 40px;
              height: 40px;
              border-radius: 50%;
              margin-right: 3px;
            }

            .icon-xiangxia {
              cursor: pointer;
              // position: absolute;
              // right: -30px;
              // top: 10px;
              // font-size: 18px;
            }

            .rotate {
              transform: rotateZ(180deg);
              transition: all 0.3s;
              transform-origin: center;
            }

            .rotateBack {
              transform-origin: center;
              transform: rotateZ(0deg);
              transition: all 0.3s;
            }
          }

          .nickname {
            margin-left: 8px;
            margin-right: 8px;
            font-weight: bold;
          }
        }
      }
    }
  }
  :deep(.el-input__wrapper) {
    border-radius: 30px;
  }

  // 响应式适配
  @media screen and (max-width: 1600px) {
    .container {
      padding: 0 30px;
    }
    .logo-section {
      min-width: 260px;
    }
    .logo .home-title {
      font-size: 16px;
    }
  }

  @media screen and (max-width: 1440px) {
    .container {
      padding: 0 20px;
    }
    .logo-section {
      min-width: 240px;
    }
    .logo .home-title {
      font-size: 15px;
    }
  }

  @media screen and (max-width: 1200px) {
    .container {
      padding: 0 15px;
    }
    .logo-section {
      min-width: 200px;
    }
    .logo .home-title {
      font-size: 14px;
    }
    .right {
      font-size: 14px;
      gap: 8px;
      .nickname {
        display: none;
      }
    }
  }
</style>
