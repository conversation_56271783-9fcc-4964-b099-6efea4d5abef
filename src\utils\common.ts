/*
 * @Description: 通用js方法封装处理
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-17 08:49:10
 * @LastEditTime: 2025-02-25 13:03:28
 */
import { getToken } from "@/utils/auth"
import { productionEnvList } from "@/utils/constant"
import useTenantStore from "@/store/modules/tenant"

const env = import.meta.env.VITE_APP_ENV
const loginPageUrl = import.meta.env.VITE_APP_LOGIN_URL
const redirectUrl = import.meta.env.VITE_APP_REDIRECT_URL
/**
 * 参数处理
 * @param {*} params  参数
 */
export function tansParams(params) {
  let result = ""
  for (const propName of Object.keys(params)) {
    const value = params[propName]
    var part = encodeURIComponent(propName) + "="
    if (value !== null && value !== "" && typeof value !== "undefined") {
      if (typeof value === "object") {
        for (const key of Object.keys(value)) {
          if (value[key] !== null && value[key] !== "" && typeof value[key] !== "undefined") {
            let params = propName + "[" + key + "]"
            var subPart = encodeURIComponent(params) + "="
            result += subPart + encodeURIComponent(value[key]) + "&"
          }
        }
      } else {
        result += part + encodeURIComponent(value) + "&"
      }
    }
  }
  return result
}

// 返回项目路径
export function getNormalPath(p) {
  if (p.length === 0 || !p || p == "undefined") {
    return p
  }
  let res = p.replace("//", "/")
  if (res[res.length - 1] === "/") {
    return res.slice(0, res.length - 1)
  }
  return res
}

// 验证是否为blob格式
export async function blobValidate(data) {
  try {
    const text = await data.text()
    JSON.parse(text)
    return false
  } catch (error) {
    return true
  }
}

// 判断当前时间是否在指定时间区间内
export function isInTimeRange(start, end) {
  const now = new Date() // 获取当前时间
  const startTime = new Date(start) // 开始时间
  const endTime = new Date(end) // 结束时间
  return now >= startTime && now <= endTime
}

// 添加日期范围
export function addDateRange(params, dateRange, propName) {
  let search = params
  search.params =
    typeof search.params === "object" && search.params !== null && !Array.isArray(search.params)
      ? search.params
      : {}
  dateRange = Array.isArray(dateRange) ? dateRange : []
  search[`${propName}From`] = dateRange[0]
  search[`${propName}To`] = dateRange[1]

  return search
}

// 传入秒钟，格式化为对应的时分秒
export function formatSeconds(seconds) {
  var hours = Math.floor(seconds / 3600)
  var minutes = Math.floor((seconds % 3600) / 60)
  var remainingSeconds = seconds % 60
  var result = ""

  if (hours > 0) {
    result += hours + "小时"
  }
  if (minutes > 0) {
    result += minutes + "分钟"
  }
  if (hours == 0 && minutes == 0) {
    result = remainingSeconds + "秒"
  } else if (remainingSeconds > 0) {
    result += remainingSeconds + "秒"
  }

  return result
}

// 获取文件名称
export function getFileName(name) {
  name = decodeURIComponent(name)
  // 从URL中获取文件名
  const fileName = name.substring(name.lastIndexOf("/") + 1)
  // 移除文件扩展名
  const fileNameWithoutExtension = fileName.split(".").slice(0, -1).join(".")

  return fileNameWithoutExtension
}

//阿拉伯数字转中文数字
export function changeNumToHan(num) {
  var arr1 = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"]
  var arr2 = [
    "",
    "十",
    "百",
    "千",
    "万",
    "十",
    "百",
    "千",
    "亿",
    "十",
    "百",
    "千",
    "万",
    "十",
    "百",
    "千",
    "亿"
  ]
  if (!num || isNaN(num)) return "零"
  var english = num.toString().split("")
  var result = ""
  for (var i = 0; i < english.length; i++) {
    var des_i = english.length - 1 - i // 倒序排列设值
    result = arr2[i] + result
    var arr1_index = english[des_i]
    result = arr1[arr1_index] + result
  }
  result = result.replace(/零(千|百|十)/g, "零").replace(/十零/g, "十") // 将【零千、零百】换成【零】 【十零】换成【十】
  result = result.replace(/零+/g, "零") // 合并中间多个零为一个零
  result = result.replace(/零亿/g, "亿").replace(/零万/g, "万") // 将【零亿】换成【亿】【零万】换成【万】
  result = result.replace(/亿万/g, "亿") // 将【亿万】换成【亿】
  result = result.replace(/零+$/, "") // 移除末尾的零
  // 将【一十】换成【十】
  result = result.replace(/^一十/g, "十")
  return result
}

// 计算子字符串在原始字符串中出现的次数
export function countSubstr(str, subStr) {
  return str.split(subStr).length - 1
}

export function dataURLtoFile(dataurl, filename) {
  var arr = dataurl.split(",")
  var mime = arr[0].match(/:(.*?);/)[1]
  var bstr = atob(arr[1])
  var n = bstr.length
  var u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new File([u8arr], filename, { type: mime })
}

/**  isRequiredIP 为true时如果当前是 IP 地址，则直接返回IP Address，为false则返回空; 如果是 uzi.bkehs.cn 则返回'uzi' */
export function getSubdomain(isRequiredIP = true) {
  try {
    let subdomain = ""
    const { domain } = document
    const domainList = domain.split(".")
    const ipAddressReg =
      /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/ // 若为 IP 地址、localhost，则直接返回
    if (ipAddressReg.test(domain) || domain === "localhost") {
      return isRequiredIP ? domain : ""
    }
    subdomain = domainList[0]

    if (env === "production") {
      if (subdomain === "training" || subdomain === "learning") return ""
      subdomain = subdomain + "."
      return subdomain || document.domain
    } else if (env === "hwproduction" || env === "xdproduction") {
      subdomain = subdomain.replace("eduuser", "")
      return subdomain
    }
  } catch (e) {
    return document.domain
  }
}

// 跳转至管理端
export function pushToAdmin(path = "/login", isRequiredIP = true) {
  const subDomain = getSubdomain(isRequiredIP)
  const tenantStore = useTenantStore()
  // 检查是否为pt租户，是的话触发登录弹窗事件而不是跳转
  if (
    subDomain === "pt" ||
    localStorage.getItem("domainName") === "pt" ||
    tenantStore.domainName === "pt"
  ) {
    // 触发全局登录弹窗事件
    const customEvent = new CustomEvent("global-login-dialog", {
      detail: { source: "pushToAdmin", path }
    })
    window.dispatchEvent(customEvent)
    return
  }
  if (
    subDomain === "pthtg" ||
    localStorage.getItem("domainName") === "pthtg" ||
    tenantStore.domainName === "pthtg"
  ) {
    return
  }

  if (path === "/login") {
    if (productionEnvList.includes(env)) {
      window.location.href = `https://${subDomain}${loginPageUrl}${path}?redirect=https://${subDomain}${redirectUrl}`
    } else {
      window.location.href = `${loginPageUrl}${path}?redirect=${redirectUrl}`
    }
  } else if (path === "/index") {
    const testTenant = localStorage.getItem("testTenant")
    if (productionEnvList.includes(env)) {
      window.location.href = `https://${subDomain}${loginPageUrl}${path}?token=${getToken()}`
    } else {
      window.location.href = `${loginPageUrl}${path}?token=${getToken()}&testTenant=${testTenant}`
    }
  }
}

// 获取传入时间到当前时间相差的秒数
export function getTimeDifferenceInSeconds(time: string) {
  const timestamp = new Date(time).getTime()
  const nowTimestamp = new Date().getTime()
  const differenceInMilliseconds = Math.abs(nowTimestamp - timestamp)
  const differenceInSeconds = Math.floor(differenceInMilliseconds / 1000)
  return differenceInSeconds
}

// 定义文件类型枚举
export enum FileType {
  WORD = "word",
  EXCEL = "excel",
  PDF = "pdf",
  PPT = "ppt",
  UNKNOWN = "unknown"
}

// 定义允许的文件扩展名映射
const FILE_EXTENSIONS: Record<FileType, readonly string[]> = {
  [FileType.WORD]: ["doc", "docx"],
  [FileType.EXCEL]: ["xls", "xlsx"],
  [FileType.PDF]: ["pdf"],
  [FileType.PPT]: ["ppt", "pptx"],
  [FileType.UNKNOWN]: []
} as const

/**
 * 获取文件类型
 * @param fileName 文件名
 * @returns FileType 文件类型枚举值
 */
export function getFileType(fileName: string): FileType {
  // 获取文件扩展名
  const fileExtension = fileName.split(".").pop()?.toLowerCase()

  if (!fileExtension) return FileType.UNKNOWN

  // 遍历文件类型映射查找匹配的类型
  for (const [type, extensions] of Object.entries(FILE_EXTENSIONS)) {
    if (extensions.includes(fileExtension)) {
      return type as FileType
    }
  }

  return FileType.UNKNOWN
}

// 去除文件名后缀
export function removeFileExtension(filename) {
  const lastDotIndex = filename.lastIndexOf(".")
  if (lastDotIndex !== -1) {
    return filename.substring(0, lastDotIndex)
  } else {
    return filename
  }
}

// 判断字符串是否包含中文
export function containsChinese(str: string): boolean {
  return /[\u4e00-\u9fa5]/.test(str)
}

// 获取显示用的用户名
export function getDisplayName(nickName?: string, userName?: string): string {
  if (!nickName && !userName) return ""

  // 如果任一名字包含中文，优先返回包含中文的名字
  if (nickName && containsChinese(nickName)) return nickName
  if (userName && containsChinese(userName)) return userName

  // 如果都不包含中文，优先返回 nickName
  return nickName || userName || ""
}
