<!--
 * @Description: 
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-16 10:20:53
 * @LastEditTime: 2024-12-06 16:59:48
-->
<!--
 * @Description: 我的考试
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-16 10:20:53
 * @LastEditTime: 2024-12-06 16:59:41
-->
<script setup lang="ts">
  import { ElMessage } from "element-plus"
  import type { ExamListInfo, ExamItem } from "@/types"
  import { Search, ArrowRight } from "@element-plus/icons-vue"
  import { getNeedExamList, checkTaskCompleted } from "@/api/onlineExam"
  import useTenantStore from "@/store/modules/tenant"
  import { DEFAULT_COVER } from "@/utils/constant"

  const { proxy } = getCurrentInstance()!
  const { exam_status } = proxy?.useDict("exam_status")!
  const { domainName } = storeToRefs(useTenantStore())
  const router = useRouter()

  // 添加全部选项
  const allExamStatus = computed(() => {
    return [
      { label: "全部", value: "", elTagType: "default", elTagClass: null },
      // @ts-ignore
      ...exam_status.value
    ]
  })

  const searchValue = ref("")
  const pageNum = ref(1)
  const pageSize = ref(8)
  const dataTotal = ref<number | undefined>(0)
  const examStatus = ref("") // 默认为空字符串，对应"全部"选项

  const handleDetail = async (item: ExamItem) => {
    if (item.remark) {
      const res = await checkTaskCompleted({ taskId: item.remark })
      if (!res.data) {
        if (domainName.value === "yygf") {
          ElMessage({
            message:
              "提醒！本次工伤预防项目，需要学员首先完成“我的课程”里所有课程，才能进入“我的考试”完成考试！",
            type: "warning"
          })
        } else {
          ElMessage({
            message: "提醒！学员首先完成“我的课程”里所有课程，才能进入“我的考试”完成考试！",
            type: "warning"
          })
        }
        return
      }
    }
    router.push({
      path: "/prepare",
      query: {
        baseId: item.baseId,
        arrangeId: item.arrangeId,
        taskId: item.taskId
      }
    })
  }

  const examListInfo = ref<ExamListInfo>()
  const loadData = async () => {
    let queryData = {
      baseName: searchValue.value,
      pageNum: pageNum.value,
      pageSize: pageSize.value,
      examStatus: examStatus.value, // 添加考试状态筛选
      ...extraQueryData.value
    }
    const { rows, total } = await getNeedExamList(queryData)
    examListInfo.value = rows
    dataTotal.value = total
  }

  const extraQueryData = ref({})
  const handleFilterChange = () => {
    pageNum.value = 1
    loadData()
  }

  const arrowSortChange = value => {
    pageNum.value = 1
    extraQueryData.value = { ...value }
    loadData()
  }

  loadData()
</script>

<template>
  <div class="bg-white">
    <div class="p-4 border-b border-gray-200">
      <div class="flex items-center">
        <div class="text-gray-600 text-16px mr-5 text-skyblueColor font-bold">考试状态：</div>
        <el-radio-group v-model="examStatus" @change="handleFilterChange">
          <el-radio v-for="item in allExamStatus" :key="item.value" :label="item.value">
            {{ item.label }}
          </el-radio>
        </el-radio-group>
      </div>
    </div>

    <div class="flex justify-between items-center p-4 border-b-2px border-gray-200 border-b-dashed">
      <BaseArrowSort
        :data-list="[
          { name: 'a.last_study_time', label: '最后学习' },
          { name: 'a.learning_process', label: '完成进度' },
          { name: 'b.course_name', label: '名称' }
        ]"
        @sort="arrowSortChange"
      />
      <el-input
        v-model="searchValue"
        class="w-300px rounded-full"
        :prefix-icon="Search"
        @keyup.enter="handleFilterChange"
        @clear="handleFilterChange"
        clearable
      />
    </div>

    <div v-if="examListInfo && examListInfo.length > 0" class="p-5">
      <div
        v-for="item in examListInfo"
        :key="item.arrangeId"
        class="flex items-center p-5 border-b border-gray-200"
      >
        <div class="w-300px flex-shrink-0 relative">
          <div class="pb-[56.25%] relative overflow-hidden">
            <img
              :src="DEFAULT_COVER"
              alt=""
              class="absolute inset-0 w-full h-full object-cover object-center"
            />
          </div>
        </div>

        <div class="flex justify-between items-stretch ml-5 flex-1 h-[169px]">
          <div class="flex flex-col justify-between flex-1">
            <div class="text-20px font-bold">{{ item.baseName }}</div>
            <div class="text-16px text-gray-600">
              时间：{{ item.startTime }} - {{ item.endTime }}
            </div>
            <div class="text-16px font-bold flex items-center">
              <span class="text-gray-600">状态：</span>
              <dict-tag
                class="text-14px"
                size="large"
                :options="exam_status"
                :value="item.examStatus"
              />
            </div>
          </div>

          <div class="flex items-center">
            <el-button
              v-if="item.examStatus === '0'"
              type="primary"
              @click="handleDetail(item)"
              class="w-150px h-40px text-16px hover:scale-105 transition-transform"
            >
              开始考试
              <el-icon class="ml-2"><ArrowRight /></el-icon>
            </el-button>
            <el-button v-else @click="handleDetail(item)" class="w-150px h-40px text-16px">
              查看详情
              <el-icon class="ml-2"><ArrowRight /></el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <el-empty v-else description="暂无数据" :image-size="200" />

    <BasePagination
      v-if="examListInfo && examListInfo.length > 0"
      class="mt-4"
      :total="dataTotal!"
      v-model:pageNum="pageNum"
      v-model:pageSize="pageSize"
      @pagination="loadData"
    />
  </div>
</template>
