<!--
 * @Description: 嘉定学习平台-首页
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-01-15 11:45:02
 * @LastEditTime: 2024-10-25 14:03:15
-->

<script lang="ts" setup>
  const router = useRouter()

  const pathList = ref([
    { url: "幼儿园", name: "幼儿课程", type: "2252" },
    { url: "小学", name: "小学课程", type: "2253" },
    { url: "中学", name: "初中课程", type: "2254" },
    { url: "高中", name: "高中课程", type: "2255" }
  ])

  const toPath = ({ type }) => {
    router.push(`/jdCourse?type=${type}`)
  }

  const getAssetUrl = index => {
    return new URL(`../../../assets/images/${index}.png`, import.meta.url).href
  }
</script>
<template>
  <div class="prevent-disaster-home">
    <h2 class="title">
      <img src="https://training-voc.obs.cn-north-4.myhuaweicloud.com:443/jd/jd_title.png" alt="" />
    </h2>
    <ul class="loginType">
      <li v-for="item in pathList" @click="toPath(item)">
        <div class="imgcon">
          <img :src="getAssetUrl(item.url)" />
        </div>
      </li>
    </ul>
  </div>
</template>

<style scoped lang="less">
  .prevent-disaster-home {
    padding: 30px 0 45px 0;
    width: 1240px;
    margin: 0 auto;
  }
  .title {
    height: 60px;
    color: #f59a23;
    font-size: 50px;
    font-weight: bolder;
    line-height: 1;
    margin-bottom: 50px;
    text-align: center;
    text-shadow: 2px 2px 2px #333333;
  }

  .loginType {
    display: flex;
    justify-content: space-between;
    width: 100%;
    left: 50%;
    transform: translateX(-50%);
    position: relative;
    top: 20%;
    font-size: 0;
    padding: 20px;
    background: rgba(255, 255, 255, 0.4);
    border-radius: 6px;
    gap: 20px;
    > li {
      background-color: rgba(255, 255, 255, 0.85);
      cursor: pointer;
      border-radius: 8px;
      overflow: hidden;
      flex: 1;
      box-shadow: 0 0 10px 0 rgba(0, 0, 100, 0.25);
      .imgcon {
        width: 100%;
        height: 50vh;

        position: relative;
        > img {
          width: 100%;
          position: absolute;
          top: 30%;
          left: 50%;
          transform: translate(-50%, -30%);
        }
      }
      > p {
        line-height: 50px;
        font-size: 52px;
        color: var(--el-color-primary);
        text-align: center;
        // margin-bottom: 10%;
      }
    }
    > li:hover {
      /* font-weight: bold; */
      box-shadow: 0 0 30px 0 rgba(0, 0, 100, 0.25);
    }
  }

  .disaster-content {
    width: 1240px;
    background-color: #fff;
    padding: 50px 80px;
    line-height: 30px;
    font-size: 15px;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
  }

  .bottom-btn {
    margin-top: 46px;
    display: flex;
    font-size: 24px;
    font-weight: bold;
    justify-content: space-around;
    align-items: center;
    flex-wrap: wrap;
    div {
      margin: 30px 80px 0 80px;
      height: 60px;
      width: 25%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      color: #fff;
    }
  }
</style>
