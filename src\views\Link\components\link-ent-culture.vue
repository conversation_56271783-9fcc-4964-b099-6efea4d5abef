<script setup lang="ts">
import useStore from "@/store";
//获取轮播图图片
const { singleRichText } = useStore();
singleRichText.getSingleRichTextQY();
</script>

<template>
  <div class="linkus">
    <h3>{{ singleRichText.singleRichTextqywh.title }}</h3>
    <div class="linkus-article">
      <div v-html="singleRichText.singleRichTextqywh.content"></div>
    </div>
  </div>
</template>

<style scoped lang="less">
.linkus {
  width: 940px;
  background: #fff;
  h3 {
    height: 70px;
    line-height: 70px;
    border-bottom: 1px solid #f5f5f5;
    padding-left: 50px;
    font-size: 18px;
    font-weight: 400;
    margin-bottom: 10px;
    text-align: center;
  }
  .linkus-article {
    margin-top: 20px;
    padding-bottom: 40px;
  }
}
</style>
