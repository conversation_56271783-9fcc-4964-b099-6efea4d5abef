<script setup lang="ts">
  import { RouterLink } from "vue-router"
  import AppHeaderNav from "./layout-header-nav.vue"
  import useTenantStore from "@/store/modules/tenant"
  import { storeToRefs } from "pinia"
  // vueuse 中导入获取屏幕滚动数据的函数
  import { useWindowScroll } from "@vueuse/core"
  // 解构出 y 表示垂直方向滚动值，y 是 ref 响应式数据，可直接用于模板绑定
  const { y } = useWindowScroll()
  const tenantStore = useTenantStore()
  const { tenantLogo } = storeToRefs(tenantStore)
</script>

<template>
  <div class="app-header-sticky" :class="{ show: y > 78 }">
    <div class="container">
      <RouterLink
        class="logo"
        :style="
          tenantLogo
            ? `background-image: url(${tenantLogo})`
            : `background-image: url('https://beckwelldb.obs.cn-east-3.myhuaweicloud.com/logo2.png')`
        "
        to="/"
      />
      <AppHeaderNav />
      <!-- <div class="right">
        <RouterLink to="/login"
          ><i class="iconfont icon-phone"></i>注册/登录</RouterLink
        >
      </div> -->
    </div>
  </div>
</template>

<style scoped lang="less">
  .app-header-sticky {
    display: flex;
    align-items: center;
    width: 100%;
    height: 80px;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 999;
    background-color: #fff !important;
    border-bottom: 1px solid #fff;
    transform: translateY(-500%);

    &.show {
      transition: all 1s linear;
      transform: translateY(0%);
    }

    .container {
      display: flex;
      align-items: center;
    }

    .logo {
      width: 180px;
      height: 40px;
      margin-right: 30px;
      background-repeat: no-repeat;
      background-position: right 2px;
      background-size: 160px auto;
    }

    .right {
      width: 220px;
      display: flex;
      text-align: center;
      padding-left: 40px;
      border-left: 2px solid @baseColor;

      a {
        width: 138px;
        margin-right: 40px;
        font-size: 16px;
        line-height: 1;

        &:hover {
          color: @baseColor;
        }
      }
    }
  }
</style>
