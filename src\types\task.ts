import { MessageBox, EditPen, Document } from "@element-plus/icons-vue"
import { markRaw } from "vue"

// 任务类型枚举
export enum TaskType {
  COURSE = "0",
  EXAM = "1",
  QUESTIONNAIRE = "2",
  RESOURCE = "3"
}

// 任务类型配置
export const TaskTypeConfig = {
  [TaskType.COURSE]: {
    key: TaskType.COURSE,
    text: "在线课程",
    color: "#0088f4",
    icon: markRaw(MessageBox)
  },
  [TaskType.EXAM]: {
    key: TaskType.EXAM,
    text: "在线考试",
    color: "#70b603",
    icon: markRaw(EditPen)
  },
  [TaskType.QUESTIONNAIRE]: {
    key: TaskType.QUESTIONNAIRE,
    text: "问卷调查",
    color: "#f59a23",
    icon: markRaw(Document)
  },
  [TaskType.RESOURCE]: {
    key: TaskType.RESOURCE,
    text: "资料学习",
    color: "#8080ff",
    icon: markRaw(Document)
  }
} as const

// 获取任务类型信息的工具函数
export const getTaskTypeInfo = (item: any) => {
  if (item.courseId > 0) return TaskTypeConfig[TaskType.COURSE]
  if (item.examId > 0) return TaskTypeConfig[TaskType.EXAM]
  if (item.questionnaireId > 0) return TaskTypeConfig[TaskType.QUESTIONNAIRE]
  return TaskTypeConfig[TaskType.RESOURCE]
}

// 任务状态枚举
export enum TaskStatus {
  NOT_START = "1",
  IN_PROGRESS = "2",
  COMPLETED = "3",
  EXPIRED = "4"
}

// 任务状态配置
export const TaskStatusConfig = {
  [TaskStatus.NOT_START]: {
    text: "未开始",
    type: "info"
  },
  [TaskStatus.IN_PROGRESS]: {
    text: "进行中",
    type: "warning"
  },
  [TaskStatus.COMPLETED]: {
    text: "已完成",
    type: "success"
  },
  [TaskStatus.EXPIRED]: {
    text: "已超期",
    type: "danger"
  }
} as const

// completionStatus枚举
export enum CompletionStatus {
  NOT_START = "0",
  IN_PROGRESS = "1",
  COMPLETED = "2"
}

// completionStatus配置
export const CompletionStatusConfig = {
  [CompletionStatus.NOT_START]: {
    text: "未开始",
    type: "info"
  },
  [CompletionStatus.IN_PROGRESS]: {
    text: "进行中",
    type: "warning"
  },
  [CompletionStatus.COMPLETED]: {
    text: "已完成",
    type: "success"
  }
} as const

export interface taskInfoType {
  coverPhoto: string
  endTime: string
  startTime: string
  rateLearning: number
  taskId: number
  taskName: string
  taskStatus: TaskStatus
  courseId: number
  completionStatus: CompletionStatus
  examId: number
  questionnaireId: number
  preconditionLinkList: Array<any>
  mixedPrecondition: string
  examName: string
  courseName: string
  manageName: string
  questionnaireName: string
  learningProcess: number
  fieldType: TaskType
  fieldId: any
  duration?: any
  result?: any
}
