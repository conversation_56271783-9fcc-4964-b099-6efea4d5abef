<template>
  <div class="slider-news">
    <div class="news-contain">
      <aside class="news-left">
        <div class="news-left-icon"></div>
      </aside>
      <div class="news-content" v-if="latestData">
        <div class="course-news-title">课程动态 </div>
        <div class="scroll-container">
          <vue3ScrollSeamless
            class="scroll-wrap"
            :classOptions="classOption"
            :dataList="latestData"
          >
            <ul class="ul-wrap">
              <li class="li-item" v-for="(item, i) of latestData" :key="i">
                <p>{{ item.news }}</p>
              </li>
            </ul>
          </vue3ScrollSeamless>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { vue3ScrollSeamless } from "vue3-scroll-seamless"
  import { formatSeconds } from "@/utils/common"
  import { ref } from "vue"
  import { getLatestLearningLog } from "@/api/home"
  import type { LatestDataType } from "@/types"

  let latestData = ref<LatestDataType>()
  const fetchData = async () => {
    const { rows } = await getLatestLearningLog()
    if (!rows || rows.length === 0) return
    latestData.value = rows.map(item => {
      item.news = `${item.userName} 学习了 ${formatSeconds(item.lastDeltaDuration)} 《${
        item.courseName
      }》 `
      return item
    })
  }

  const classOption = ref({
    singleHeight: 50,
    waitTime: 5000,
    limitMoveNum: 2,
    hoverStop: false
  })

  fetchData()
</script>

<style scoped lang="less">
  .slider-news {
    background: #fff;
    width: 100%;
  }

  .news-contain {
    align-items: center;
    background: #fff;
    border-radius: 0 0 10px 10px;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
    display: flex;
    height: 80px;
    margin: 0 auto;
    width: 100%;

    .news-left {
      border-right: 1px solid rgba(184, 192, 218, 0.35);
      flex-shrink: 0;
      padding: 7px 24px;
      width: 171px;

      .news-left-icon {
        background-image: url("@/assets/images/news.png");
        background-position: 50%;
        background-repeat: no-repeat;
        background-size: 100%;
        height: 26px;
        width: 123px;
      }
    }

    .news-content {
      display: flex;
      padding: 13px 24px;
      width: 100%;
      height: 100%;

      .course-news-title {
        display: flex;
        justify-content: center;
        align-items: center;
        background: #ffeee0;
        border-radius: 3px;
        color: #ff6200;
        flex-shrink: 0;
        font-size: 14px;
        font-weight: 400;
        line-height: 1;
        margin-right: 12px;
        margin: 15px 0;
        padding: 5px 10px;
        text-align: center;
      }

      .scroll-container {
        margin-left: 30px;
        overflow: hidden;
        ul {
          list-style: none;
          padding: 0;
          margin: 0 auto;
          li,
          a {
            display: block;
            height: 50px;
            line-height: 50px;
            display: flex;
            justify-content: space-between;
            font-size: 15px;
          }
        }
      }
    }
  }
</style>
