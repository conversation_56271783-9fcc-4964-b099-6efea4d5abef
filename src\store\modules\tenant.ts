/*
 * @Description: 租户相关store
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-08-16 14:43:00
 * @LastEditTime: 2025-07-04 11:42:50
 */
import { defineStore } from "pinia"
import { querySysTenantInfo } from "@/api/system/tenant"

const useTenantStore = defineStore("tenant", {
  state: () => ({
    tenantName: "",
    tenantLogo: "",
    startTime: "",
    endTime: "",
    domainName: "yygf"
  }),
  actions: {
    async queryTenantInfo() {
      const { data } = await querySysTenantInfo()
      this.tenantLogo =
        data?.tenantIcon || "https://beckwelldb.obs.cn-east-3.myhuaweicloud.com/logo2.png"
      this.domainName = data?.domain
      this.tenantName = data?.name
      this.startTime = data?.activityInfo?.startTime
      this.endTime = data?.activityInfo?.endTime
    }
  }
})

export default useTenantStore
