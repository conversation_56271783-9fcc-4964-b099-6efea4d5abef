<script lang="ts">
  export default {
    name: "homeTrailer"
  }
</script>
<script lang="ts" setup>
  import { listVideo } from "@/api/trailer"
  import { DEFAULT_COVER } from "@/utils/constant"

  const trailerList = ref<any>()
  // 视频播放弹窗相关的响应式变量
  const dialogVisible = ref(false)
  const currentVideo = ref({
    url: "",
    title: ""
  })

  // 获取首页公益宣传片
  const getRecommendCourse = async () => {
    let queryData = {
      pageNum: 1,
      pageSize: 10,
      videoStatus: "1"
    }
    const { rows } = await listVideo(queryData)
    trailerList.value = rows || []
  }

  // 打开视频播放弹窗
  const openVideoDialog = (video: any) => {
    currentVideo.value = {
      url: video.url,
      title: video.title
    }
    dialogVisible.value = true
  }

  onMounted(() => {
    getRecommendCourse()
  })
</script>
<template>
  <div class="recommend" v-if="trailerList?.length">
    <div class="recommend-title">
      <div class="recommend-title-left">
        <div>公益宣传片</div>
      </div>
    </div>
    <div class="recommend-list">
      <div
        class="recommend-item"
        v-for="item in trailerList"
        :key="item.psaId"
        @click="openVideoDialog(item)"
      >
        <div class="recommend-img">
          <img :src="item.cover || DEFAULT_COVER" alt="" />
        </div>
        <div class="foot">
          <div class="meta ellipsis">{{ item.title }}</div>
          <div class="info">
            <div class="info-left"> </div>
            <div class="info-right">
              <el-button type="primary" class="bg-primary study_immediate"> 立即观看 </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 视频播放弹窗 -->
    <el-dialog
      top="5vh"
      v-model="dialogVisible"
      :title="currentVideo.title"
      width="1200px"
      destroy-on-close
      center
    >
      <div class="video-container">
        <video
          v-if="currentVideo.url"
          :src="currentVideo.url"
          controls
          autoplay
          controlsList="nodownload"
          oncontextmenu="return false;"
          disablePictureInPicture
          style="width: 100%"
        ></video>
      </div>
    </el-dialog>
  </div>
</template>
<style scoped lang="less">
  .recommend {
    margin: 20px auto;
    border-radius: 15px;
    padding: 20px 20px 10px 20px;
    width: 95%;
    background-color: white;
    .recommend-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
      .recommend-title-left {
        border-left: 5px solid #6b7dfa;
        padding-left: 10px;
        :first-child {
          font-size: 22px;
        }
      }
      .recommend-title-right {
        display: flex;
        align-items: center;
      }
    }
    .recommend-list {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      grid-column-gap: 20px;
      .recommend-item {
        box-shadow: 0 2px 10px 0 rgba(103, 111, 144, 0.15);
        box-sizing: border-box;
        border-radius: 5px;
        // width: 290px;
        background: #fff;
        margin-bottom: 30px;
        overflow: hidden;
        cursor: pointer;
        .hoverShadow();
        div {
          display: block;
          position: relative;
          img {
            /*  border-radius: 5px; */
            width: 100%;
            height: 160px;
          }
        }
        .foot {
          padding: 13px;
          font-size: 14px;
          color: #545c63;
          .meta {
            margin-bottom: 10px;
            font-size: 14px;
            font-weight: 500;
          }
          // 一行省略
          .ellipsis {
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }

          .info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            &-left {
              display: flex;
              font-size: 13px;

              .iconfont {
                color: @warmOrange;
                font-weight: bold;
              }

              .lecturer {
                margin-right: 15px;
              }
            }
            &-right {
              .study_immediate {
                border-radius: 5px;
                color: white;
                font-family: Microsoft YaHei;
                font-size: 14px;
                font-weight: 400;
                height: 28px;
                line-height: 28px;
                text-align: center;
                width: 88px;
                border: none;
              }
            }
          }
        }
      }
    }
  }

  // 添加视频容器样式
  .video-container {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    video {
      max-height: 70vh;
      object-fit: contain;
    }
  }

  // 让列表项可点击的样式
  .recommend-item {
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-5px);
    }
  }
</style>
