/*
 * @Description: 在线考试-考试管理相关api
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-06 16:17:49
 * @LastEditTime: 2024-08-16 11:27:03
 */
import request from "@/utils/request"

// 查询考试列表
export function listExam(query) {
  return request({
    url: "/exam/base/list",
    method: "get",
    params: query
  })
}

// 查询考试详细
export function getExam(examId) {
  return request({
    url: "/exam/base/" + examId,
    method: "get"
  })
}

// 查询考试安排列表
export function listArrange(query) {
  return request({
    url: "/exam/arrange/list",
    method: "get",
    params: query
  })
}

// 查询考试安排/记录详细
export function getArrange(arrangeId) {
  return request({
    url: "/exam/arrange/" + arrangeId,
    method: "get"
  })
}

// 查询待考试列表
export function getNeedExamList(query) {
  return request({
    url: "/exam/arrange/userNeedExamList",
    method: "get",
    params: query
  })
}

// 查询试卷详细
export function getPaper(paperId) {
  return request({
    url: "/exam/paper/getInfoQuestion/" + paperId,
    method: "get"
  })
}

// 开始考试
export function startTheExam(data) {
  return request({
    url: "/exam/paper-answer/startExam",
    method: "put",
    data: data
  })
}

// 每道题目完成
export function completeEachQuestion(data) {
  return request({
    url: "/exam/question-answer",
    method: "post",
    data: data
  })
}

// 提交试卷
export function submitAnswerPaper(data) {
  return request({
    url: "/exam/paper-answer/submitAnswerPaper",
    method: "put",
    data: data
  })
}

export function getPaperAnswerInfo(paperAnswerId) {
  return request({
    url: "/exam/paper-answer/" + paperAnswerId,
    method: "get"
  })
}

// 查询已考完的试卷详情
export function completedPaperDetail(data) {
  return request({
    url: "/exam/paper-answer/completedPaperDetail",
    method: "put",
    data: data
  })
}

// 北蔡防灾减灾首页获取考试baseId与arrangeId
export function getTownExamInfo() {
  return request({
    url: "/exam/arrange/examList",
    method: "get"
  })
}

// 北蔡防灾减灾首页获取回顾页内容
export function getTownHistoryList() {
  return request({
    url: "/exam/paper-answer/historyList",
    method: "get"
  })
}

// 工会项目根据taskId获取考试examId与arrangeId
export function getCjaqExamInfo(params) {
  return request({
    url: "/exam/arrange/examByTask",
    method: "get",
    params
  })
}

// 金外滩获取考试试题列表
export function jinwaitanQuestionList(examId) {
  return request({
    url: "/exam/paper/questionList/" + examId,
    method: "get"
  })
}

// 西电项目根据baseId查每日考试的arrangeId
export function getXdExamInfo(params) {
  return request({
    url: "/exam/arrange/dailyExams",
    method: "get",
    params
  })
}

// 查询试题详细
export function getQuestion(questionId) {
  return request({
    url: "/exam/question/" + questionId,
    method: "get"
  })
}

// 限制培训任务类考试是否已完成前置课程
export function checkTaskCompleted(params) {
  return request({
    url: "/exam/arrange/checkCompleted",
    method: "get",
    params
  })
}
