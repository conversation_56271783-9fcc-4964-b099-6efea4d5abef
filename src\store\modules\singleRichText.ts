import { defineStore } from "pinia";
import request from "@/utils/request";

import type { ApiRes, SingleRichText } from "@/types";

const useSingleRichTextStore = defineStore({
  id: "singleRichText",
  state: () => ({
    singleRichText: {} as SingleRichText,
    singleRichTextqywh: {} as SingleRichText,
    singleRichTextcxry: {} as SingleRichText,
    singleRichTextrczp: {} as SingleRichText,
    singleRichTextlxfs: {} as SingleRichText,
  }),
  actions: {
    async getSingleRichText() {
      const res = await request.get<ApiRes<SingleRichText>>(
        "/api/cms/richText/info?flag=gymy"
      );
      this.singleRichText = res.data.data;
    },
    async getNewsInfoText(id: string) {
      const res = await request.get<ApiRes<SingleRichText>>(
        "/api/cms/imgTextList/info?id=" + id
      );
      this.singleRichText = res.data.data;
    },
    //企业文化。一旦接口完成此处为动态取出，可以通过参数控制
    async getSingleRichTextQY() {
      const res = await request.get<ApiRes<SingleRichText>>(
        "/api/cms/richText/info?flag=qywh"
      );
      this.singleRichTextqywh = res.data.data;
    },
    //创业荣誉
    async getSingleRichTextRY() {
      const res = await request.get<ApiRes<SingleRichText>>(
        "/api/cms/richText/info?flag=cxry"
      );
      this.singleRichTextcxry = res.data.data;
    },
    //在线留言
    async getSingleRichTextLY(flag: string) {
      const res = await request.get<ApiRes<SingleRichText>>(
        "/singleRichText/getSingleRichTextLY",
        {
          params: {
            flag,
          },
        }
      );
      this.singleRichText = res.data.data;
    },
    //人才招聘
    async getSingleRichTextZP() {
      const res = await request.get<ApiRes<SingleRichText>>(
        "/api/cms/richText/info?flag=rczp"
      );
      this.singleRichTextrczp = res.data.data;
    },
    //联系方式
    async getSingleRichTextLX() {
      const res = await request.get<ApiRes<SingleRichText>>(
        "/api/cms/richText/info?flag=lxfs"
      );
      this.singleRichTextlxfs = res.data.data;
    },
  },
});
export default useSingleRichTextStore;
