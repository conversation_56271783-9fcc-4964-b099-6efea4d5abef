<template>
  <div class="base-bread">
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
  // 分隔符数据是位于Bread组件中 而对于分隔符数据的使用是在底层的组件中使用
  // provide/inject
  import { provide } from "vue"

  const props = defineProps({
    separator: {
      type: String,
      default: ""
    }
  })
  provide("separator", props.separator)
</script>

<style scoped lang="less">
  .base-bread {
    display: flex;
    padding: 25px 10px;
    &-item {
      a {
        transition: all 0.4s;
        &:hover {
          color: @warmOrange;
        }
      }
    }
    i {
      font-size: 12px;
      margin-left: 5px;
      margin-right: 5px;
      line-height: 22px;
    }
  }
</style>
