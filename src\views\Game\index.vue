<!--
 * @Description: 游戏中心
 * @Author: Assistant
 * @LastEditors: <PERSON>
 * @Date: 2025-01-27
 * @LastEditTime: 2025-06-06 15:12:56
-->
<script lang="ts" setup>
  import { listGame } from "@/api/game"
  import { BasePagination } from "@/components/BaseUI"

  const route = useRoute()
  const router = useRouter()
  const gameList = ref<any[]>([])

  const queryParams = ref({
    pageNum: 1,
    pageSize: 20,
    gameName: "",
    status: 1 // 只查询上架的游戏
  })
  const dataTotal = ref<number>(0)
  const loading = ref(false)

  async function fetchData() {
    loading.value = true
    try {
      const { rows, total } = await listGame(queryParams.value)
      dataTotal.value = total || 0
      gameList.value = rows || []
    } catch (error) {
      console.error("获取游戏列表失败:", error)
    } finally {
      loading.value = false
    }
  }



  // 点击游戏跳转到详情页
  const handleGameClick = (gameItem: any, event: Event) => {
    event.preventDefault()
    const { id } = gameItem
    
    const routeData = router.resolve({
      path: "/game/detail",
      query: {
        id: id
      }
    })
    window.open(routeData.href, "_blank")
  }

  onMounted(() => {
    fetchData()
  })
</script>

<template>
  <div class="game-container">
    <div class="container">
      <div class="home-panel">
        <div class="container">
          <div class="head">
            <div class="title">
              <i class="iconfont icon-youxi"></i>游戏中心
            </div>
          </div>

          
          <!-- 游戏列表 -->
          <div v-loading="loading" class="game-list" v-if="gameList.length > 0">
            <div class="game-item" v-for="item in gameList" :key="item.id">
              <a
                href="#"
                @click="handleGameClick(item, $event)"
                class="game-link"
              >
                <div class="game-img">
                  <img :src="item.gameCover" :alt="item.gameName" />
                  <div class="play-overlay">
                    <i class="iconfont icon-play"></i>
                  </div>
                </div>
                <div class="foot">
                  <div class="meta ellipsis">{{ item.gameName }}</div>
                  <div class="game-type">{{ item.gameType || '休闲游戏' }}</div>
                  <div class="info">
                    <div class="info-left">
                      <div class="game-tag">
                        <i class="iconfont icon-youxi"></i>
                        在线游戏
                      </div>
                    </div>
                    <div class="info-right">
                      <el-button type="primary" class="play_immediate">
                        开始游戏
                      </el-button>
                    </div>
                  </div>
                </div>
              </a>
            </div>
          </div>
          
          <!-- 空状态 -->
          <el-empty
            class="empty-container"
            v-else-if="!loading"
            description="暂无游戏"
            :image-size="200"
          ></el-empty>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 分页 -->
  <BasePagination
    class="pagination"
    v-if="gameList.length > 0"
    :total="dataTotal"
    v-model:pageNum="queryParams.pageNum"
    v-model:pageSize="queryParams.pageSize"
    @pagination="fetchData"
  />
</template>

<style scoped lang="less">
  .game-container {
    background-color: #f8f9fa;
    min-height: calc(100vh - 120px);
  }

  .home-panel {
    padding: 30px 0 60px 0;

    .head {
      padding-bottom: 20px;
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      
      .title {
        font-size: 20px;
        font-weight: 550;
        line-height: 34px;
        color: #333;

        .iconfont {
          color: #409eff;
          font-size: 24px;
          margin-right: 12px;
          vertical-align: middle;
        }
      }
    }
  }

  .empty-container {
    padding-bottom: 200px;
  }



  .game-list {
    padding: 0;
    display: grid;
    gap: 20px;
    grid-template-columns: repeat(auto-fill, minmax(290px, 1fr));
    
    .game-item {
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      overflow: hidden;
      transition: all 0.3s ease;
      position: relative;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        
        .play-overlay {
          opacity: 1;
        }
        
        .play_immediate {
          background: @warmOrange !important;
          color: #fff !important;
        }
      }

      .game-link {
        display: block;
        text-decoration: none;
        color: inherit;
      }

      .game-img {
        position: relative;
        width: 100%;
        height: 200px;
        overflow: hidden;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.3s ease;
        }

        .play-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.4);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s ease;

          .iconfont {
            font-size: 48px;
            color: #fff;
          }
        }
      }

      .foot {
        padding: 20px;
        
        .meta {
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 8px;
          color: #333;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }

        .game-type {
          font-size: 14px;
          color: #666;
          margin-bottom: 16px;
          padding: 4px 8px;
          background: #f0f9ff;
          border-radius: 4px;
          display: inline-block;
        }

        .info {
          display: flex;
          align-items: center;
          justify-content: space-between;
          
          &-left {
            .game-tag {
              font-size: 13px;
              color: #409eff;
              display: flex;
              align-items: center;
              
              .iconfont {
                font-size: 14px;
                margin-right: 4px;
              }
            }
          }
          
          &-right {
            .play_immediate {
              background: #eff4fe;
              border-radius: 5px;
              color: #666;
              font-family: Microsoft YaHei;
              font-size: 14px;
              font-weight: 400;
              height: 28px;
              line-height: 28px;
              text-align: center;
              width: 88px;
              border: none;
              transition: all 0.3s ease;
            }
          }
        }
      }
    }
  }

  .pagination {
    background-color: #f8f9fa;
    padding-bottom: 100px;
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .game-list {
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
  }

  @media (max-width: 768px) {
    .game-list {
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 15px;
    }
    
    .game-item .foot {
      padding: 15px;
    }
  }
</style>
