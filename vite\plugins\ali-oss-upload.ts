/*
 * @Description: 阿里云OSS打包后自动上传
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-06-19 14:52:35
 * @LastEditTime: 2023-09-28 14:03:27
 */
import viteAliOssPlugin from "vite-ali-oss-plugin"

export default function createAliOssUpload(): any {
  return viteAliOssPlugin({
    region: "oss-cn-shanghai.aliyuncs.com", //oss地区
    accessKeyId: "LTAI5tPTtcYFxr8iaJiA4ZA7", //你的osskeyid值
    accessKeySecret: "******************************", //你的ossKeySecret值
    bucket: "beckwell-learning", //创建的oss存储仓库名称
    overwrite: true,
    endpoint: "oss-cn-shanghai.aliyuncs.com"
  })
}
