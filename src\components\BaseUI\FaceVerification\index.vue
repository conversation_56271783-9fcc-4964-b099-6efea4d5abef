<!--
 * @Description: 
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2024-11-29 16:14:02
 * @LastEditTime: 2024-12-24 15:19:49
-->
<script lang="ts" setup>
  import { ref, watch, onBeforeUnmount, computed } from "vue"
  import { ElMessage, ElMessageBox } from "element-plus"
  import { faceVerify } from "@/api/system/user"
  import { dataURLtoFile } from "@/utils/common"
  import { useRouter } from "vue-router"
  import useUserStore from "@/store/modules/user"

  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    },
    recordCredit: {
      type: String,
      default: "0"
    },
    taskType: {
      type: String,
      default: "course"
    },
    taskId: {
      type: [String, Number],
      default: ""
    },
    subTaskId: {
      type: [String, Number],
      default: ""
    },
    subTaskName: {
      type: String,
      default: ""
    },
    arrangeId: {
      type: [String, Number],
      default: ""
    }
  })

  const router = useRouter()
  const userStore = useUserStore()
  const taskTypeMap = {
    course: {
      text: "学习",
      type: "0"
    },
    exam: {
      text: "考试",
      type: "1"
    }
  }

  const emit = defineEmits(["update:visible", "verify-success", "verify-fail"])

  // 使用计算属性来处理弹窗的显示状态
  const dialogVisible = computed({
    get: () => props.visible,
    set: value => emit("update:visible", value)
  })

  const mediaStreamTrack = ref()
  const canvasRef = ref<HTMLCanvasElement | null>(null)
  const cameraRef = ref<HTMLVideoElement | null>(null)
  const loading = ref(false)
  const hasCamera = ref(true)

  // 检查用户是否有人脸信息
  const checkUserFaceInfo = async () => {
    if (!userStore.userInfo.faceImg) {
      try {
        await ElMessageBox.confirm(
          "您还未录入头像，导致无法识别人脸信息，可能会导致当前考试没有学分，是否前往个人中心录入人脸信息?",
          "提示",
          {
            confirmButtonText: "是",
            cancelButtonText: "否",
            type: "warning"
          }
        )
        // 用户点击"是"，跳转到个人中心
        router.push("/study/myInfo")
        closeDialog()
        return false
      } catch {
        // 用户点击"否"，继续人脸识别流程
        return true
      }
    }
    return true
  }

  // 开启摄像头
  const startCamera = async () => {
    // 检查浏览器是否支持 getUserMedia
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      console.log("浏览器不支持摄像头功能")
      hasCamera.value = false
      handleNoCameraCase()
      return
    }

    try {
      // 先检查是否有人脸信息
      const canContinue = await checkUserFaceInfo()
      if (!canContinue) return

      // 请求摄像头权限
      const stream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: false
      })

      hasCamera.value = true
      cameraRef.value!.srcObject = stream
      cameraRef.value!.play()
      mediaStreamTrack.value = stream
    } catch (error: any) {
      console.log("无法获取摄像头视频流：", error)
      hasCamera.value = false
      // 根据错误类型给出不同提示
      if (error.name === "NotAllowedError" || error.name === "PermissionDeniedError") {
        ElMessage.error("请允许浏览器访问摄像头")
      }
      handleNoCameraCase()
    }
  }

  // 处理无摄像头情况
  const handleNoCameraCase = () => {
    const message =
      props.recordCredit === "1"
        ? `抓拍失败，没有找到抓拍设备或未获得摄像头权限，继续${
            taskTypeMap[props.taskType]?.text
          }将记录学分。`
        : `抓拍失败，没有找到抓拍设备或未获得摄像头权限，继续${
            taskTypeMap[props.taskType]?.text
          }将不记录学分。`

    ElMessageBox.confirm(message, "提示", {
      confirmButtonText: `继续${taskTypeMap[props.taskType]?.text}`,
      cancelButtonText: "取消",
      type: "warning"
    })
      .then(() => {
        emit("verify-fail")
        closeDialog()
      })
      .catch(() => {
        closeDialog()
      })
  }

  // 拍照并验证
  const verify = async () => {
    if (!hasCamera.value) return

    loading.value = true
    try {
      canvasRef.value!.getContext("2d")!.drawImage(cameraRef.value!, 0, 0, 300, 200)
      const dataurl = canvasRef.value!.toDataURL("image/jpeg")
      const blobFile = dataURLtoFile(
        dataurl,
        `${userStore.userInfo.userName}-${new Date().getTime()}.jpg`
      )

      const formData = new FormData()
      formData.append("file", blobFile)
      formData.append("terminalSource", "0")
      formData.append("userId", userStore.userInfo.userId)
      formData.append("userName", userStore.userInfo.userName)
      formData.append("deptId", userStore.userInfo.dept?.deptId || "")
      formData.append("deptName", userStore.userInfo.dept?.deptName || "")
      formData.append("taskId", props.taskId?.toString() || "")
      formData.append("subTaskType", taskTypeMap[props.taskType]?.type || "")
      formData.append("subTaskId", props.subTaskId?.toString() || "")
      formData.append("subTaskName", props.subTaskName || "")
      if (props.arrangeId) {
        formData.append("arrangeId", props.arrangeId.toString())
      }

      const res = await faceVerify(formData)
      if (res.code === 200) {
        ElMessage.success("人脸识别成功")
        emit("verify-success", res.data)
        closeDialog()
      } else {
        const message =
          props.recordCredit === "1"
            ? "抓拍到非本人，是否继续？本次学习将记录学分。"
            : "抓拍到非本人，是否继续？本次学习将不记录学分。"

        ElMessageBox.confirm(message, "提示", {
          confirmButtonText: `继续${taskTypeMap[props.taskType]?.text}`,
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            emit("verify-fail")
            closeDialog()
          })
          .catch(() => {
            closeDialog()
          })
      }
    } catch (error) {
      console.error("人脸识别失败:", error)
      ElMessage.error("人脸识别失败，请重试")
    } finally {
      loading.value = false
    }
  }

  // 处理关闭弹窗
  const handleClose = () => {
    ElMessageBox.confirm(
      `关闭人脸识别将不记录${taskTypeMap[props.taskType]?.text}进度，是否确认关闭？`,
      "提示",
      {
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "warning"
      }
    )
      .then(() => {
        emit("verify-fail")
        closeDialog()
      })
      .catch(() => {
        // 取消关闭，重新打开弹窗
        emit("update:visible", true)
      })
  }

  // 关闭弹窗
  const closeDialog = () => {
    if (mediaStreamTrack.value) {
      mediaStreamTrack.value.getTracks()[0].stop()
    }
    emit("update:visible", false)
  }

  // 监听弹窗显示
  watch(
    () => props.visible,
    newVal => {
      if (newVal) {
        startCamera()
      }
    }
  )

  // 组件销毁时关闭摄像头
  onBeforeUnmount(() => {
    if (mediaStreamTrack.value) {
      mediaStreamTrack.value.getTracks()[0].stop()
    }
  })
</script>

<template>
  <el-dialog
    title="人脸识别"
    v-model="dialogVisible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :show-close="true"
    width="600px"
    append-to-body
    @close="handleClose"
  >
    <div class="face-verify-container">
      <video
        v-show="hasCamera"
        ref="cameraRef"
        class="camera-preview"
        muted
        autoplay
        width="300"
        height="200"
      />
      <canvas ref="canvasRef" width="300" height="200" style="display: none" />
      <div v-if="!hasCamera" class="no-camera-tip"> 未检测到摄像头设备 </div>
    </div>
    <template #footer>
      <el-button @click="verify" type="primary" :loading="loading"> 开始验证 </el-button>
    </template>
  </el-dialog>
</template>

<style lang="less" scoped>
  .face-verify-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;

    .camera-preview {
      border-radius: 4px;
      background: #f5f7fa;
    }

    .no-camera-tip {
      color: #f56c6c;
      font-size: 14px;
    }
  }
</style>
