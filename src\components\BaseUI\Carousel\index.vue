<script lang="ts" setup name="BaseCarousel">
  import type { BannerItem } from "@/types"
  import { onMounted, onUnmounted, ref } from "vue"
  import type { PropType } from "vue"

  export interface RelevantItem {
    id: string
    title: string
    img: string
    name: string
    desc: string
    picture: string
    discount?: string
    orderNum: number
  }

  // vue写法
  // const props = defineProps({
  //   slides: {
  //     type: Array as PropType<BannerItem[] | Array<Array<RelevantItem>>>,
  //     required: true,
  //   },
  //   autoPlay: {
  //     type: Boolean,
  //     default: false,
  //   },
  //   duration: {
  //     type: Number,
  //     default: 3000,
  //   },
  // });

  // ts写法
  const props = withDefaults(
    defineProps<{
      slides: BannerItem[] | RelevantItem[][]
      autoPlay?: boolean
      duration?: number
    }>(),
    {
      autoPlay: false,
      duration: 2500
    }
  )

  // 控制切换页
  let active = ref(0)
  const prev = () => {
    if (active.value <= 0) {
      active.value = props.slides.length - 1
    } else {
      active.value--
    }
  }

  const next = () => {
    if (active.value >= props.slides.length - 1) {
      active.value = 0
    } else {
      active.value++
    }
  }

  // 自动播放
  let timer = -1
  const play = () => {
    if (!props.autoPlay) return
    timer = window.setInterval(() => {
      next()
    }, props.duration)
  }

  const stop = () => {
    clearInterval(timer)
  }
  onMounted(() => {
    play()
  })
  onUnmounted(() => {
    stop()
  })
</script>

<template>
  <div class="base-carousel" @mouseenter="stop" @mouseleave="play">
    <ul class="carousel-body">
      <!-- 修改前 :key="item.id" -->
      <li
        class="carousel-item"
        :class="{ fade: active === index }"
        v-for="(item, index) in slides"
        :key="index"
      >
        <RouterLink v-if="!Array.isArray(item)" :to="item.hrefUrl">
          <img :src="item.imgUrl" alt="" />
        </RouterLink>
        <div v-else class="slider">
          <RouterLink v-for="products in item" :key="products.id" :to="`/products/${products.id}`">
            <img :src="products.img" alt="" />
            <p class="name ellipsis">{{ products.title }}</p>
          </RouterLink>
        </div>
      </li>
    </ul>
    <a href="javascript:;" class="carousel-btn prev" @click="prev"
      ><i class="iconfont icon-angle-left"></i
    ></a>
    <a href="javascript:;" class="carousel-btn next" @click="next"
      ><i class="iconfont icon-angle-right"></i
    ></a>
    <div class="carousel-indicator">
      <!-- 修改前 :key="item.id" -->
      <span
        @mouseenter="active = index"
        :class="{ active: active === index }"
        v-for="(item, index) in slides"
        :key="index + 999"
      ></span>
    </div>
  </div>
</template>

<style scoped lang="less">
  .base-carousel {
    width: 100%;
    height: 100%;
    min-width: 300px;
    min-height: 150px;
    position: relative;
    .carousel {
      &-body {
        width: 100%;
        height: 100%;
      }
      &-item {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        opacity: 0;
        transition: opacity 0.5s linear;
        &.fade {
          opacity: 1;
          z-index: 1;
        }
        img {
          width: 100%;
          height: 100%;
        }
        .slider {
          display: flex;
          justify-content: space-around;
          padding: 0 40px;
          > a {
            width: 240px;
            text-align: center;
            img {
              padding: 20px;
              width: 230px !important;
              height: 230px !important;
            }
            .name {
              font-size: 16px;
              color: #666;
              padding: 0 40px;
            }
            .enterprise {
              font-size: 16px;
              color: @redColor;
              margin-top: 15px;
            }
          }
        }
      }
      &-indicator {
        position: absolute;
        left: 0;
        bottom: 20px;
        z-index: 2;
        width: 100%;
        text-align: center;
        span {
          display: inline-block;
          width: 12px;
          height: 12px;
          background: rgba(0, 0, 0, 0.2);
          border-radius: 50%;
          cursor: pointer;
          ~ span {
            margin-left: 12px;
          }
          &.active {
            background: #fff;
          }
        }
      }
      &-btn {
        width: 44px;
        height: 44px;
        background: rgba(0, 0, 0, 0.2);
        color: #fff;
        border-radius: 50%;
        position: absolute;
        top: 228px;
        z-index: 2;
        text-align: center;
        line-height: 44px;
        opacity: 0;
        transition: all 0.5s;
        &.prev {
          left: 20px;
        }
        &.next {
          right: 20px;
        }
      }
    }
    &:hover {
      .carousel-btn {
        opacity: 1;
      }
    }
  }
</style>
