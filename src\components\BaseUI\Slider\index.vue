<!--
 * @Description: Slider轮播图
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-03-14 09:43:19
 * @LastEditTime: 2024-04-03 11:26:45
-->
<template>
  <div class="box">
    <div class="base-slider" @mouseenter="clearTimer" @mouseleave="startTimer">
      <!-- <div class="posi">
        <ul>
          <li v-for="(item, index) in PosiData" @click="filterCatalogue(item)">
            <div class="top">{{ item.label }}</div>
            <p class="bot" v-if="item.children">{{
              item.children.map(item2 => item2.label).join(" ")
            }}</p>
             <div class="submenu">
              <div class="submenu_title">{{ item.label }}</div>
              <ul>
                <li v-for="(subtitle, indey) in item.children">
                  {{ subtitle.label }}
                </li>
              </ul>
            </div> 
          </li>
        </ul>
      </div> -->
      <!-- 图片列表 -->
      <ul class="slider-body">
        <!--
          fade: 当fade类名存在 当前图片就显示 不存在就不显示
        -->
        <li
          class="slider-item"
          v-for="(item, i) in sliders"
          :key="i"
          :class="{ fade: curIndex === i }"
        >
          <RouterLink :to="item.linkUrlWeb" class="image">
            <img :src="item.img" alt="" />
          </RouterLink>
        </li>
      </ul>
      <!-- 圆圈切换按钮 -->
      <div class="slider-indicator">
        <span
          v-for="(item, index) in sliders"
          :key="index"
          @click="curIndex = index"
          :class="{ active: curIndex === index }"
        ></span>
      </div>
    </div>
    <!-- <sliderNews /> -->
  </div>
</template>

<script setup lang="ts">
  import { getCatalogueList } from "@/api/home"
  import { catalogueType } from "@/constants"
  /**
  目标：点击圆圈按钮 实现对应图片的切换
  思路：
    1. 图片和圆圈按钮数量是一样的 下标值是对应的
    2. 记录一下当前点击的是哪一项
    3. 需要根据记录下来的下标值 去配合:class 控制fade这个类名是否应该显示
 */

  /**
    目标：图片的自动轮播功能
    思路：哪个数据变化决定了图片切换？ 从之前手动修改curIndex的值 变成一个自动修改 每隔几秒修改一下
        计时器  setInterval

 */

  /**
    目标：鼠标移入暂停播放 鼠标移除再次开启
    思路：暂停 - 清除定时器  定时器id  开启 - 再执行一次定时器

 */
  import sliderNews from "./components/sliderNews.vue"
  import useTenantStore from "@/store/modules/tenant"

  interface Slider {
    img: string
    linkUrlWeb: string
  }

  const { domainName } = storeToRefs(useTenantStore())
  const props = defineProps({
    // 数据列表
    sliders: {
      type: Array as PropType<Slider[]>,
      default: () => []
    },
    autoPlay: {
      type: Boolean,
      default: true
    }
  })

  const router = useRouter()

  const curIndex = ref(0)
  // 声明一个存放定时器的数据
  const timer = ref(-1)
  function clearTimer() {
    window.clearInterval(timer.value)
  }
  function startTimer() {
    // 开启定时器  每隔2s中修改一下curIndex的值
    initLoop()
  }

  function initLoop() {
    if (!props.autoPlay) return false
    // 开启定时器  每隔2s中修改一下curIndex的值
    timer.value = window.setInterval(() => {
      // 最大能到多大
      // 图片总数为4：length - 1为3 只要我发现你大于3了
      // 我就会重新赋值为 0 ,永远不能到达4 最大只能等于3
      curIndex.value++
      if (curIndex.value > props.sliders.length - 1) {
        curIndex.value = 0
      }
    }, 2000)
  }
  onMounted(() => {
    initLoop()
  })
  onUnmounted(() => {
    // 清理工作
    clearInterval(timer.value)
  })

  const PosiData = ref()

  const fetchCatalogueData = async () => {
    let queryData = {
      catalogueType: catalogueType.COURSE_CATALOGUE,
      parentId: domainName.value === "taibao" ? "0" : "2054"
    }
    const { data } = await getCatalogueList(queryData)
    PosiData.value = data.slice(0, 6) || []
  }

  const filterCatalogue = item => {
    router.push({
      path: "/course",
      query: {
        catalogueId: item.id
      }
    })
  }

  fetchCatalogueData()
</script>

<style scoped lang="less">
  .box {
    border-radius: 10px;
  }
  .slider-indicator {
    text-align: right !important;
    padding-right: 50px;
  }

  .base-slider {
    border-radius: 10px;
    width: 100%;
    height: 100%;
    min-width: 500px;
    min-height: 400px;
    position: relative;
    box-shadow: 0 -3px 8px rgba(0, 0, 0, 0.2);
    .posi {
      border-radius: 10px 0 0 10px;
      height: 100%;
      left: 0;
      position: absolute;
      top: 0;
      width: 240px;
      z-index: 99;
      background-color: #39364d;

      > ul {
        display: flex;
        flex-direction: column;
        position: relative;
        width: 100%;
        height: 100%;
        padding: 20px 0;
        > li {
          flex: 1;
          color: #fff;
          cursor: pointer;
          list-style: none;
          padding: 1.1em 1.2em;
          width: 100%;

          .top {
            font-size: 14px;
            line-height: 1em;
            margin-bottom: 6px;
          }

          .bot {
            font-size: 0.75em;
            margin-right: 10px;
            opacity: 0.5;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            width: 100%;
          }

          .submenu {
            display: none;
            background-color: #fff;
            bottom: 0;
            left: 240px;
            padding: 30px 20px;
            position: absolute;
            top: 0;
            width: 526px;
            z-index: 1000;
            border-radius: 0 10px 0 0;
            .submenu_title {
              border-left: 4px solid #ff5a00;
              color: #333;
              font-size: 14px;
              height: 14px;
              line-height: 14px;
              margin-bottom: 24px;
              padding-left: 8px;
              font-weight: 700;
            }

            > ul {
              align-content: flex-start;
              display: flex;
              flex-wrap: wrap;
              > li {
                color: #666;
                font-size: 14px;
                height: auto;
                margin-bottom: 20px;
                margin-right: 40px;
              }

              > li:hover {
                color: #ff5a00;
                cursor: pointer;
              }
            }
          }
        }
        /* 
        > li:nth-child(1):hover {
          border-radius: 10px 0 0 0;
        } */
        > li:hover {
          background-color: rgba(255, 255, 255, 0.1);
          color: #fff;
        }

        > li:hover .submenu {
          display: block;
        }
      }
    }

    .slider {
      &-body {
        width: 100%;
        height: 100%;
      }
      &-item {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        opacity: 0;
        transition: opacity 0.5s linear;
        &.fade {
          opacity: 1;
          z-index: 1;
        }
        img {
          border-radius: 10px;
          width: 100%;
          height: 100%;
        }
      }
      &-indicator {
        position: absolute;
        left: 0;
        bottom: 20px;
        z-index: 2;
        width: 100%;
        text-align: center;
        span {
          display: inline-block;
          width: 10px;
          height: 10px;
          background: rgba(0, 0, 0, 0.2);
          border-radius: 50%;
          cursor: pointer;
          ~ span {
            margin-left: 12px;
          }
          &.active {
            background: #fff;
          }
        }
      }
      &-btn {
        width: 44px;
        height: 44px;
        background: rgba(0, 0, 0, 0.2);
        color: #fff;
        border-radius: 50%;
        position: absolute;
        top: 228px;
        z-index: 2;
        text-align: center;
        line-height: 44px;
        opacity: 0;
        transition: all 0.5s;
        &.prev {
          left: 20px;
        }
        &.next {
          right: 20px;
        }
      }
    }
    &:hover {
      .slider-btn {
        opacity: 1;
      }
    }
  }
</style>
