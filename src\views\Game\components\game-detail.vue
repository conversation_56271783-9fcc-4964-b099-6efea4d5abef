<!--
 * @Description: 游戏详情页
 * @Author: Assistant
 * @LastEditors: <PERSON>
 * @Date: 2025-01-27
 * @LastEditTime: 2025-06-06 15:16:54
-->
<script lang="ts" setup>
  import { getGame } from "@/api/game"
  import { ElMessage } from "element-plus"

  const route = useRoute()
  const router = useRouter()
  const gameInfo = ref<any>()
  const loading = ref(true)
  const gameLoading = ref(true)
  const gameError = ref(false)

  // 获取游戏详情
  async function fetchGameData() {
    loading.value = true
    try {
      if (!route.query.id) return
      const { data } = await getGame(route.query.id)
      gameInfo.value = data
    } catch (error) {
      console.error("获取游戏详情失败:", error)
      ElMessage.error("获取游戏详情失败")
    } finally {
      loading.value = false
    }
  }

  // 返回列表页面
  const handleClose = () => {
    router.push("/game")
  }

  // iframe加载完成
  const handleIframeLoad = () => {
    gameLoading.value = false
  }

  // iframe加载错误
  const handleIframeError = () => {
    gameLoading.value = false
    gameError.value = true
  }

  // 重试加载游戏
  const retryLoadGame = () => {
    gameError.value = false
    gameLoading.value = true
    // 重新加载iframe
    const iframe = document.getElementById("gameIframe") as HTMLIFrameElement
    if (iframe && gameInfo.value?.gameLink) {
      iframe.src = gameInfo.value.gameLink
    }
  }

  onMounted(() => {
    fetchGameData()
  })
</script>

<template>
  <div class="game-detail-container">
    <!-- 加载状态 -->
    <el-skeleton v-if="loading" :rows="5" animated>
      <template #template>
        <div class="w-full h-600px">
          <el-skeleton-item variant="image" class="w-full h-full" />
        </div>
      </template>
    </el-skeleton>

    <!-- 已加载完成的内容 -->
    <template v-else>
      <!-- 游戏未找到或已下架 -->
      <div v-if="!gameInfo || gameInfo?.status !== 1" class="game-tip">
        <div class="game-tip-inner">
          <div class="game-tip-info">
            {{ !gameInfo ? "游戏不存在" : "该游戏已下架，请联系管理员" }}
          </div>
          <div class="game-btn">
            <el-button type="primary" size="large" round @click="handleClose">
              返回游戏中心
            </el-button>
          </div>
        </div>
      </div>

      <!-- 正常游戏内容 -->
      <template v-else>
        <!-- 游戏主体区域 - 左右布局 -->
        <div class="game-main">
          <!-- 左侧：游戏信息 -->
          <div class="game-sidebar">
            <div class="game-info-card">
              <div class="game-cover">
                <img :src="gameInfo.gameCover" :alt="gameInfo.gameName" />
              </div>
              <div class="game-details">
                <h1 class="game-title">{{ gameInfo.gameName }}</h1>
                <div class="game-meta">
                  <span class="game-type">{{ gameInfo.gameType || "休闲游戏" }}</span>
                  <el-tag type="success" class="game-status">
                    {{ gameInfo.status === 1 ? "上架中" : "已下架" }}
                  </el-tag>
                </div>
                <div class="game-actions">
                  <el-button type="primary" @click="retryLoadGame" v-if="gameError">
                    重新加载
                  </el-button>
                  <el-button @click="handleClose"> 返回列表 </el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧：游戏区域 -->
          <div class="game-content">
            <!-- 游戏iframe容器 - 16:9宽高比 -->
            <div class="game-frame-container">
              <!-- 加载状态 -->
              <div v-if="gameLoading" class="game-loading">
                <el-icon class="is-loading"><loading /></el-icon>
                <span>游戏加载中...</span>
              </div>

              <!-- 错误状态 -->
              <div v-if="gameError" class="game-error">
                <el-icon><warning /></el-icon>
                <span>游戏加载失败，请重试</span>
                <el-button type="primary" size="small" @click="retryLoadGame">重试</el-button>
              </div>

              <!-- 游戏iframe -->
              <iframe
                v-show="!gameLoading && !gameError"
                id="gameIframe"
                :src="gameInfo.gameLink"
                frameborder="0"
                class="game-iframe"
                scrolling="auto"
                allowfullscreen
                allow="fullscreen; autoplay; encrypted-media; gyroscope; picture-in-picture"
                @load="handleIframeLoad"
                @error="handleIframeError"
              />
            </div>
          </div>
        </div>
      </template>
    </template>
  </div>
</template>

<style lang="less" scoped>
  .game-detail-container {
    height: calc(100vh); /* 减去header(70px) + footer(约80px) */
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
    box-sizing: border-box;
    overflow: hidden;
  }

  .game-tip {
    height: 500px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;

    .game-tip-inner {
      text-align: center;
      color: #666;

      .game-tip-info {
        margin-bottom: 24px;
        font-size: 18px;
      }
    }
  }

  .game-main {
    display: flex;
    gap: 20px;
    height: 100%;
  }

  .game-sidebar {
    width: 380px;
    flex-shrink: 0;

    .game-info-card {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
      height: fit-content;

      .game-cover {
        width: 100%;
        height: 260px;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        margin-bottom: 16px;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .game-details {
        .game-title {
          font-size: 20px;
          font-weight: bold;
          color: #333;
          margin: 0 0 12px 0;
          line-height: 1.2;
        }

        .game-meta {
          display: flex;
          flex-direction: column;
          gap: 8px;
          margin-bottom: 20px;

          .game-type {
            background: #e6f7ff;
            color: #0958d9;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 14px;
            font-weight: 500;
            text-align: center;
            width: fit-content;
          }

          .game-status {
            width: fit-content;
          }
        }

        .game-actions {
          display: flex;
          flex-direction: column;
          gap: 12px;

          .el-button {
            width: 100%;
          }
        }
      }
    }
  }

  .game-content {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;

    .game-frame-container {
      position: relative;
      width: 100%;
      flex: 1;
      background: #fff;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

      .game-loading,
      .game-error {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background: #f8f9fa;
        gap: 16px;
        z-index: 10;

        .el-icon {
          font-size: 48px;
        }

        span {
          font-size: 16px;
          color: #666;
        }
      }

      .game-loading .el-icon {
        color: #409eff;
      }

      .game-error .el-icon {
        color: #f56c6c;
      }

      .game-iframe {
        width: 100%;
        height: 100%;
        border: none;
        background: #fff;
        display: block;
      }
    }
  }

  // 响应式设计
  @media (max-width: 1024px) {
    .game-main {
      height: calc(100vh - 70px - 80px - 30px);
      max-height: calc(100vh - 70px - 80px - 30px);
      min-height: 500px;
    }

    .game-sidebar {
      width: 280px;
    }
  }

  @media (max-width: 768px) {
    .game-detail-container {
      padding: 12px;
    }

    .game-main {
      flex-direction: column;
      height: auto;
      max-height: none;
      gap: 16px;
    }

    .game-sidebar {
      width: 100%;

      .game-info-card {
        padding: 16px;

        .game-cover {
          height: 150px;
          margin-bottom: 16px;
        }

        .game-details {
          .game-title {
            font-size: 20px;
          }

          .game-actions {
            flex-direction: row;
            gap: 8px;

            .el-button {
              width: auto;
              flex: 1;
            }
          }
        }
      }
    }

    .game-content {
      height: 450px; /* 移动端固定高度 */

      .game-frame-container {
        height: 100%;
      }
    }
  }

  @media (max-width: 480px) {
    .game-sidebar .game-info-card {
      .game-details .game-actions {
        flex-direction: column;

        .el-button {
          width: 100%;
        }
      }
    }
  }

  // 骨架屏样式
  :deep(.el-skeleton) {
    .el-skeleton__item {
      border-radius: 12px;
    }
  }
</style>
