<!--
 * @Description: 北蔡防灾减灾首页
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-12-07 13:24:30
 * @LastEditTime: 2025-02-10 11:48:52
-->
<script lang="ts" setup>
  import { getTownExamInfo } from "@/api/onlineExam"
  import { ElMessage } from "element-plus"
  import type { examPaperAnswerType } from "@/types/study"

  const router = useRouter()
  const startLearn = () => {
    router.push("/safetyCourse")
  }
  const exerciseData = ref<examPaperAnswerType>()
  const examData = ref<examPaperAnswerType>()
  const startExam = flag => {
    if (flag === "exercise") {
      if (!exerciseData.value || !exerciseData.value.baseId) {
        return ElMessage({ message: "暂无可用考试", type: "warning" })
      }
      router.push({
        path: "/prepare",
        query: {
          baseId: exerciseData.value.baseId,
          arrangeId: exerciseData.value.arrangeId,
          taskId: 1
        }
      })
    } else if (flag === "exam") {
      if (!examData.value || !examData.value.baseId) {
        return ElMessage({ message: "暂无可用考试", type: "warning" })
      }
      router.push({
        path: "/prepare",
        query: {
          baseId: examData.value.baseId,
          arrangeId: examData.value.arrangeId,
          taskId: 1
        }
      })
    } else {
      router.push({
        path: `/study/townReview`
      })
    }
  }
  const fetchData = async () => {
    const { data } = await getTownExamInfo()
    exerciseData.value = data.find(item => item.baseId == "2066")
    examData.value = data.find(item => item.baseId == "2057")
  }

  fetchData()
</script>
<template>
  <div class="prevent-disaster-home">
    <h2 class="title">
      <img src="@/assets/images/safety.png" alt="" />
    </h2>
    <div class="disaster-content">
      <div class="activity-rule">
        安全是什么？对于一个人，安全意味着健康。对于一个家庭，安全意味着和睦。对于一个企业，安全意味着发展。对于一个国家，安全意味着强大。对于海湾地区，安全意味着什么？是生命！古语道：“千里之堤，溃于蚁穴”，意思是说虽然是小问题，却有可能导致全局的失败。如果我们把整个集团的经营发展比做千里之堤，那么出现的安全问题就是那小小的蚁穴，安全工作做不好，一切工作都将毫无意义。
      </div>
    </div>

    <div class="bottom-btn">
      <div style="background-color: #f59a23" @click="startLearn">学习</div>
      <div style="background-color: #0000bf" @click="startExam('exercise')"> 做题 </div>
      <div style="background-color: #75affd" @click="startExam('exam')"> 考试 </div>
      <div style="background-color: #00bfbf" @click="startExam('review')"> 回顾 </div>
    </div>
  </div>
</template>

<style scoped lang="less">
  .prevent-disaster-home {
    padding: 30px 0 45px 0;
    width: 1240px;
    margin: 0 auto;
  }
  .title {
    color: #f59a23;
    font-size: 50px;
    font-weight: bolder;
    line-height: 1;
    margin-bottom: 30px;
    text-align: center;
    text-shadow: 4px 4px 4px #333333;
  }

  .disaster-content {
    width: 1240px;
    background-color: #fff;
    padding: 50px 80px;
    line-height: 30px;
    font-size: 15px;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
  }

  .bottom-btn {
    margin-top: 46px;
    display: flex;
    font-size: 24px;
    font-weight: bold;
    justify-content: space-around;
    align-items: center;
    flex-wrap: wrap;
    div {
      margin: 30px 80px 0 80px;
      height: 60px;
      width: 25%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      color: #fff;
    }
  }
</style>
