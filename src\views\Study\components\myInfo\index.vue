<!--
 * @Description: 个人信息页面
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-16 11:12:41
 * @LastEditTime: 2024-11-29 14:14:10
-->
<script setup lang="ts">
  import useUserStore from "@/store/modules/user"
  import { editInfo } from "@/api/system/user"
  import { ElMessage } from "element-plus"
  import FaceRecognition from "./components/faceRecognition.vue"
  import ChangePwd from "@/views/Study/components/myInfo/components/changePwd.vue"

  let userStore = useUserStore()
  const { userInfo } = storeToRefs(userStore)

  const dialogVisible = ref(false)

  async function saveInfo() {
    let queryData = {
      nickName: userInfo.value.nickName,
      sex: userInfo.value.sex,
      phonenumber: userInfo.value.phonenumber,
      email: userInfo.value.email,
      avatar: userInfo.value.avatar,
      userName: userInfo.value.userName,
      userId: userInfo.value.userId
    }
    let res = await editInfo(queryData)
    if (res.code == 200) {
      ElMessage({
        message: "修改成功！",
        type: "success"
      })
      userStore.getInfo()
    }
  }

  // 修改密码
  const changePwdRef = ref()
  const changePassword = () => {
    changePwdRef.value.openDialog()
    dialogVisible.value = !dialogVisible.value
  }
</script>

<template>
  <div class="myInfo">
    <el-form label-position="left" label-width="68px" :model="userInfo">
      <el-row :gutter="50">
        <el-col :span="10">
          <el-form-item label="账号">
            <el-input v-model="userInfo.userName" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="昵称">
            <el-input v-model="userInfo.nickName" />
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="手机">
            <el-input
              v-model="userInfo.phonenumber"
              maxlength="11"
              @input="v => (userInfo.phonenumber = v.replace(/[^\d.]/g, ''))"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="密码">
            <el-input :value="12312312312" maxlength="11" disabled type="password" />
            <span class="edit_password_text" @click="changePassword">修改密码</span>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="邮箱">
            <el-input v-model="userInfo.email" />
          </el-form-item>
        </el-col>
        <el-col :span="10" v-if="userInfo.dept">
          <el-form-item label="所属部门">
            <el-input v-model="userInfo.dept.deptName" disabled></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="头像">
            <BaseImageUpload class="avatar" v-model="userInfo.avatar" :limit="1" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="人脸识别">
            <FaceRecognition />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item>
            <el-button type="primary" @click="saveInfo" style="width: 100%"> 保存 </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <ChangePwd ref="changePwdRef" />
  </div>
</template>

<style scoped lang="less">
  .myInfo {
    padding: 20px 0;
  }
  .edit_password_text {
    position: absolute;
    right: -65px;
    color: #0f75e7;
    cursor: pointer;
  }

  .recognition-container {
    .recognition-area {
      > img {
        border: 1px solid #dcdfe6;
        border-radius: 5px;
      }
      .face-recognition {
        width: 100px;
        height: 100px;
      }

      .sampling-text {
        position: absolute;
        top: 35px;
        left: 115px;
        font-size: 16px;
        color: #0f75e7;
        cursor: pointer;
      }
    }

    .tips {
      margin-top: 10px;
      > p {
        color: #9e9e9e;
      }
    }
  }

  // el-upload相关样式
  :deep(.el-upload-list__item-actions) {
    height: 100px;
    width: 100px;
  }
  :deep(.el-upload--picture-card) {
    width: 100px;
    height: 100px;
  }
  :deep(.el-upload) {
    width: 100px;
    height: 100px;
    line-height: 100px;
  }
  :deep(.el-upload-list--picture-card .el-upload-list__item) {
    width: 100px;
    height: 100px;
  }
  :deep(.el-upload-list--picture-card .el-upload-list__item-thumbnail) {
    width: 100px;
    height: 100px;
  }

  :deep(.el-upload__tip) {
    margin-top: -5px;
  }
</style>
