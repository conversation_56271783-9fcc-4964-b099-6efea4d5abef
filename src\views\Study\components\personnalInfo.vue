<!--
 * @Description: 个人信息
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-08-29 15:23:11
 * @LastEditTime: 2024-12-05 15:53:09
-->
<template>
  <div
    class="h-180px bg-no-repeat bg-cover bg-center flex text-#131415 items-center w-full"
    style="
      background-image: url('https://training-voc.obs.cn-north-4.myhuaweicloud.com/personal-bg.png');
    "
  >
    <div class="ml-50px flex items-center">
      <div class="w-100px h-100px">
        <img :src="avatar" alt="" class="rounded-full" />
      </div>
      <div class="ml-20px">
        <div class="mb-10px text-26px font-bold flex gap5 items-center">
          <span>{{ displayName }}</span>
          <el-button @click="handleSignIn" class="rounded-xs bg-[var(--warmOrange)] text-white">
            {{ extraInfo?.signed ? "已签到" : "签到" }}
          </el-button>
        </div>
        <div class="text-18px">部门：{{ userInfo.dept?.deptName }}</div>
      </div>
    </div>

    <div class="flex flex-col ml-300px">
      <div class="flex justify-center items-center rounded-10px gap20">
        <template v-for="(item, index) in personnalScoreList" :key="index">
          <div class="w-150px h-60px flex justify-center items-center flex-col">
            <div class="font-bold flex items-center">
              <span class="text-30px">
                {{
                  item.processFn
                    ? item.processFn(extraInfo?.[item.value] || 0)
                    : extraInfo?.[item.value] || 0
                }}
              </span>
              <span class="ml-5px text-24px">{{ item.unit }}</span>
            </div>
            <div class="text-16px">{{ item.label }}</div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import useStore from "@/store"
  import { storeToRefs } from "pinia"
  import { getDisplayName } from "@/utils/common"

  const emit = defineEmits(["signin"])
  const props = defineProps({
    extraInfo: {
      type: Object,
      default: () => ({})
    }
  })

  const personnalScoreList = [
    { value: "credit", unit: "分", label: "获得学分" },
    {
      value: "classHour",
      unit: "小时",
      label: "完成学时",
      processFn: (value: number) => {
        return Math.floor(Number(value) / 60 / 60)
      }
    },
    { value: "integral", unit: "分", label: "获得积分" }
  ]

  const { user } = useStore()
  const { nickName, userInfo, avatar } = storeToRefs(user)

  const displayName = computed(() => {
    return getDisplayName(nickName.value, userInfo.value?.userName)
  })

  const handleSignIn = () => {
    if (props.extraInfo?.signed) return
    emit("signin")
  }
</script>
