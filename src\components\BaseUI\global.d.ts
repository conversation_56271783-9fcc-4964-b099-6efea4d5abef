/*
 * @Description:
 * @Author: <PERSON><PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-04-17 08:49:10
 * @LastEditTime: 2024-11-13 14:54:23
 */
// 自动导入所有组件
const components = import.meta.glob("./**/index.vue", { eager: true })
const otherComponents = import.meta.glob("./**/*.vue", { eager: true })

// 全局组件类型声明
declare module "vue" {
  export interface GlobalComponents {
    [key: string]: any // 由于是动态导入，这里使用索引签名
  }
}

export {}
