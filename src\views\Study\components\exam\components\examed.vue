<script setup lang="ts">
  import { useRoute, useRouter } from "vue-router"
  import { ref, getCurrentInstance } from "vue"
  import type {
    completedPaperType,
    paperTacticsType,
    ExamQuestionsType,
    ExamQuestionItem
  } from "@/types"
  import { completedPaperDetail } from "@/api/onlineExam"
  import { noNeedExamScoreDomainList } from "@/utils/constant"
  import useTenantStore from "@/store/modules/tenant"

  const { proxy } = getCurrentInstance()!
  const { exam_question_type } = proxy?.useDict("exam_question_type")!
  const { domainName } = storeToRefs(useTenantStore())
  const route = useRoute()
  const router = useRouter()
  const { baseId, paperAnswerId, arrangeId } = route.query
  const completedPaperInfo = ref<completedPaperType>()
  const paperTacticsList = ref<paperTacticsType>()
  const examQuestionList = ref<ExamQuestionsType>([])

  const loadData = async () => {
    let queryData = {
      baseId,
      arrangeId,
      paperAnswerId
    }
    const { data } = await completedPaperDetail(queryData)
    completedPaperInfo.value = data
    paperTacticsList.value = data.paperTactics
    paperTacticsList.value?.forEach(item => {
      item?.examQuestions.forEach((question: ExamQuestionItem) => {
        examQuestionList.value.push(question)
      })
    })
    examQuestionList.value.forEach(item => {
      if (item.questionType === "F") {
        item.fillAnswerList = []
        if (item.userAnswer && item.userAnswer.includes("::")) {
          item.fillAnswerList = item.userAnswer.split("::") || []
        }
      }
    })
  }

  // 点击左侧数字平滑移动到某一题位置
  const scrollToQuestion = (index: number) => {
    const questionEl = proxy?.$refs[`questionBox${index}`]![0]
    if (questionEl) {
      const headerOffset = 10 // 顶部偏移量
      const elementPosition = questionEl.getBoundingClientRect().top
      const offsetPosition = elementPosition + window.pageYOffset - headerOffset

      window.scrollTo({
        top: offsetPosition,
        behavior: "smooth"
      })
    }
  }

  const goBack = () => {
    router.go(-1)
  }

  // 正确的选项
  function isCorrect(item: ExamQuestionItem, choice: string): boolean {
    if (item.questionType === "S" || item.questionType === "J") {
      return item.answer === choice
    } else if (item.questionType === "M") {
      if (item.answer.includes(",")) {
        return item.answer.split(",").indexOf(choice) > -1
      }
      return item.answer === choice
    }
    return false
  }

  // 错误的选项
  function isWrong(item: ExamQuestionItem, choice: string): boolean {
    if (!item.userAnswer) return false
    if (item.questionType === "S" || item.questionType === "J") {
      return item.userAnswer === choice && item.userAnswer !== item.answer
    } else if (item.questionType === "M") {
      return item.userAnswer.includes(choice) && !item.answer.includes(choice)
    }
    return false
  }

  onMounted(() => {
    loadData()
  })
</script>

<template>
  <div class="w-[90%] p10 mx-auto">
    <!-- 标题区域 -->
    <div class="text-24px flex items-center cursor-pointer" @click="goBack">
      <el-icon class="mr-20px"><ArrowLeftBold /></el-icon>
      <span>{{ completedPaperInfo?.paperName }}</span>
    </div>

    <!-- 主要内容区域 -->
    <div class="mt-20px flex justify-between">
      <!-- 左侧试题区域 -->
      <div class="w-[70%] bg-white p-20px">
        <div
          v-for="(item, index) in examQuestionList"
          :key="index"
          :ref="`questionBox${index}`"
          class="pb-20px mt-10px border-b border-b-dotted border-[#cfd0d4]"
        >
          <!-- 题目标题 -->
          <div class="flex items-center">
            <div class="flex items-center text-[#477cda] text-16px">
              <div class="mr-10px">{{ index + 1 }}</div>
              [<dict-tag :options="exam_question_type" :value="item.questionType" />]
            </div>
            <div
              v-if="!noNeedExamScoreDomainList.includes(domainName)"
              class="ml-10px bg-[#4479da] text-white px-8px rounded-5px"
            >
              {{ item.questionScore }}分
            </div>
          </div>

          <!-- 题目内容 -->
          <div class="mt-20px text-18px">{{ item.questionName }}</div>

          <!-- 选择题选项 -->
          <div v-if="item.questionType === 'S' || item.questionType === 'M' || item.questionType === 'J'" class="mt-20px">
            <template v-for="choice in 11" :key="choice">
              <div
                v-if="item[`item${choice}`]"
                class="bg-[#f0f5fe] mb-10px h-40px flex items-center px-10px"
              >
                <div class="flex items-center">
                  <div class="w-24px h-24px flex-shrink-0 flex items-center justify-center">
                    <img
                      v-if="isCorrect(item, String.fromCharCode(64 + choice))"
                      src="@/assets/images/correct.png"
                      class="w-[95%] h-[95%]"
                    />
                    <img
                      v-else-if="isWrong(item, String.fromCharCode(64 + choice))"
                      src="@/assets/images/error.png"
                      class="w-[95%] h-[95%]"
                    />
                    <span
                      v-else
                      class="w-22px h-22px border border-gray-300 rounded-full flex items-center justify-center"
                    >
                      {{ String.fromCharCode(64 + choice) }}
                    </span>
                  </div>
                  <div class="pl-10px text-16px">{{ item[`item${choice}`] }}</div>
                </div>
              </div>
            </template>
          </div>

          <!-- 填空题 -->
          <div v-else-if="item.questionType === 'F'" class="space-y-20px">
            <div v-for="blankChoice in item.blankCount" class="flex items-center">
              <div class="w-90px">填空{{ blankChoice }}：</div>
              <el-input v-model="item.fillAnswerList[blankChoice - 1]" disabled class="w-200px" />
              <img
                class="w-24px h-24px ml-20px"
                :src="
                  item.fillAnswerList[blankChoice - 1] === item.answer.split('::')[blankChoice - 1]
                    ? '@/assets/images/correct.png'
                    : '@/assets/images/error.png'
                "
              />
              <div
                v-if="
                  item.fillAnswerList[blankChoice - 1] !== item.answer.split('::')[blankChoice - 1]
                "
                class="ml-20px"
              >
                正确答案：{{ item.answer.split("::")[blankChoice - 1] }}
              </div>
            </div>
          </div>

          <!-- 简答题 -->
          <div v-else-if="item.questionType === 'Q'" class="mt-20px">
            <div class="mb-10px">
              <span class="font-bold text-gray-700">你的答案：</span>
            </div>
            <div class="bg-[#f0f5fe] p-15px rounded-5px mb-10px min-h-100px text-16px leading-relaxed">
              {{ item.userAnswer || "未作答" }}
            </div>
            <div class="mb-10px">
              <span class="font-bold text-gray-700">参考答案：</span>
            </div>
            <div class="bg-[#f0f5fe] p-15px rounded-5px min-h-100px text-16px leading-relaxed">
              {{ item.answer || "暂无参考答案" }}
            </div>
          </div>

          <!-- 答案分析区域 -->
          <div class="mt-20px">
            <div class="flex items-center">
              <div
                class="rounded-5px px-10px py-5px text-white mr-30px"
                :class="item.userAnswer === item.answer ? 'bg-[#4479da]' : 'bg-[#e1483f]'"
              >
                {{ item.userAnswer === item.answer ? "回答正确" : "回答错误" }}
              </div>

              <div class="flex items-center mr-30px">
                <div class="w-4px h-18px rounded-2px bg-[#04c877] mr-10px"></div>
                <span>答案：{{ item.answer }}</span>
              </div>

              <div
                v-if="item.answer !== item.userAnswer && item.questionType !== 'F'"
                class="flex items-center mr-30px"
              >
                <div class="w-4px h-18px rounded-2px bg-[#ff4d4f] mr-10px"></div>
                <span>
                  我的答案：
                  {{
                    item.userAnswer
                      ? Array.isArray(item.userAnswer)
                        ? item.userAnswer?.sort().join()
                        : item.userAnswer
                      : "未选择"
                  }}
                </span>
              </div>

              <div v-if="!noNeedExamScoreDomainList.includes(domainName)" class="flex items-center">
                <div class="w-4px h-18px rounded-2px bg-[#3ca7fa] mr-10px"></div>
                <span>得分：{{ item.userQuestionScore || 0 }}</span>
              </div>
            </div>
          </div>

          <!-- 解析 -->
          <div v-if="item.analysis" class="text-16px mt-10px mb-20px text-red-500">
            解析：{{ item.analysis }}
          </div>
        </div>
      </div>

      <!-- 右侧答题卡区域 -->
      <div class="w-[28%] relative">
        <!-- 分数展示 -->
        <div
          v-if="!noNeedExamScoreDomainList.includes(domainName)"
          class="bg-gradient-to-br from-[#8abdec] to-[#4479da] text-22px text-white text-center h-120px leading-120px rounded-10px"
        >
          考试得分：
          <span class="text-32px">
            <span class="text-white">{{ completedPaperInfo?.userPaperScore }}分</span>
            /{{ completedPaperInfo?.paperScore }}分
          </span>
        </div>

        <!-- 答题卡 -->
        <div class="sticky top-100px w-full mt-20px bg-white rounded-10px p-20px min-h-500px">
          <div class="text-24px pb-20px border-b border-b-dotted border-[#cfd0d4]">答题卡</div>
          <div class="mt-20px grid grid-cols-5 gap-30px">
            <div
              v-for="(item, index) in examQuestionList"
              :key="index"
              class="h-50px leading-50px text-center text-white rounded-5px cursor-pointer"
              :class="[
                !item.userAnswer
                  ? 'bg-[#a8a8a8]'
                  : item.userAnswer === item.answer
                  ? 'bg-[#4479da]'
                  : 'bg-[#e1483f]'
              ]"
              @click="scrollToQuestion(index)"
            >
              {{ index + 1 }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
