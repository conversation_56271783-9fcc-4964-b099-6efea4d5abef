<script setup lang="ts">
import useStore from "@/store";
//获取轮播图图片
const { singleRichText } = useStore();
singleRichText.getSingleRichText("linkUs");
</script>

<template>
  <div class="linkus">
    <h3>我要发布</h3>
    <div class="base-form">
      <div class="base-form-item">
        <div class="field">
          <i class="icon iconfont icon-user"></i>
          <input class="input" type="text" placeholder="请输入姓名" />
        </div>
        <div class="error"></div>
      </div>
      <div class="base-form-item">
        <div class="field">
          <i class="icon iconfont icon-phone"></i>
          <input class="input" type="text" placeholder="请输入手机号" />
        </div>
        <div class="error"></div>
      </div>

      <div class="base-form-item">
        <div class="field">
          <textarea cols="60" rows="10" class="textarea">
请输入发布内容</textarea
          >
        </div>
        <div class="error"></div>
      </div>

      <br />
      <br />
      <br />
      <br />
      <br />
      <br />

      <!-- <div class="base-form-item">
        <div class="field">
          <BaseCheckBox v-model="isAgree">求购</BaseCheckBox>
          <BaseCheckBox v-model="isAgree">供应</BaseCheckBox>
          <BaseCheckBox v-model="isAgree">企业登录</BaseCheckBox>
          <BaseCheckBox v-model="isAgree">个人登录</BaseCheckBox>
          <BaseCheckBox v-model="isAgree">司机</BaseCheckBox>
        </div>
        <div class="error"></div>
      </div> -->
      <a href="javascript:;" class="submit">立即提交</a>
    </div>
  </div>
</template>

<style scoped lang="less">
.linkus {
  width: 940px;
  background: #fff;
  height: 600px;
  h3 {
    height: 70px;
    line-height: 50px;
    border-bottom: 1px solid #f5f5f5;
    padding-left: 50px;
    font-size: 18px;
    font-weight: 400;
    margin-bottom: 10px;
    text-align: center;
  }
  .linkus-article {
    margin-top: 20px;
    padding-bottom: 40px;
  }
}

.code {
  position: absolute;
  right: 0;
  top: 0;
  line-height: 50px;
  width: 80px;
  color: #999;
  &:hover {
    cursor: pointer;
  }
}
.base-form {
  padding: 0;
}
.base-form-item .field {
  margin-left: 23px;
}
.action {
  padding: 20px 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .url {
    a {
      color: #999;
      margin-left: 10px;
    }
  }
}
.textarea {
  border: 1px solid #e4e4e4;
  width: 500px;
  color: #c8c9cc;
  padding: 10px;
}
</style>
